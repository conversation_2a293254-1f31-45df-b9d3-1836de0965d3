package com.xyy.saas.inquiry.pojo.inquiry;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.validation.InEnum;
import cn.iocoder.yudao.framework.dict.core.DictFrameworkUtils;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.constant.PrescriptionConstant;
import com.xyy.saas.inquiry.constant.TenantPackageConstant;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * 问诊套餐item
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/04 14:06
 */
@Data
@Schema(description = "门店问诊套餐item")
@Valid
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryPackageItem implements Serializable {

    /**
     * {@link InquiryWayTypeEnum}
     */
    @Schema(description = "问诊类型", example = "1")
    @InEnum(value = InquiryWayTypeEnum.class, message = "问诊类型必须是 {value}")
    private Integer inquiryWayType;

    @Schema(description = "额度数量", example = "100")
    @Min(value = 0)
    private Long count;

    @Schema(description = "是否不限", example = "false")
    private boolean unlimited;

    // === 新字段（支持嵌套结构） ===

    @Schema(description = "值数组", example = "[0,1]")
    private List<Integer> prescriptionValue;

    @Schema(description = "医院编码", example = "[\"hospital001\"]")
    private List<String> hospitalPref;

    @JsonIgnore
    private String hospitalName;

    @Schema(description = "嵌套的问诊项目明细")
    private List<InquiryPackageItems> items;

    public Long getCount() {
        return count == null ? 0L : count;
    }

    /**
     * 判断是否为新格式 新格式的特征：有 type 字段且有 items 列表
     *
     * @return true 如果是新格式
     */
    @JSONField(serialize = false) // 忽略此字段的序列化
    @JsonIgnore
    public boolean hasItems() {
        return CollUtil.isNotEmpty(items);
    }

    /**
     * 获取有效的问诊项目列表 如果是新格式，返回 items 转换后的列表；否则返回自身的副本
     *
     * @return 问诊项目列表
     */
    @JSONField(serialize = false)
    @JsonIgnore
    public List<InquiryPackageItem> effectiveItems() {
        if (hasItems()) {
            // 新格式：将 InquiryPackageItems 转换为 InquiryPackageItem
            return items.stream()
                .map(i -> i.toInquiryPackageItem().setHospitalPref(getHospitalPref()).setPrescriptionValue(getPrescriptionValue()))
                .collect(Collectors.toList());
        } else {
            // 修复：返回当前对象的副本，避免循环引用
            InquiryPackageItem copy = InquiryPackageItem.builder()
                .inquiryWayType(this.inquiryWayType)
                .count(this.count)
                .unlimited(this.unlimited)
                .prescriptionValue(this.prescriptionValue != null ? new ArrayList<>(this.prescriptionValue) : null)
                .hospitalPref(this.hospitalPref != null ? new ArrayList<>(this.hospitalPref) : null)
                // 注意：不复制items字段，避免循环引用
                .build();
            return Arrays.asList(copy);
        }
    }

    /**
     * 检查是否有可用额度（兼容新旧格式和unlimited字段） 修复TenantPackageEffectiveStatusEnum.getEffectiveStatus中的额度判断bug
     *
     * @return true 如果有可用额度
     */
    @JSONField(serialize = false)
    @JsonIgnore
    public boolean hasAvailableQuota() {
        // 优先检查unlimited标识
        if (this.unlimited) {
            return true;
        }

        // 检查count是否为无限额度标识(-1)
        if (TenantPackageConstant.isUnlimitedCost(this.getCount())) {
            return true;
        }

        // 检查count是否大于0
        if (this.getCount() > 0) {
            return true;
        }

        // 如果是新格式，递归检查items
        if (hasItems()) {
            return items.stream().anyMatch(item ->
                item.isUnlimited() ||
                    TenantPackageConstant.isUnlimitedCost(item.getCount()) ||
                    item.getCount() > 0
            );
        }

        return false;
    }

    /**
     * 转换为新格式（用于迁移兼容） 将旧格式的 InquiryPackageItem 列表转换为新格式
     *
     * @param oldItems 旧格式的项目列表
     * @return 新格式的项目列表
     */
    public static List<InquiryPackageItem> convertToNewFormat(Integer inquiryBizType, List<InquiryPackageItem> oldItems, List<String> hospitalPrefs, boolean... fillWayType) {
        if (CollUtil.isEmpty(oldItems)) {
            return new ArrayList<>();
        }

        // 检查是否已经是新格式
        if (!Objects.equals(inquiryBizType, InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode())) {
            return oldItems; // 已经是新格式，直接返回
        }

        if (oldItems.stream().anyMatch(InquiryPackageItem::hasItems)) {
            for (InquiryPackageItem oldItem : oldItems) {
                // 填充其他的问诊类型出参
                if (fillWayType.length > 0 && fillWayType[0]) {
                    fillInquiryWay(oldItem.items);
                }
            }
            return oldItems;
        }
        // 将旧格式转换为新格式 - 修复：创建新的项目明细避免循环引用
        List<InquiryPackageItems> newItems = oldItems.stream()
            .map(oldItem -> InquiryPackageItems.builder()
                .inquiryWayType(oldItem.getInquiryWayType())
                .count(oldItem.getCount())
                // .checked(true)
                .unlimited(oldItem.isUnlimited())
                .build())
            .collect(Collectors.toList());
        // 填充其他的问诊类型出参
        if (fillWayType.length > 0 && fillWayType[0]) {
            fillInquiryWay(newItems);
        }

        // 创建包装的新格式项目
        InquiryPackageItem newItem = InquiryPackageItem.builder()
            .prescriptionValue(List.of())
            .hospitalPref(hospitalPrefs)
            .items(newItems) // 使用新的 InquiryPackageItems 类型
            .build();

        return Arrays.asList(newItem);
    }

    private static void fillInquiryWay(List<InquiryPackageItems> newItems) {

        Set<Integer> existingCodes = newItems.stream()
            .map(InquiryPackageItems::getInquiryWayType).collect(Collectors.toSet());

        List<Integer> fillCodes = List.of(InquiryWayTypeEnum.TEXT.getCode(), InquiryWayTypeEnum.VIDEO.getCode()).stream()
            .filter(code -> !existingCodes.contains(code)).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(fillCodes)) {
            newItems.addAll(fillCodes.stream()
                .map(code -> InquiryPackageItems.builder()
                    .inquiryWayType(code)
                    .count(0L)
                    .checked(false)
                    .build())
                .toList());
        }

    }


    /**
     * 从 InquiryPackageItem 列表中提取 hospitalPref 用于写入 saas_tenant_package_relation 表时的兼容处理
     *
     * @param items 问诊套餐项目列表
     * @return 医院编码列表
     */
    public static List<String> extractHospitalPrefs(List<InquiryPackageItem> items) {
        if (CollUtil.isEmpty(items)) {
            return new ArrayList<>();
        }

        // 优先从新格式中提取
        for (InquiryPackageItem item : items) {
            if (item.hasItems() && CollUtil.isNotEmpty(item.getHospitalPref())) {
                return new ArrayList<>(item.getHospitalPref());
            }
        }

        // 如果没有新格式，从旧格式中提取
        for (InquiryPackageItem item : items) {
            if (CollUtil.isNotEmpty(item.getHospitalPref())) {
                return new ArrayList<>(item.getHospitalPref());
            }
        }

        return new ArrayList<>();
    }

    public static String getInquiryPackageItemStr(Integer inquiryBizType, List<InquiryPackageItem> packageItemList) {
        if (CollUtil.isEmpty(packageItemList)) {
            return null;
        }

        return convertToNewFormat(inquiryBizType, packageItemList, null).stream()
            .sorted(Comparator.comparing(item ->
                CollUtil.isEmpty(item.getPrescriptionValue()) ? Integer.MAX_VALUE :
                Collections.min(item.getPrescriptionValue())))
            .map(InquiryPackageItem::formatSingleItem)
            .collect(Collectors.joining(";\r\n"));
    }

    /**
     * 格式化单个问诊套餐项目 格式：【处方类型】-【问诊方式】【额度】，【问诊方式】【额度】 例如：门诊统筹处方-图文200，视频100；非统筹处方-图文不限
     */
    private static String formatSingleItem(InquiryPackageItem item) {
        if (item.hasItems()) {
            // 新格式：按分组显示
            StringBuilder sb = new StringBuilder();

            // 获取处方类型描述
            String prescriptionTypeDesc = getPrescriptionTypeDesc(PrescriptionConstant.PRESCRIPTION_TYPE, item.getPrescriptionValue());
            if (StringUtils.isNotBlank(prescriptionTypeDesc)) {
                sb.append(prescriptionTypeDesc).append("-");
            }

            // 格式化问诊方式和额度
            String itemsStr = item.getItems().stream()
                .filter(InquiryPackageItems::isChecked)
                .map(i -> InquiryWayTypeEnum.fromCode(i.getInquiryWayType()).getDescForPackage() +
                    (i.isUnlimited() ? "不限" : Optional.ofNullable(i.getCount()).orElse(0L).toString()))
                .collect(Collectors.joining("，"));

            sb.append(itemsStr);
            return sb.toString();
        } else {
            // 旧格式：直接显示问诊方式和额度
            return InquiryWayTypeEnum.fromCode(item.getInquiryWayType()).getDescForPackage() +
                (item.isUnlimited() ? "不限" : item.getCount().toString());
        }
    }

    /**
     * 获取处方类型描述 使用 DictFrameworkUtils 转换处方类型的值
     */
    private static String getPrescriptionTypeDesc(String type, List<Integer> values) {
        if (StringUtils.isBlank(type) || CollUtil.isEmpty(values)) {
            return "";
        }

        return values.stream()
            .map(value -> {
                String label = DictFrameworkUtils.getDictDataLabel(type, value.toString());
                return StringUtils.isNotBlank(label) ? label : "未知类型";
            })
            .collect(Collectors.joining("/"));
    }


}
