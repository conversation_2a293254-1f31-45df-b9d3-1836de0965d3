package com.xyy.saas.inquiry.enums.prescription;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import java.util.Arrays;

/**
 * 处方类型
 */
@Getter
@RequiredArgsConstructor
public enum PrescriptionTypeEnum implements IntArrayValuable {


    GENERAL_PRESCRIPTION(0, "非统筹处方"),

    PRESCRIPTION(1, "统筹处方"),

    CHRONIC_DISEASES_PRESCRIPTION(2, "门诊慢特病处方"),

    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrescriptionTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static PrescriptionTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(null);
    }
}
