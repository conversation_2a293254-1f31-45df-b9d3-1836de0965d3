package com.xyy.saas.inquiry.patient.service.inquiry.impl;

import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.doctor.review.InquiryDoctorReviewApi;
import com.xyy.saas.inquiry.hospital.api.doctor.review.dto.DoctorReviewDto;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.im.api.user.InquiryImUserApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordDetailRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryReceptionInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRecordAndPrescriptionVO;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordDetailConvert;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordPrescriptionConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryRecordQueryService;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryService;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @Desc 问诊记录查询
 * <AUTHOR>
 */
@Service
@Slf4j
public class InquiryRecordQueryServiceImpl implements InquiryRecordQueryService {

    @Resource
    private InquiryService inquiryService;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    @DubboReference
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @DubboReference
    private InquiryImUserApi inquiryImUserApi;

    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    @DubboReference
    private InquiryHospitalApi inquiryHospitalApi;

    @DubboReference
    private InquiryDoctorReviewApi inquiryDoctorReviewApi;

    @Override
    @InquiryDateType
    public InquiryRecordDetailRespVO queryInquiryRecordVOByPref(String inquiryPref) {
        InquiryRecordDetailRespVO recordPrescriptionVO = inquiryService.getInquiryRecordDetailVO(inquiryPref);
        // 填充处方信息
        if (Objects.equals(recordPrescriptionVO.getInquiryStatus(), InquiryStatusEnum.ENDED.getStatusCode())) {
            InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescription(InquiryPrescriptionQueryDTO.builder().inquiryPref(inquiryPref).build());
            if (prescription != null) {
                recordPrescriptionVO.setOutPrescriptionTime(prescription.getOutPrescriptionTime());
                recordPrescriptionVO.setPrescriptionPref(prescription.getPref());
            }
        }
        return recordPrescriptionVO;

    }


    @Override
    public InquiryRecordAndPrescriptionVO getInquiryPrescriptionByPref(String inquiryPref) {
        // 查问诊+详情
        InquiryRecordDetailRespVO inquiryRecordVO = inquiryService.getInquiryRecordDetailVO(inquiryPref);
        // 查处方 - 可能还没有处方记录
        InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescriptionWithPdf(InquiryPrescriptionQueryDTO.builder().inquiryPref(inquiryPref).build());
        // 填充编码
        Optional.ofNullable(inquiryDoctorApi.getInquiryDoctorCardInfoByDoctorPref(inquiryRecordVO.getDoctorPref())).ifPresent(inquiryDoctorCardInfoDto -> {
            inquiryRecordVO.setDoctorMedicareNo(inquiryDoctorCardInfoDto.getDoctorMedicareNo());
        });
        Optional.ofNullable(inquiryHospitalApi.getInquiryHospitalByPref(inquiryRecordVO.getHospitalPref())).ifPresent(inquiryHospitalDto -> {
            inquiryRecordVO.setInstitutionCode(inquiryHospitalDto.getInstitutionCode());
        });

        // 隐藏处方
        boolean isHidePrescription = getPrescriptionShowStatus(inquiryRecordVO);

        return InquiryRecordPrescriptionConvert.INSTANCE.convertRecordPrescription(inquiryRecordVO, prescription, isHidePrescription);
    }

    /**
     * 处方查询场景控制当前处方是否展示
     *
     * @param inquiryRecordVO 问诊信息
     * @return isHidePrescription 是否隐藏处方  默认 false
     */
    private boolean getPrescriptionShowStatus(InquiryRecordDetailRespVO inquiryRecordVO) {
        boolean isHidePrescription = false;
        // 当前查询人员类型
        LoginUser loginUser = getLoginUser();
        // 系统用户不隐藏
        if (loginUser == null || ObjectUtil.equals(loginUser.getUserType(), UserTypeEnum.ADMIN.getValue())) {
            return isHidePrescription;
        }
        // 查询门店配置
        TenantDto tenantDto = tenantApi.getTenant(inquiryRecordVO.getTenantId());
        // 小程序问诊需要判断当前商家是否需要隐藏处方
        InquiryOptionConfigRespDto inquiryOptionConfig = inquiryOptionConfigApi.getInquiryOptionConfig(tenantDto, InquiryOptionTypeEnum.PROC_WE_CHAT_INQUIRY_PATIENT_VIEW_PRESCRIPTION);
        if (ObjectUtil.isNotEmpty(inquiryOptionConfig) && ObjectUtil.isNotEmpty(inquiryOptionConfig.getProcWeChatInquiryPatientViewPrescription()) && ObjectUtil.equals(inquiryOptionConfig.getProcWeChatInquiryPatientViewPrescription(),
            Boolean.TRUE)) {
            return isHidePrescription;
        }
        return Boolean.TRUE;
    }

    @Override
    public InquiryReceptionInfoRespVO getReceptionInfo(String inquiryPref) {
        // 查问诊单
        InquiryRecordDto recordDto = inquiryService.getInquiryDtoByPref(inquiryPref);
        // 获取问诊详细信息
        InquiryRecordDetailDO inquiryDetailDo = inquiryService.getInquiryRecordDetailByPref(inquiryPref);
        // 接诊医生信息
        InquiryDoctorDto doctorDto = null;
        // 查询医生信息
        if (StringUtils.isNotBlank(recordDto.getDoctorPref())) {
            doctorDto = inquiryDoctorApi.getInquiryDoctorByDoctorPref(recordDto.getDoctorPref());
        }
        // 查询问诊评价信息
        DoctorReviewDto reviewDto = null;
        if (ObjectUtil.equal(InquiryStatusEnum.ENDED.getStatusCode(), recordDto.getInquiryStatus())
            || ObjectUtil.equal(InquiryStatusEnum.DOCTOR_CANCELED.getStatusCode(), recordDto.getInquiryStatus())
            || ObjectUtil.equal(InquiryStatusEnum.TIMEOUT_CANCELED.getStatusCode(), recordDto.getInquiryStatus())) {
            reviewDto = inquiryDoctorReviewApi.getDoctorReview(inquiryPref);
        }
        // 获取商家IM账号
        String patientImAccount = inquiryImUserApi.getImAccountByUserIdAndClientType(Long.parseLong(recordDto.getCreator()), recordDto.getClientChannelType());
        // 获取医生IM账号
        String doctorImAccount = Optional.ofNullable(doctorDto).map(dto -> inquiryImUserApi.getDoctorImAccountByDoctorPref(dto.getPref())).orElse("");
        return InquiryRecordDetailConvert.INSTANCE.convertDO2VO(doctorImAccount, patientImAccount, doctorDto, recordDto, inquiryDetailDo, reviewDto);
    }

    @Override
    public PageResult<InquiryRecordRespVO> getInquiryRecordPage(InquiryRecordPageReqVO pageReqVO) {
        return null;
    }
}
