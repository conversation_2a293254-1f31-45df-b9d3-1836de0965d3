package com.xyy.saas.inquiry.patient.es.inquiry;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import com.xyy.saas.binlog.core.es.EsQueryBuilder;
import com.xyy.saas.binlog.core.es.EsQueryService;
import com.xyy.saas.binlog.core.es.EsSearchRequest;
import com.xyy.saas.binlog.core.es.EsSearchResponse;
import com.xyy.saas.binlog.core.es.EsSearchResponse.EsHit;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.QuerySourceEnum;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordRespVO;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 问诊表ES数据查询
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/29 11:14
 */
@Service
@Slf4j
public class InquiryRecordEsService {

    @Resource
    private EsQueryService esQueryService;

    private static final String INDEX_NAME = "saas_inquiry_record_index_";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * ES分页查询问诊记录 参考 InquiryRecordMapper#selectPage 的SQL查询逻辑转换为ES查询
     */
    public PageResult<InquiryRecordRespVO> getEsInquiryRecordPage(InquiryRecordPageReqVO pageReqVO) {
        try {
            BoolQuery.Builder boolQuery = new BoolQuery.Builder();

            // 构建查询条件
            buildInquiryRecordQueryConditions(boolQuery, pageReqVO);

            // 构建搜索请求
            String index = StrUtil.concat(true, INDEX_NAME, StringUtils.defaultIfBlank(pageReqVO.getYear(), LocalDateTime.now().getYear() + ""));
            String routing = getRouting(pageReqVO);
            EsSearchRequest searchRequest = EsSearchRequest.builder()
                .index(index)
                .routing(routing)
                .trackTotalHits(true)
                .query(boolQuery.build()._toQuery())
                .from(pageReqVO.getPageNo() != null ? (pageReqVO.getPageNo() - 1) * pageReqVO.getPageSize() : 0)
                .size(pageReqVO.getPageSize() != null ? pageReqVO.getPageSize() : 10)
                .sorts(List.of(EsQueryBuilder.sort("createTime", SortOrder.Desc))) // 按创建时间倒序
                .build();

            // 执行ES查询
            EsSearchResponse<InquiryRecordRespVO> response = esQueryService.search(searchRequest, InquiryRecordRespVO.class);

            if (response == null) {
                log.warn("[getEsInquiryRecordPage] ES查询返回null");
                return PageResult.empty();
            }

            // 转换结果
            List<InquiryRecordRespVO> list = response.getHits() != null ?
                response.getHits().stream().map(EsHit::getSource).toList() :
                Collections.emptyList();
            long total = response.getTotalHits() != null ? response.getTotalHits().getValue() : 0L;

            log.info("[getEsInquiryRecordPage] ES查询成功，返回{}条记录，总计{}条", list.size(), total);
            return new PageResult<>(list, total);

        } catch (Exception e) {
            log.error("[getEsInquiryRecordPage] ES查询异常", e);
            return PageResult.empty();
        }
    }

    /**
     * 构建问诊记录查询条件
     * <p>
     * 原始SQL查询逻辑参考：InquiryRecordMapper#selectPage 主要查询条件包括： 1. 租户过滤：tenant_id = ? OR tenant_id IN (?) 2. 问诊单号：pref = ? OR pref IN (?) 3. 患者信息：patient_pref = ? AND patient_name LIKE ? 4. 医生信息：doctor_pref = ? 5. 状态过滤：inquiry_status = ? OR
     * inquiry_status IN (?) 6. 业务类型：inquiry_way_type, biz_channel_type, client_channel_type, inquiry_biz_type 7. 其他条件：auto_inquiry, creator, enable 8. 医院过滤：hospital_pref IN (?) 9. 时间范围：create_time BETWEEN ? AND ? 10. APP特殊逻辑：排除远程审方类型 11.
     * 排序：ORDER BY create_time DESC
     */
    private void buildInquiryRecordQueryConditions(BoolQuery.Builder boolQuery, InquiryRecordPageReqVO pageReqVO) {
        // 租户ID过滤 - 支持单个租户和多租户查询
        // SQL: tenant_id = ? OR tenant_id IN (?)
        EsQueryBuilder.termQueryOpt("tenantId", pageReqVO.getTenantId()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("tenantId", pageReqVO.getTenantIds()).ifPresent(boolQuery::filter);

        // 问诊单号过滤 - 支持精确匹配和批量查询
        // SQL: pref = ? OR pref IN (?)
        EsQueryBuilder.termQueryOpt("pref", pageReqVO.getPref()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("pref", pageReqVO.getPrefs()).ifPresent(boolQuery::filter);

        // 患者相关过滤
        // SQL: patient_pref = ? AND patient_name LIKE '%?%'
        EsQueryBuilder.termQueryOpt("patientPref", pageReqVO.getPatientPref()).ifPresent(boolQuery::filter);
        // EsQueryBuilder.wildcardQueryOpt("patientName.keyword", pageReqVO.getPatientName()).ifPresent(boolQuery::must);

        // 医生过滤 - 精确匹配医生编码
        // SQL: doctor_pref = ?
        EsQueryBuilder.termQueryOpt("doctorPref", pageReqVO.getDoctorPref()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("hospitalPref", pageReqVO.getHospitalPref()).ifPresent(boolQuery::filter);
        // 填写身份证号查询
        EsQueryBuilder.termQueryOpt("patientHasIdCard", pageReqVO.getPatientHasIdCard()).ifPresent(boolQuery::filter);

        // 问诊状态过滤 - 支持单个状态和多状态查询
        // SQL: inquiry_status = ? OR inquiry_status IN (?)
        EsQueryBuilder.termQueryOpt("inquiryStatus", pageReqVO.getInquiryStatus()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("inquiryStatus", pageReqVO.getInquiryStatusList()).ifPresent(boolQuery::filter);

        // 业务类型过滤 - 问诊方式、渠道类型等
        // SQL: inquiry_way_type = ? AND biz_channel_type = ? AND client_channel_type = ? AND inquiry_biz_type = ?
        EsQueryBuilder.termQueryOpt("inquiryWayType", pageReqVO.getInquiryWayType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("bizChannelType", pageReqVO.getBizChannelType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("clientChannelType", pageReqVO.getClientChannelType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("inquiryBizType", pageReqVO.getInquiryBizType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("prescriptionType", pageReqVO.getPrescriptionType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("slowDisease", pageReqVO.getSlowDisease()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("offlinePrescription", pageReqVO.getOfflinePrescription()).ifPresent(boolQuery::filter);

        // 其他条件过滤
        // SQL: auto_inquiry = ? AND creator = ? AND enable = ?
        EsQueryBuilder.termQueryOpt("autoInquiry", pageReqVO.getAutoInquiry()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("creator", pageReqVO.getCreator()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("enable", pageReqVO.getEnable()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("deleted", false).ifPresent(boolQuery::filter);
        // 医院编码列表过滤 - 支持多医院查询
        // SQL: hospital_pref IN (?)
        EsQueryBuilder.termQueryOpt("hospitalPref", pageReqVO.getHospitalPrefs()).ifPresent(boolQuery::filter);

        // 创建时间范围过滤
        // SQL: create_time BETWEEN ? AND ?
        buildTimeRangeQuery("createTime", pageReqVO.getCreateTime()).ifPresent(boolQuery::filter);

        // APP查询特殊逻辑：排除远程审方类型
        // SQL: inquiry_biz_type != ?
        // 业务含义：APP端查询时不显示远程审方的问诊记录
        if (ObjectUtil.equals(pageReqVO.getQuerySource(), QuerySourceEnum.APP)) {
            EsQueryBuilder.termQueryOpt("inquiryBizType", InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())
                .ifPresent(boolQuery::mustNot);
        }
    }

    /**
     * 构建时间范围查询 参考 ProductStdlibEsServiceImpl 的实现风格
     */
    private java.util.Optional<Query> buildTimeRangeQuery(String field, LocalDateTime[] timeRange) {
        if (timeRange == null || timeRange.length != 2) {
            return java.util.Optional.empty();
        }

        LocalDateTime startTime = timeRange[0];
        LocalDateTime endTime = timeRange[1];

        if (startTime == null || endTime == null) {
            return java.util.Optional.empty();
        }

        Query rangeQuery = EsQueryBuilder.rangeQuery(field)
            .gte(startTime.format(DATE_TIME_FORMATTER))
            .lte(endTime.format(DATE_TIME_FORMATTER))
            .build();

        return java.util.Optional.of(rangeQuery);
    }

    private String getRouting(InquiryRecordPageReqVO queryVo) {
        // 超管,查全部 或 单个门店
        if (TenantConstant.isSystemTenant()) {
            return queryVo.getTenantId() == null ? null : queryVo.getTenantId().toString();
        }
        // 单体查自己，或者连锁查所有
        if (CollUtil.isEmpty(queryVo.getTenantIds())) {
            return queryVo.getTenantId() == null ? TenantContextHolder.getTenantId().toString() : queryVo.getTenantId().toString();
        }
        return CollUtil.size(queryVo.getTenantIds()) == 1 ? queryVo.getTenantIds().getFirst().toString() : null;
    }
}
