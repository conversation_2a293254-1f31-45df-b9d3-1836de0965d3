package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import cn.iocoder.yudao.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * @Author: xucao
 * @DateTime: 2025/7/17 11:08
 * @Description: 问诊人信息
 **/
@Schema(description = "问诊人基础信息VO模型")
@Data
@Valid
public class BaseInquiryPatientVO {

    @Schema(description = "处方类型")
    private Integer prescriptionType;

    @Schema(description = "患者编码")
    private String patientPref;

    @Schema(description = "患者姓名", example = "张三")
    @NotBlank(message = "请输入用药人姓名")
    @Size(max = 20, message = "患者姓名不能超过20个字符")
    private String patientName;

    @Schema(description = "患者手机号", example = "13800138000")
    @Mobile(message = "用药人手机格式不正确")
    @NotEmpty(message = "请输入用药人手机号")
    private String patientMobile;

    @Schema(description = "患者身份证号(非必填)", example = "110101199001011234")
    @Size(max = 32, message = "身份证号码超长，请检查")
    private String patientIdCard;

    @Schema(description = "患者年龄", example = "30")
    @NotBlank(message = "患者年龄不能为空")
    @Size(max = 4, message = "年龄输入有误,请检查")
    private String patientAge;

    @Schema(description = "患者性别", example = "1 男 2 女")
    @NotNull(message = "患者性别不能为空")
    private Integer patientSex;

    @Schema(description = "监护人姓名")
    @Size(max = 20, message = "监护人姓名不能超过20个字符")
    private String guardianName;

    @Schema(description = "监护人身份证号")
    @Size(max = 32, message = "监护人身份证号码超长，请检查")
    private String guardianIdCard;
}
