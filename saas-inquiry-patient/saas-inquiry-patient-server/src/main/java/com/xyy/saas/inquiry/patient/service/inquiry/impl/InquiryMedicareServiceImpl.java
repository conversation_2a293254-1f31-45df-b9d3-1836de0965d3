package com.xyy.saas.inquiry.patient.service.inquiry.impl;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.hospital.api.medicare.MedicareBaseApi;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicarePersonInfoRespDTO;
import com.xyy.saas.inquiry.patient.api.medical.dto.MedicarePersonInsuranceRecordDTO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryPatientVO;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryMedicareConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.medicare.MedicarePersonInsuranceRecordDO;
import com.xyy.saas.inquiry.patient.dal.mysql.medicare.MedicarePersonInsuranceRecordMapper;
import com.xyy.saas.inquiry.patient.enums.SuperviseErrorCodeConstants;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryMedicareService;
import com.xyy.saas.transmitter.api.servicepack.TransmissionServicePackApi;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionFunConfigOptionDTO;
import com.xyy.saas.transmitter.api.transmission.TransmissionApi;
import com.xyy.saas.transmitter.api.transmission.TransmissionConfigApi;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @DateTime: 2025/7/17 11:57
 * @Description: 问诊医保相关服务
 **/
@Service
@Slf4j
public class InquiryMedicareServiceImpl implements InquiryMedicareService {

    @Resource
    private MedicarePersonInsuranceRecordMapper medicarePersonInsuranceRecordMapper;

    @DubboReference
    private TransmissionApi transmissionApi;

    @DubboReference
    private TransmissionServicePackApi transmissionServicePackApi;

    @DubboReference
    private TransmissionConfigApi transmissionConfigApi;

    @DubboReference
    private MedicareBaseApi medicareBaseApi;

    /**
     * 查询参保人信息
     *
     * @param baseInquiryPatientVO 患者信息
     * @return 参保信息记录ID
     */
    @Override
    public CommonResult<Long> queryPersonInfo(BaseInquiryPatientVO baseInquiryPatientVO) {
        String idCard = baseInquiryPatientVO.getPatientIdCard();
        Long tenantId = TenantContextHolder.getTenantId();

        // 是否需要读取参保人信息
        boolean needInsuredInfo = transmissionConfigApi.isNeedReadInsuredInfo(tenantId, baseInquiryPatientVO.getPrescriptionType());
        // 不需要读取直接返回
        if (!needInsuredInfo) {
            return null;
        }
        TransmissionFunConfigOptionDTO configOptionDTO = transmissionConfigApi.getTransmissionConfigReqDTO(tenantId);
        // 构建查询传输配置
        TransmissionConfigReqDTO queryConfigReqDTO = TransmissionConfigReqDTO.builder().tenantId(tenantId).nodeType(NodeTypeEnum.MEDICARE_PERSON_INFO_QUERY).build();
        // 构建查询传输请求
        TransmissionReqDTO queryReqDTO = TransmissionReqDTO.buildReq(queryConfigReqDTO, InquiryMedicareConvert.INSTANCE.buildPersonInfoQueryTransmitterData(idCard, configOptionDTO.getSupervisionHospitalPref()));

        // 查询签到 设置签到信息
        // MedicareSignDTO medicareSignDTO = medicareBaseApi.getMedicareSignInfo(configOptionDTO.getSupervisionHospitalPref());
        // InquiryMedicareConvert.INSTANCE.setSigninInfoToAux(queryReqDTO, medicareSignDTO);
        // 业务逻辑校验
        CommonResult<Boolean> businessLogic = transmissionApi.validateBusinessLogic(queryReqDTO);
        if (!businessLogic.isSuccess()) {
            log.error("【人员信息查询】业务逻辑校验失败，身份证号：{}, 错误：{}", idCard, businessLogic.getMsg());
            throw exception(SuperviseErrorCodeConstants.NOT_CONFIGURED_1101_PERSON_INFO_QUERY_NODE);
        }
        // 调用DSL进行查询
        CommonResult<MedicarePersonInfoRespDTO> queryResult = transmissionApi.contractInvoke(queryReqDTO, MedicarePersonInfoRespDTO.class);
        log.info("【人员信息查询】1101查询调用结果：{}", queryResult);
        if (queryResult.isError()) {
            throw exception0(queryResult.getCode(), queryResult.getMsg());
        }
        // 保存参保信息记录并返回ID
        MedicarePersonInsuranceRecordDO personInsuranceRecord = getPersonInsuranceRecord(tenantId, queryResult.getData(), configOptionDTO);
        if (personInsuranceRecord == null) {
            throw exception(SuperviseErrorCodeConstants.NO_EFFECTIVE_PERSON_INFO);
        }
        medicarePersonInsuranceRecordMapper.insert(personInsuranceRecord);
        return CommonResult.success(personInsuranceRecord.getId());
    }

    private MedicarePersonInsuranceRecordDO getPersonInsuranceRecord(Long tenantId, MedicarePersonInfoRespDTO personInfo, TransmissionFunConfigOptionDTO configOptionDTO) {
        if (ObjectUtil.isEmpty(personInfo)) {
            return null;
        }

        List<String> insuredType = StringUtils.isNotBlank(configOptionDTO.getEffectiveInsuredType()) ? List.of(configOptionDTO.getEffectiveInsuredType().split(",")) : List.of();
        List<String> insuredStatus = StringUtils.isNotBlank(configOptionDTO.getEffectiveInsuredStatus()) ? List.of(configOptionDTO.getEffectiveInsuredStatus().split(",")) : List.of();
        // 如果insuredType 不为空，则过滤personInfo中的InsuranceInfo信息
        if (!insuredType.isEmpty()) {
            personInfo.setInsuinfo(personInfo.getInsuinfo().stream().filter(info -> insuredType.contains(info.getInsuranceType())).toList());
        }
        if (!insuredStatus.isEmpty()) {
            personInfo.setInsuinfo(personInfo.getInsuinfo().stream().filter(info -> insuredStatus.contains(info.getInsuranceStatus())).toList());
        }
        if (CollectionUtils.isEmpty(personInfo.getInsuinfo())) {
            return null;
        }
        // personInfo中的InsuranceInfo信息  根据参保日期降序排序后取第一条
        personInfo.setInsuinfo(List.of(personInfo.getInsuinfo().stream().sorted((o1, o2) -> o2.getBeginDate().compareTo(o1.getBeginDate())).toList().getFirst()));
        return InquiryMedicareConvert.INSTANCE.buildPersonInsuranceRecord(tenantId, personInfo);
    }


    @Override
    public MedicarePersonInsuranceRecordDTO getMedicarePersonInsuranceRecord(Long id) {
        return InquiryMedicareConvert.INSTANCE.convert(medicarePersonInsuranceRecordMapper.selectById(id));
    }
}
