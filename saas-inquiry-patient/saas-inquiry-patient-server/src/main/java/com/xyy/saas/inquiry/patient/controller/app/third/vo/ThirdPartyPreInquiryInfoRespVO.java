package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ExcelIgnoreUnannotated
@Schema(description = "app侧 - 三方获取处方信息返参对象")
public class ThirdPartyPreInquiryInfoRespVO {

    @Schema(description = "预问诊id")
    private Long id;

    @Schema(description = "预问诊编码(流水号)")
    @ExcelProperty("问诊流水号")
    private String pref;

    @Schema(description = "三方预问诊单渠道来源")
    private Integer transmissionOrganId;

    @Schema(description = "三方预问诊单渠道来源名称")
    private String transmissionOrganName;

    @Schema(description = "患者姓名")
    @ExcelProperty("患者姓名")
    private String userName;

    @Schema(description = "性别")
    @ExcelProperty(value = "性别", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.USER_SEX)
    private Integer sex;

    @Schema(description = "患者手机号")
    @ExcelProperty("患者手机号")
    private String mobile;

    @Schema(description = "身份证")
    private String idCard;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "用药类型：0西药，1中药")
    private Integer medicineType;


    @Schema(description = "扩展字段")
    private DrugstoreInquiryReqVO ext;


    @Schema(description = "订单日期")
    @ExcelProperty("订单日期")
    private LocalDateTime createTime;

    /**
     * {@link InquiryWayTypeEnum}
     */
    @Schema(description = "问诊方式 1-图文，2-视频，3-电话")
    @ExcelProperty(value = "问诊方式" , converter = DictConvert.class)
    @DictFormat(DictTypeConstants.INQUIRY_WAY_TYPE)
    private Integer inquiryWayType;

    @Schema(description = "问诊门店")
    @ExcelProperty("问诊门店")
    private String tenantName;

    @Schema(description = "药品详情")
    private List<ThirdPartyPreInquiryDetailRespVO> drugList;
}
