package com.xyy.saas.inquiry.patient.service.strategy.autoinquiry;

import com.xyy.saas.inquiry.annotation.TraceNode;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryJudgeNodeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.UnableAutoReasonEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.patient.InquiryPatientInfoDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.patient.InquiryPatientInfoMapper;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @Date: 2024/12/23 9:51
 * @Description: 自动开方策略-同一患者24小时内存在视频问诊自动开方记录
 */
@Component
public class AutoInquiryManyAutoVideoStrategy extends AutoInquiryStrategy {

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;

    @Resource
    private ConfigApi configApi;

    @Resource
    private InquiryPatientInfoMapper inquiryPatientInfoMapper;

    /**
     * 执行判定是否自动开方方法
     *
     * @param inquiryDto   问诊参数
     * @param globalConfig 全局配置
     */
    @Override
    @TraceNode(node = TraceNodeEnum.AUOTO_INQUIRY_MANYAUTOVIDEO ,prefLocation = "inquiryDto.pref")
    public void executeJudge(InquiryRecordDto inquiryDto, InquiryOptionGlobalConfigRespDto globalConfig) {

        // 非视频问诊直接return
        if (!inquiryDto.isVideoInquiry()) {
            return;
        }

        // 查询患者的pref
        InquiryPatientInfoDO existPatientInfoDO = inquiryPatientInfoMapper.selectByNameAndMobile(
            InquiryPatientInfoPageReqVO.builder().name(inquiryDto.getPatientName()).mobile(inquiryDto.getPatientMobile()).tenantId(inquiryDto.getTenantDto().getId()).build());

        if (existPatientInfoDO == null) {
            return;
        }

        InquiryRecordPageReqVO inquiryRecordPageReqVO = InquiryRecordPageReqVO.builder()
            .tenantId(inquiryDto.getTenantId())
            .createTime(new LocalDateTime[]{LocalDateTime.now().minusDays(1), LocalDateTime.now()})
            .patientPref(existPatientInfoDO.getPref())
            .inquiryWayType(InquiryWayTypeEnum.VIDEO.getCode())
            .autoInquiry(AutoInquiryEnum.YES.getCode()).build();

        // 获取同一患者24小时内存在视频问诊自动开方记录次数
        Long autoInquiryCount = inquiryRecordMapper.selectCountByCondition(inquiryRecordPageReqVO);
        autoInquiryCount = autoInquiryCount == null ? 0 : autoInquiryCount;

        // 获取同一患者24小时内存在视频问诊真人开方记录次数
        inquiryRecordPageReqVO.setAutoInquiry(AutoInquiryEnum.NO.getCode());
        Long noAutoInquiryCount = inquiryRecordMapper.selectCountByCondition(inquiryRecordPageReqVO);
        noAutoInquiryCount = noAutoInquiryCount == null ? 0 : noAutoInquiryCount;

        // 获取配置的ai问诊间隔真人人数
        int autoInquiryIntervalCount = MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(InquiryConstant.AUTO_INQUIRY_INTERVAL_COUNT), 2);

        // 24小时内 , (真人次数 < 配置次数 && AI次数 != 0) 走真人 , 反之走AI
        if (noAutoInquiryCount >= autoInquiryIntervalCount || autoInquiryCount == 0) {
            return;
        }

        inquiryDto.setAutoInquiry(AutoInquiryEnum.NO.getCode());
        inquiryDto.setUnableAutoReason(UnableAutoReasonEnum.PATIENT_HAD_24HOURS_VIDEO_AUTO_INQUIRY.getCode());
    }

    @Override
    public AutoInquiryJudgeNodeEnum getAutoInquiryJudgeNodeEnum() {
        return AutoInquiryJudgeNodeEnum.AUTO_INQUIRY_TO_REAL_FOR_MANY_AUTO_VIDEO;
    }
}
