package com.xyy.saas.inquiry.patient.controller.admin.third.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "管理后台 - 三方药品匹配失败记录 Response VO")
public class ThirdPartyDrugMatchFailRecordRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "22831")
    private Long id;

    @Schema(description = "三方平台代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "11123")
    private Integer transmissionOrganId;

    @Schema(description = "三方平台代码名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "11123")
    private String transmissionOrganName;

    @Schema(description = "药品名称", example = "赵六")
    private String commonName;

    @Schema(description = "商品规格")
    private String attributeSpecification;

    @Schema(description = "69码")
    private String barCode;

    @Schema(description = "批准文号")
    private String approvalNumber;

    @Schema(description = "匹配失败原因")
    private String matchFailMsg;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}
