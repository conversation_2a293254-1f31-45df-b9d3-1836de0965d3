package com.xyy.saas.inquiry.patient.dal.dataobject.inquiry;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.BizChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryGrabStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.StreamStatus;
import com.xyy.saas.inquiry.enums.inquiry.UnableAutoReasonEnum;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 问诊记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_record")
// @KeySequence("saas_inquiry_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryRecordDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 问诊单号
     */
    private String pref;
    /**
     * 患者编码
     */
    private String patientPref;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者年龄
     */
    private String patientAge;
    /**
     * 患者性别 1、男   2、女
     */
    private Integer patientSex;
    /**
     * 患者手机号
     */
    private String patientMobile;
    /**
     * 互联网医院编码
     */
    private String hospitalPref;
    /**
     * 互联网医院名称
     */
    private String hospitalName;
    /**
     * 科室编码
     */
    private String deptPref;
    /**
     * 科室编码
     */
    private String deptName;
    /**
     * 处方笺模版id
     */
    private Long preTempId;
    /**
     * 医生GUID
     */
    private String doctorPref;


    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 接诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消 {@link InquiryStatusEnum }
     */
    private Integer inquiryStatus;
    /**
     * 取消开方原因
     */
    private String cancelReason;
    /**
     * 问诊方式  1、图文问诊  2、视频问诊  3、电话问诊 {@link InquiryWayTypeEnum }
     */
    private Integer inquiryWayType;
    /**
     * 问诊业务类型 1、药店问诊  2、远程审方 {@link InquiryBizTypeEnum }
     */
    private Integer inquiryBizType;
    /**
     * 客户端渠类型 0、app  1、pc  2、小程序 {@link ClientChannelTypeEnum}
     */
    private Integer clientChannelType;
    /**
     * 客户端系统类型
     */
    private String clientOsType;
    /**
     * 问诊渠道 0、荷叶 1、智慧脸  2、海典ERP {@link BizChannelTypeEnum}
     */
    private Integer bizChannelType;
    /**
     * 用药类型：0西药  、1中药 {@link MedicineTypeEnum}
     */
    private Integer medicineType;
    /**
     * 是否自动开方：0 否  、 1是 {@link AutoInquiryEnum}
     */
    private Integer autoInquiry;

    /**
     * 自动抢派单状态：0 常规派单  、 1、自动抢单 {@link InquiryGrabStatusEnum}
     */
    private Integer autoGrabStatus;

    /**
     * 不走自动开方的原因  1、预购药品无用法用量 2、无自动开方医生  3、门店未开通自动开方 {@link UnableAutoReasonEnum} unableAutoReason
     */
    private Integer unableAutoReason;
    /**
     * IM平台类型  0、腾讯IM
     */
    private Integer imPlatform;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 问诊扩展字段
     */
    // private String ext;
    /**
     * 医生接诊时间
     */
    private LocalDateTime startTime;
    /**
     * 问诊结束时间
     */
    private LocalDateTime endTime;
    /**
     * 医生录屏编码
     */
    private String doctorVideoPref;
    /**
     * 视频流状态 0 未视频 1 已推流 2 已开启转推 3 已停止转推 {@link StreamStatus}
     */
    private Integer streamStatus;
    /**
     * 视频地址
     */
    private String mp4Url;
    /**
     * im问诊记录
     */
    private String imPdf;

    /**
     * 聊天记录内容
     */
    private String imHistory;

    /**
     * 视频混流id
     */
    private String streamId;
    /**
     * 任务ID
     */
    private String transcodingId;

    /**
     * 数据状态 0 有效 1 作废 {@link CommonStatusEnum}
     */
    private Integer enable;
}