package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.QuestionAnswerInfoDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2025/01/22 16:34
 * @Description: 问诊接诊信息查询返回参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryReceptionInfoRespVO {

    @Schema(description = "问诊商家IM账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String patientImAccount;

    @Schema(description = "接诊医生IM账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String doctorImAccount;

    @Schema(description = "接诊医生头像", requiredMode = Schema.RequiredMode.REQUIRED)
    private String doctorHeadImageUrl;

    //问诊单状态
    @Schema(description = "问诊单状态 0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消", requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    private Integer inquiryStatus;

    @Schema(description = "问诊方式 图文 or  视频", requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    private Integer inquiryWayType;

    @Schema(description = "问诊通话时长", requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    private Integer inquiryCallTime;

    //医生小助聊天内容
    @Schema(description = "医生小助聊天内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<QuestionAnswerInfoDO> questionAnswerList;

    //评价状态
    @Schema(description = "评价状态 0 未评价 1已评价", requiredMode = Schema.RequiredMode.REQUIRED,example = "1")
    @ExcelProperty("评价状态")
    private Integer reviewsState;

    @Schema(description = "满意度评分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("满意度评分")
    private BigDecimal satisfactionScore;

    @Schema(description = "满意的点", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("满意的点")
    private List<String> satisfactionItem;

    @Schema(description = "评论内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("评论内容")
    private String reviewsContent;
}
