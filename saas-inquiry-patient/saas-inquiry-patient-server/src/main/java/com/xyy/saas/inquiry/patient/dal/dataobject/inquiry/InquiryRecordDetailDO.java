package com.xyy.saas.inquiry.patient.dal.dataobject.inquiry;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 问诊记录详情 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_inquiry_record_detail", autoResultMap = true)
// @KeySequence("saas_inquiry_record_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryRecordDetailDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 问诊单GUID
     */
    private String inquiryPref;

    /**
     * 租户编号
     */
    private Long tenantId;
    /**
     * 肝肾功能异常  0、无  1、肝功能异常  2、肾功能异常  3、肝肾功能异常
     */
    private Integer liverKidneyValue;
    /**
     * 妊娠哺乳期   0、否  1、妊娠期   2、哺乳期  3、妊娠哺乳期
     */
    private Integer gestationLactationValue;
    /**
     * 患者GUID
     */
    private String patientPref;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者性别：1 男 2 女
     */
    private Integer patientSex;
    /**
     * 患者年龄
     */
    private String patientAge;
    /**
     * 患者手机号
     */
    private String patientMobile;

    /**
     * 患者身份证号码
     */
    private String patientIdCard;

    /**
     * 慢病病情需要 0 否  1是
     */
    private Integer slowDisease;

    /**
     * 处方类型
     */
    private Integer prescriptionType;
    /**
     * 主诉
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> mainSuit;
    /**
     * 过敏史  eg：青霉素|头孢
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> allergic;
    /**
     * 诊断编码
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> diagnosisCode;
    /**
     * 诊断说明
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> diagnosisName;
    /**
     * 个人史
     */
    private String patientHisDesc;
    /**
     * 现病史
     */
    private String currentIllnessDesc;
    /**
     * 线下就医处方或病历图片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> offlinePrescriptions;
    /**
     * 预购药明细
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private InquiryProductDto preDrugDetail;

    /**
     * 问诊扩展信息
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private InquiryDetailExtDO ext;
    /**
     * 备注说明
     */
    private String remarks;

    @JsonIgnore
    public InquiryDetailExtDO extGet() {
        if (ext == null) {
            ext = new InquiryDetailExtDO();
        }
        return ext;
    }
}