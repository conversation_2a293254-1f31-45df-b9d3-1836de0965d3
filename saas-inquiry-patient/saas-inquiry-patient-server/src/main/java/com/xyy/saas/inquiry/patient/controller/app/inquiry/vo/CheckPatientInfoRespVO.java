package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @DateTime: 2025/7/18 11:34
 * @Description: 检查患者信息返回结果
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckPatientInfoRespVO {

    /**
     * 医保参保人信息
     */
    private Long medicareInsuranceId;

    /**
     * 商品查询是否切目录
     */
    private boolean isSwitchProductCategory;
}
