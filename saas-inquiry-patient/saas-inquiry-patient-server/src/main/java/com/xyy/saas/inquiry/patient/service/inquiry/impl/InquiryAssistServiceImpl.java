package com.xyy.saas.inquiry.patient.service.inquiry.impl;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryPatientVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.CheckPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryAssistService;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryMedicareService;
import com.xyy.saas.inquiry.util.IdCardUtil;
import com.xyy.saas.transmitter.api.transmission.TransmissionConfigApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_RECORD_ID_CARD_FAIL;

/**
 * @Author: xucao
 * @DateTime: 2025/7/17 11:22
 * @Description: 问诊流程中辅助服务
 **/
@Service
@Slf4j
public class InquiryAssistServiceImpl implements InquiryAssistService {

    @Resource
    private InquiryMedicareService inquiryMedicareService;

    @DubboReference
    private TransmissionConfigApi transmissionConfigApi;

    /**
     * 患者信息填写完毕后校验
     *
     * @param baseInquiryPatientVO
     * @return
     */
    @Override
    public CommonResult<CheckPatientInfoRespVO> checkPatientInfo(BaseInquiryPatientVO baseInquiryPatientVO) {
        String patientIdCard = baseInquiryPatientVO.getPatientIdCard();
        if (StringUtils.isBlank(patientIdCard)) {
            return CommonResult.success(null);
        }
        // 校验身份证格式
        if (!IdCardUtil.validateCard(patientIdCard)) {
            throw exception(INQUIRY_RECORD_ID_CARD_FAIL);
        }
        // 查询参保人信息
        CommonResult<Long> result = inquiryMedicareService.queryPersonInfo(baseInquiryPatientVO);
        boolean isSwitchProductCategory = transmissionConfigApi.isProductChangeQueryCatalog(TenantContextHolder.getTenantId(), baseInquiryPatientVO.getPrescriptionType());
        return CommonResult.success(CheckPatientInfoRespVO.builder().medicareInsuranceId(result == null ? null : result.getData()).isSwitchProductCategory(isSwitchProductCategory).build());
    }
}
