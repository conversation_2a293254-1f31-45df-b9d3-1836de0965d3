package com.xyy.saas.inquiry.patient.controller.admin.patient.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 患者信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryPatientInfoRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8262")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "患者编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "386")
    @ExcelProperty("患者编码")
    private String pref;

    @Schema(description = "患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("患者姓名")
    private String name;

    @Schema(description = "患者性别：1 男 2 女", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者性别：1 男 2 女")
    private Integer sex;

    @Schema(description = "患者年龄", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者年龄")
    private String age;

    @Schema(description = "出生日期")
    @ExcelProperty("出生日期")
    private LocalDateTime birthday;

    @Schema(description = "患者手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者手机号")
    private String mobile;

    @Schema(description = "患者身份证号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者身份证号码")
    private String idCard;

    @Schema(description = "患者来源：0、荷叶问诊   1、智慧脸  2、海典ERP", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("患者来源：0、荷叶问诊   1、智慧脸  2、海典ERP")
    private Integer source;

    @Schema(description = "三方系统患者id", example = "19075")
    @ExcelProperty("三方系统患者id")
    private String thirdUserId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}