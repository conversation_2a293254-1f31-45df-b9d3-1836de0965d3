package com.xyy.saas.inquiry.pharmacist.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 使用 2_005_000_000 段
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/12 20:47
 */
public interface ErrorCodeConstants {

    ErrorCode INQUIRY_PHARMACIST_NOT_EXISTS = new ErrorCode(2_005_000_000, "药师信息不存在或未审核通过");

    ErrorCode INQUIRY_PHARMACIST_OFFLINE_ONLINE = new ErrorCode(2_005_000_001, "药师已{},不可重复操作");

    ErrorCode INQUIRY_PHARMACIST_STATUS_ERROR = new ErrorCode(2_005_000_002, "药师状态{},不可操作");

    ErrorCode INQUIRY_PHARMACIST_MOBILE_EXISTS = new ErrorCode(2_005_000_003, "手机号【{}】已存在");

    ErrorCode INQUIRY_PHARMACIST_IS_AUDIT = new ErrorCode(2_005_000_004, "当前药师状态已审核,不可重复操作");

    ErrorCode INQUIRY_TENANT_PHARMACIST_RELATION_NOT_EXISTS = new ErrorCode(2_005_000_005, "门店药师关系不存在");


    ErrorCode PRESCRIPTION_AUDIT_NOT_EXISTS = new ErrorCode(2_005_000_006, "处方审核记录不存在");

    ErrorCode PRESCRIPTION_AUDIT_DISTRIBUTE_FAIL = new ErrorCode(2_005_000_007, "来晚了,当前处方已被领取,请选择其他处方");

    ErrorCode PRESCRIPTION_AUDIT_RECEIVE_END = new ErrorCode(2_005_000_008, "来晚了,处方已经被领取完了");

    ErrorCode PRESCRIPTION_AUDITED_OR_TIMEOUT = new ErrorCode(2_005_000_009, "处方已审核或已超时,请重新选择");

    ErrorCode INQUIRY_PHARMACIST_CREATE_EXISTS = new ErrorCode(2_005_000_010, "药师信息已存在,不可重复新增");

    ErrorCode INQUIRY_PHARMACIST_AUDIT_PARAM_ERROR = new ErrorCode(2_005_000_011, "审核错误,{}");

    ErrorCode INQUIRY_PHARMACIST_ONLINE_LOGOFF_ERROR = new ErrorCode(2_005_000_012, "已出诊状态下无法注销,请先停诊");

    ErrorCode INQUIRY_PHARMACIST_CHAIN_AUDIT_ERROR = new ErrorCode(2_005_000_013, "当前药师无远程审方资质,请检查药师或总部门店配置,点击确认跳过此张处方");

    ErrorCode INQUIRY_PHARMACIST_REMOTE_PRESCRIPTION_DATA_ERROR = new ErrorCode(2_005_000_014, "远程审方处方不存在或状态异常");

    ErrorCode INQUIRY_PHARMACIST_REMOTE_COST_DATA_ERROR = new ErrorCode(2_005_000_015, "门店远程审方套餐额度不存在或异常");


    // -- 迁移错误码
    ErrorCode INQUIRY_MIGRATION_NOT_EXISTS = new ErrorCode(2_005_000_900, "问诊迁移记录不存在");

    ErrorCode INQUIRY_MIGRATION_QUERY_STORE_ERROR = new ErrorCode(2_005_000_901, "问诊迁移门店查询失败,{}");

    ErrorCode INQUIRY_MIGRATION_UPDATE_STORE_ERROR = new ErrorCode(2_005_000_902, "问诊迁移门店更新旧系统状态失败,{}");

    ErrorCode INQUIRY_MIGRATION_STATUS_OPERATE_ERROR = new ErrorCode(2_005_000_903, "当前问诊迁移门店状态不可操作{}");

    ErrorCode INQUIRY_MIGRATION_OPERATE_ERROR = new ErrorCode(2_005_000_903, "问诊迁移操作失败:{}");

    ErrorCode INQUIRY_MIGRATION_INVALIDATE_ERROR = new ErrorCode(2_005_000_904, "作废迁移失败,新系统中门店id不存在,请检查门店是否创建");

    ErrorCode INQUIRY_MIGRATION_LIMIT_MAX = new ErrorCode(2_005_000_905, "该计划迁移时间内已超过最大迁移数量上限{}条");

    ErrorCode INQUIRY_MIGRATION_RIGHT_NOW_LIMIT_MAX = new ErrorCode(2_005_000_905, "立即迁移超过最大迁移数量上限{}条");


    ErrorCode INQUIRY_MIGRATION_DRUGSTORE_FAIL = new ErrorCode(2_005_000_990, "查询旧荷叶系统门店失败:{}");

    ErrorCode INQUIRY_MIGRATION_HOSPITAL_FAIL = new ErrorCode(2_005_000_990, "旧荷叶系统医院在新系统不存在,{}");

    ErrorCode INQUIRY_MIGRATION_EMPLOYEE_FAIL = new ErrorCode(2_005_000_990, "查询旧荷叶系统门店员工失败,{}");

    ErrorCode INQUIRY_MIGRATION_PHARMACIST_FAIL = new ErrorCode(2_005_000_990, "查询旧荷叶系统门店药师失败,{}");

    ErrorCode INQUIRY_MIGRATION_PATIENT_FAIL = new ErrorCode(2_005_000_990, "查询旧荷叶系统门店患者失败,{}");

    ErrorCode INQUIRY_MIGRATION_PACKAGE_FAIL = new ErrorCode(2_005_000_990, "查询旧荷叶系统门店套餐失败,{}");

    ErrorCode INQUIRY_MIGRATION_PRESCRIPTION_FAIL = new ErrorCode(2_005_000_990, "迁移旧荷叶处方查询失败,{}");

}
