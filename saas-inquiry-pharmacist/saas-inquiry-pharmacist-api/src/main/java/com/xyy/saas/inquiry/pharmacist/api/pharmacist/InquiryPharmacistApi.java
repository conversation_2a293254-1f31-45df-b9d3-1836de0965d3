/**
 * @Author:ch<PERSON><PERSON><PERSON><PERSON>
 * @Date:2024/09/12 20:14
 */
package com.xyy.saas.inquiry.pharmacist.api.pharmacist;

import com.xyy.saas.inquiry.pharmacist.api.pharmacist.dto.InquiryPharmacistDto;

public interface InquiryPharmacistApi {

    /**
     * 根据药师id 查询药师信息
     *
     * @param id
     * @return getPharmacistById
     */
    InquiryPharmacistDto getPharmacistById(Long id);

    /**
     * 根据userId 查询药师信息 存在且审核通过的
     *
     * @param userId
     * @return
     */
    InquiryPharmacistDto getRequiredApprovedPharmacistByUserId(Long userId);


    InquiryPharmacistDto getPharmacistByUserId(Long userId);

    /**
     * 根据药师编码查询药师信息
     *
     * @param pref
     * @return
     */
    InquiryPharmacistDto getPharmacistByPref(String pref);

    /**
     * 定时任务处理药师离线
     */
    void jobHandPharmacistOffline();

    /**
     * 定时任务处理saas迁移
     */
    void jobHandSaasMigration();
}