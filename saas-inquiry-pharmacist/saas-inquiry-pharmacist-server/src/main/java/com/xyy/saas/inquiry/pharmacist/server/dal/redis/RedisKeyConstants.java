package com.xyy.saas.inquiry.pharmacist.server.dal.redis;

import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;

/**
 * System Redis Key 枚举类
 *
 * <AUTHOR>
 */
public interface RedisKeyConstants {

    /**
     * 处方审核领取预占key, KEY 格式： pharmacist:prescription:audit:receive:{pharmacistId}  -  {处方pref}
     */
    String PRESCRIPTION_AUDIT_RECEIVE = "pharmacist:prescription:audit:receive:";

    /**
     * 处方审核锁单号 KEY 格式：pharmacist:prescription:audit:lock:{处方pref} 用于 审核、驳回、超时释放
     */
    String PRESCRIPTION_AUDIT_LOCK = "pharmacist:prescription:audit:lock:";

    /**
     * 门店药师处方池key, KEY 格式：pharmacist:prescription:waiting_review:drugstore_pool_keys:{pharmacistId}  - VALUE数据类型：List<String> 药师处方池keys 默认缓存5s
     */
    String PHARMACIST_PRESCRIPTION_WAITING_REVIEW_DRUGSTORE_POOL_KEYS = "pharmacist:prescription:waiting_review:drugstore_pool_keys:";


    /**
     * 药师定时下线lockKey
     */
    String PHARMACIST_JOB_OFFLINE_LOCK_KEY = "pharmacist:job:offline:lock_key";


    /**
     * 处方门店待审池 KEY 格式：pharmacist:prescription:waiting_review:drugstore_pool:{envTag}:{medicineType}:{tenantId}
     * <p>
     * VALUE数据类型：处方Pref  根据开方时间getOutPrescriptionTime 打分有序ZSet
     *
     * @param envTag       环境
     * @param medicineType 中西药类型
     * @param tenantId     当前处方门店ID
     * @return 门店待审审方池Key
     */
    static String getPrescriptionWaitingReviewDrugstorePoolKey(EnvTagEnum envTag, MedicineTypeEnum medicineType, Long tenantId) {
        return "pharmacist:prescription:waiting_review:drugstore_pool:".concat(envTag.getEnv()).concat(":").concat(medicineType.getCode() + ":").concat(tenantId.toString());
    }


    /**
     * 处方连锁总部待审池 KEY 格式：pharmacist:prescription:waiting_review:chain_pool:{envTag}:{medicineType}:{tenantId}
     * <p>
     * VALUE数据类型：处方Pref  根据开方时间getOutPrescriptionTime 打分有序ZSet
     *
     * @param envTag       环境
     * @param medicineType 中西药类型
     * @param tenantId     当前处方门店ID
     * @return 门店待审审方池Key
     */
    static String getPrescriptionWaitingReviewChainPoolKey(EnvTagEnum envTag, MedicineTypeEnum medicineType, Long tenantId) {
        return "pharmacist:prescription:waiting_review:chain_pool:".concat(envTag.getEnv()).concat(":").concat(medicineType.getCode() + ":").concat(tenantId.toString());
    }


    /**
     * 处方待审平台池 KEY 格式：pharmacist:prescription:waiting_review:platform_pool:{envTag}:{medicineType}:{provinceCode}
     * <p>
     * VALUE数据类型：处方Pref  根据开方时间getOutPrescriptionTime 打分有序ZSet
     *
     * @param envTag       环境
     * @param medicineType 中西药类型
     * @param provinceCode 审方省分,全国默认空
     * @return 平台待审审方池Key
     */
    static String getPrescriptionWaitingReviewPlatformPoolKey(EnvTagEnum envTag, MedicineTypeEnum medicineType, String provinceCode) {
        return "pharmacist:prescription:waiting_review:platform_pool:".concat(envTag.getEnv()).concat(":").concat(medicineType.getCode() + ":").concat(provinceCode);
    }

    /**
     * 处方待审平台远程审方池 KEY 格式：pharmacist:prescription:waiting_review:remote:platform_pool:{envTag}:{medicineType}:{provinceCode}
     *
     * @param envTag       环境
     * @param medicineType 中西药类型
     * @param provinceCode 审方省分,全国默认空
     * @return 处方待审平台远程审方池 KEY
     */
    static String getPrescriptionWaitingReviewRemotePlatformPoolKey(EnvTagEnum envTag, MedicineTypeEnum medicineType, String provinceCode) {
        return "pharmacist:prescription:waiting_review:remote:platform_pool:".concat(envTag.getEnv()).concat(":").concat(medicineType.getCode() + ":").concat(provinceCode);
    }

    /**
     * 药师处方审核 排除key KEY 格式：pharmacist:prescription:review:exclude_key:{pharmacistId} - VALUE数据类型：Set<String> 排除key
     */
    static String getPrescriptionReviewExcludeKey(Long pharmacistId) {
        return "pharmacist:prescription:review:exclude_key:".concat(pharmacistId.toString());
    }


}
