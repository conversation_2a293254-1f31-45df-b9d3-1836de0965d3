package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist;

import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.im.ImEventPushEnum;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.im.api.message.InquiryImMessageApi;
import com.xyy.saas.inquiry.im.api.message.InquiryNotificationPushApi;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.api.user.InquiryImUserApi;
import com.xyy.saas.inquiry.im.enums.PushContentEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pharmacist.server.convert.audit.InquiryPharmacistAuditConvert;
import com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist.InquiryPharmacistConvert;
import com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist.InquiryPharmacistImConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.audit.InquiryPrescriptionAuditDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.mysql.audit.InquiryPrescriptionAuditMapper;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistQueryDto;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistReceiverImUserDto;
import com.xyy.saas.inquiry.util.ThreadPoolManager;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

/**
 * @Author: xucao
 * @DateTime: 2025/4/1 13:53
 * @Description: 药师IM服务实现
 **/
@Service
@Slf4j
public class InquiryPharmacistImServiceImpl implements InquiryPharmacistImService {

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private InquiryImUserApi inquiryImUserApi;

    @DubboReference
    private InquiryImMessageApi inquiryImMessageApi;

    @DubboReference
    private InquiryNotificationPushApi inquiryNotificationPushApi;

    @Resource
    protected AdminUserApi adminUserApi;

    @Resource
    private InquiryPharmacistService inquiryPharmacistService;

    @Resource
    private InquiryPrescriptionAuditMapper prescriptionAuditMapper;

    @Override
    public void sendPrescriptionAuditMsgToPatient(String inquiryPref) {
        sendPrescriptionMsg(inquiryPref, ImEventPushEnum.PRESCRIPTION_AUDIT);
    }

    @Override
    public void sendPrescriptionDrawnFinish(String inquiryPref) {
        sendPrescriptionMsg(inquiryPref, ImEventPushEnum.ISSUE_PRESCRIPTION);
    }

    @Override
    public void sendAuditPrescriptionVideo(PharmacistReceiverImUserDto receiverImAccount, Integer actionType, InquiryPrescriptionRespDTO prescription) {
        InquiryImMessageDto messageDto = InquiryPharmacistImConvert.INSTANCE.convertDefaultMsg(receiverImAccount, actionType, prescription, ImEventPushEnum.PRESCRIPTION_AUDIT_VIDEO);
        sendSystemMsg(messageDto);
    }

    /**
     * 推送处方审核视频通话处理结果给发起方
     *
     * @param receiverImAccount
     * @param handleStatus
     */
    @Override
    public void sendAuditVideoCallHandleResult(PharmacistReceiverImUserDto receiverImUserDto, Integer handleStatus, InquiryPrescriptionRespDTO prescription) {
        InquiryImMessageDto messageDto = InquiryPharmacistImConvert.INSTANCE.convertHandMsg(receiverImUserDto, handleStatus, prescription, ImEventPushEnum.PRESCRIPTION_AUDIT_VIDEO_HAND_RESULT);
        sendSystemMsg(messageDto);
    }

    private void sendSystemMsg(InquiryImMessageDto messageDto) {
        ThreadPoolManager.execute(() -> {
            try {

                // 推送消息
                inquiryImMessageApi.sendSystemMessage(messageDto);
            } catch (Exception e) {
                log.error("发送系统消息失败,原因：{}", e.getMessage(), e);
            }
        });
    }

    private void sendPrescriptionMsg(String inquiryPref, ImEventPushEnum imEventPushEnum) {
        ThreadPoolManager.execute(() -> {
            try {
                // 查询问诊信息
                InquiryRecordDto recordDto = inquiryApi.getInquiryRecord(inquiryPref);
                // 查询商家IM账号
                String patientIm = inquiryImUserApi.getImAccountByUserIdAndClientType(Long.valueOf(recordDto.getCreator()), recordDto.getClientChannelType());
                // 发送消息通知商家处理
                InquiryImMessageDto messageDto = InquiryPharmacistImConvert.INSTANCE.convertPrescriptionAuditMsgToPatient(patientIm, imEventPushEnum, inquiryPref);
                // 发送处方审核消息通知商家端
                inquiryImMessageApi.sendSystemMessage(messageDto);
            } catch (Exception e) {
                log.error("问诊单号：{},发送处方审核消息通知商家失败,原因：{}", inquiryPref, e.getMessage(), e);
            }
        });
    }


    @Override
    public void sendPrescriptionWaitAuditToPharmacist(String prescriptionPref, EnvTagEnum envTagEnum, Long tenantId, MedicineTypeEnum medicineTypeEnum, Integer pharmacistType, Integer pharmacistNature) {
        List<Long> userIds = getPharmacistUserIds(envTagEnum, tenantId, medicineTypeEnum, pharmacistType, pharmacistNature);
        if (userIds == null)
            return;
        ThreadPoolManager.execute(() -> {
            try {
                // 获取IM账号信息
                List<String> imAccountList = inquiryImUserApi.queryUserImAccountList(userIds, ClientChannelTypeEnum.PC);
                if (CollUtil.isEmpty(imAccountList)) {
                    return;
                }
                InquiryImMessageDto messageDto = InquiryPharmacistImConvert.INSTANCE.convertPrescriptionPushWaitAuditMsgToPharmacist(imAccountList, ImEventPushEnum.PRESCRIPTION_PUSH_WAIT_AUDIT, prescriptionPref);
                // 发送处方审核消息通知药师端
                inquiryImMessageApi.batchSendSystemMessage(messageDto);
            } catch (Exception e) {
                log.error("处方单号：{},推送处方待审核语音提醒药师消息失败,原因：{}", prescriptionPref, e.getMessage(), e);
            }
        });
    }

    /**
     * 推送新处方消息给药师
     *
     * @param prescriptionPref
     * @param envTagEnum
     * @param tenantId
     * @param medicineTypeEnum
     * @param pharmacistType
     * @param pharmacistNature
     */
    @Override
    public void notifyForNewPrescription(String prescriptionPref, EnvTagEnum envTagEnum, Long tenantId, MedicineTypeEnum medicineTypeEnum, Integer pharmacistType, Integer pharmacistNature) {
        List<Long> userIds = getPharmacistUserIds(envTagEnum, tenantId, medicineTypeEnum, pharmacistType, pharmacistNature);
        log.info("处方单号：{},推送新处方消息给药师:{}", prescriptionPref,JSON.toJSONString(userIds));
        if (userIds == null){
            return;
        }
        inquiryNotificationPushApi.sendNoticeToMobile(InquiryPharmacistImConvert.INSTANCE.convertNotificationPushDto(userIds, PushContentEnum.PHARMACIST_NEW_ORDER,prescriptionPref));
    }

    /**
     * 获取药师userIds
     * @param envTagEnum
     * @param tenantId
     * @param medicineTypeEnum
     * @param pharmacistType
     * @param pharmacistNature
     * @return
     */
    private List<Long> getPharmacistUserIds(EnvTagEnum envTagEnum, Long tenantId, MedicineTypeEnum medicineTypeEnum, Integer pharmacistType, Integer pharmacistNature) {
        log.info("获取药师userIds:环境:{},租户:{},用药类型:{},药师类型:{},药师性质:{}", envTagEnum, tenantId, medicineTypeEnum, pharmacistType, pharmacistNature);
        // 获取当前门店下的药师
        List<AdminUserRespDTO> userRespDTOS = TenantUtils.execute(tenantId, () -> adminUserApi.getUserListByRoleCodes(Collections.singletonList(RoleCodeEnum.PHARMACIST.getCode())));
        List<Long> userIds = CollectionUtils.convertList(userRespDTOS, AdminUserRespDTO::getId);
        if (CollUtil.isEmpty(userIds)) {
            return null;
        }

        // 筛选药师信息
        PharmacistQueryDto queryDto = InquiryPharmacistConvert.INSTANCE.convertQueryDto(userIds, envTagEnum, medicineTypeEnum, pharmacistType, pharmacistNature);

        List<InquiryPharmacistDO> pharmacistList = inquiryPharmacistService.getPharmacistList(queryDto);

        userIds = CollectionUtils.convertList(pharmacistList, InquiryPharmacistDO::getUserId);
        if (CollUtil.isEmpty(userIds)) {
            return null;
        }
        return userIds;
    }


    @Override
    public void sendPrescriptionReceivePatient(Long userId, InquiryPharmacistDO pharmacist, String inquiryPref) {
        ThreadPoolManager.execute(() -> {
            try {
                // 获取IM账号信息
                String patientIm = inquiryImUserApi.getImAccountByUserIdAndClientType(userId, ClientChannelTypeEnum.APP.getCode());
                // 发送消息通知商家处理
                InquiryImMessageDto messageDto = InquiryPharmacistImConvert.INSTANCE.convertPrescriptionReceive(patientIm, pharmacist, ImEventPushEnum.PRESCRIPTION_AUDIT_RECEIVE, inquiryPref);
                // 发送处方审核领方消息通知商家端
                inquiryImMessageApi.sendSystemMessage(messageDto);
            } catch (Exception e) {
                log.error("问诊单号：{},发送处方审核领方消息通知商家端失败,原因：{}", inquiryPref, e.getMessage(), e);
            }
        });
    }

    public PharmacistReceiverImUserDto getReceiverImAccount(InquiryPrescriptionRespDTO prescription) {
        InquiryRecordDto inquiryRecord = inquiryApi.getInquiryRecord(prescription.getInquiryPref());

        // 接收方是否为药师
        boolean receiverIsPharmacist = ObjectUtil.equal(getLoginUserId(), Long.parseLong(inquiryRecord.getCreator()));

        log.info("getReceiverImAccount,问诊单号:{} 当前登陆人:{},问诊单创建人:{},问诊单渠道:{}", prescription.getInquiryPref(), getLoginUserId(), inquiryRecord.getCreator(), inquiryRecord.getClientChannelType());
        if (receiverIsPharmacist) {
            // 查询领方记录
            InquiryPrescriptionAuditDO auditDO = prescriptionAuditMapper.selectListByCondition(InquiryPharmacistAuditConvert.INSTANCE.convertAuditPageReqVO(prescription.getPref(), AuditorTypeEnum.DRUGSTORE_PHARMACIST)).getFirst();
            log.info("getReceiverImAccount,问诊单号:{} 发送给药师,领方记录:{}", prescription.getInquiryPref(), JSON.toJSONString(auditDO));
            String imAccount = inquiryImUserApi.getImAccountByUserIdAndClientType(auditDO.getAuditorId(), auditDO.getClientChannelType());
            return new PharmacistReceiverImUserDto().setImAccount(imAccount).setPharmacistName(auditDO.getAuditorName());
        }
        String imAccount = inquiryImUserApi.getImAccountByUserIdAndClientType(Long.parseLong(inquiryRecord.getCreator()), inquiryRecord.getClientChannelType());
        return new PharmacistReceiverImUserDto().setImAccount(imAccount).setPatientName(inquiryRecord.getPatientName());
    }

}
