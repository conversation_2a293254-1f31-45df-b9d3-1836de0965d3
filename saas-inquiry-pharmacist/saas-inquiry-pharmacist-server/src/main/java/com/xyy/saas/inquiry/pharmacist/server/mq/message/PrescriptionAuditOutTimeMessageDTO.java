package com.xyy.saas.inquiry.pharmacist.server.mq.message;

import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 处方审核超时检查mq
 *
 * <AUTHOR>
 */
@Builder
@Data
@Accessors(chain = true)
public class PrescriptionAuditOutTimeMessageDTO implements Serializable {

    /**
     * 业务ID - 处方号
     */
    private String pref;

    /**
     * 审方记录ID
     */
    private Long auditRecordId;

    /**
     * 药师id
     */
    private Long pharmacistId;

}
