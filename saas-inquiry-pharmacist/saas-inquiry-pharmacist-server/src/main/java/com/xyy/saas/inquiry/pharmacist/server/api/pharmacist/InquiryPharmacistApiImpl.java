package com.xyy.saas.inquiry.pharmacist.server.api.pharmacist;

import com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.dto.InquiryPharmacistDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist.InquiryPharmacistConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.job.PharmacistOfflineJobService;
import com.xyy.saas.inquiry.pharmacist.server.job.migration.MigrationJobService;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryPharmacistService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @ClassName：InquiryPharmacistApiImpl
 * @Author: xucao
 * @Date: 2024/10/29 13:12
 * @Description: 药师服接口实现类
 */
@DubboService
public class InquiryPharmacistApiImpl implements InquiryPharmacistApi {

    @Resource
    private InquiryPharmacistService inquiryPharmacistService;

    @Resource
    private PharmacistOfflineJobService pharmacistOfflineJobService;

    @Resource
    private MigrationJobService migrationJobService;

    /**
     * 根据guid 查询药师信息
     *
     * @param id
     * @return
     */
    @Override
    public InquiryPharmacistDto getPharmacistById(Long id) {
        final InquiryPharmacistRespVO pharmacist = inquiryPharmacistService.getInquiryPharmacistVo(id);
        return InquiryPharmacistConvert.INSTANCE.convert2VO(pharmacist);
    }

    /**
     * 根据userId 查询药师信息
     *
     * @param userId
     * @return
     */
    @Override
    public InquiryPharmacistDto getRequiredApprovedPharmacistByUserId(Long userId) {
        InquiryPharmacistDO pharmacistDO = inquiryPharmacistService.getRequiredApprovedPharmacistByUserId(userId);
        return InquiryPharmacistConvert.INSTANCE.convertDO2DTO(pharmacistDO);
    }

    @Override
    public InquiryPharmacistDto getPharmacistByUserId(Long userId) {
        InquiryPharmacistDO pharmacistDO = inquiryPharmacistService.getPharmacistByUserId(userId);
        return InquiryPharmacistConvert.INSTANCE.convertDO2DTO(pharmacistDO);
    }

    /**
     * 根据药师编码查询药师信息
     *
     * @param pref
     * @return
     */
    @Override
    public InquiryPharmacistDto getPharmacistByPref(String pref) {
        return InquiryPharmacistConvert.INSTANCE.convertDO2DTO(inquiryPharmacistService.getPharmacistByPref(pref));
    }


    @Override
    public void jobHandPharmacistOffline() {
        pharmacistOfflineJobService.jobHandPharmacistOffline();
    }

    @Override
    public void jobHandSaasMigration() {
        migrationJobService.jobHandSaasMigration();
    }
}
