package com.xyy.saas.inquiry.pharmacist.server.job.migration;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.baomidou.lock.annotation.Lock4j;
import com.xyy.saas.inquiry.enums.migration.MigrationStatusEnum;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.migration.InquiryMigrationDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.mysql.migration.InquiryMigrationMapper;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.migration.InquiryMigrationEvent;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.migration.InquiryMigrationMsgDto;
import com.xyy.saas.inquiry.pharmacist.server.mq.producer.migration.InquiryMigrationProducer;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MigrationJobService {


    @Resource
    private InquiryMigrationProducer inquiryMigrationProducer;

    @Resource
    private InquiryMigrationMapper inquiryMigrationMapper;

    @Resource
    private ConfigApi configApi;

    private static final String TENANT_MIGRATION_DELAY_SECONDS = "system.tenant.migration.delay.seconds";

    /**
     * 问诊迁移延迟间隔时间 默认 5s一条
     */
    private int getMigrationDelaySeconds() {
        return NumberUtil.parseInt(configApi.getConfigValueByKey(TENANT_MIGRATION_DELAY_SECONDS), 5);
    }

    @Lock4j(keys = "'migration:job:lock_key'")
    public void jobHandSaasMigration() {
        try {
            // 查询待迁移机构列表
            List<InquiryMigrationDO> doList = inquiryMigrationMapper.selectMigrationList(
                MigrationStatusEnum.PENDING_MIGRATION.getCode(), LocalDateTime.now());

            int migrationCount = CollUtil.size(doList);
            log.info("定时任务处理老系统迁移 - 迁移机构数:{}", migrationCount);

            if (CollUtil.isEmpty(doList)) {
                return;
            }

            int delaySeconds = getMigrationDelaySeconds();
            int index = 0;
            LocalDateTime now = LocalDateTime.now();

            for (InquiryMigrationDO migrationDO : doList) {
                String organSign = migrationDO.getOrganSign();
                LocalDateTime executeTime = now.plusSeconds((long) index++ * delaySeconds);

                log.info("定时任务处理老系统迁移 - 迁移中,机构:{},时间:{}", organSign, executeTime);

                try {
                    // 发送MQ消息执行迁移
                    InquiryMigrationMsgDto msgDto = InquiryMigrationMsgDto.builder().organSign(organSign).build();

                    inquiryMigrationProducer.sendMessage(InquiryMigrationEvent.builder().msg(msgDto).build(), executeTime);
                } catch (Exception e) {
                    log.error("发送迁移MQ消息失败，机构:{}", organSign, e);
                }
            }
        } catch (Exception e) {
            log.error("定时任务处理老系统迁移时发生异常", e);
        }
    }


}
