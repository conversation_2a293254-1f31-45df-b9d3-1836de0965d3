package com.xyy.saas.inquiry.common.controller.admin.rational.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.common.annotation.rational.RationalImportField;
import com.xyy.saas.inquiry.common.handler.rational.strategy.paramAnalysis.impl.AgeLimitAnalysis;
import com.xyy.saas.inquiry.common.handler.rational.strategy.paramAnalysis.impl.AgeRangeLimitAnalysis;
import com.xyy.saas.inquiry.common.handler.rational.strategy.paramAnalysis.impl.Category6LvAnalysis;
import com.xyy.saas.inquiry.common.handler.rational.strategy.paramAnalysis.impl.HealthLimitAnalysis;
import com.xyy.saas.inquiry.common.handler.rational.strategy.paramAnalysis.impl.IngredientLimitAnalysis;
import com.xyy.saas.inquiry.common.handler.rational.strategy.paramAnalysis.impl.SexLimitAnalysis;
import com.xyy.saas.inquiry.common.handler.rational.strategy.paramAnalysis.impl.WomenLimitAnalysis;
import com.xyy.saas.inquiry.enums.rational.RationalStatusEnum;
import com.xyy.saas.inquiry.pojo.excel.ImportExcelVoDto;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * @Author: xucao
 * @DateTime: 2025/8/4 19:46
 * @Description: 限制类商品导出vo
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class InquiryRationalProductImportVo extends ImportExcelVoDto {

    private Integer index;

    @ExcelProperty(value = "通用名", index = 0)
    @NotBlank(message = "通用名必填")
    @Length(max = 50, message = "通用名最大长度50位")
    private String commonName;


    @ExcelProperty(value = "性别用药", index = 1)
    @RationalImportField(checkClass = SexLimitAnalysis.class)
    private String sexLimitStr;


    @ExcelProperty(value = "年龄段限制", index = 2)
    @RationalImportField(checkClass = AgeLimitAnalysis.class)
    private String ageRule;


    @ExcelProperty(value = "年龄限制", index = 3)
    @RationalImportField(checkClass = AgeRangeLimitAnalysis.class)
    private String ageRangeRule;

    @ExcelProperty(value = "肝肾异常限制", index = 4)
    @RationalImportField(checkClass = HealthLimitAnalysis.class)
    private String healthRule;


    @ExcelProperty(value = "妊娠哺乳期限制", index = 5)
    @RationalImportField(checkClass = WomenLimitAnalysis.class)
    private String womenRule;

    /**
     * 成分 - 单独校验
     */
    @ExcelProperty(value = "成分", index = 6)
    @RationalImportField(checkClass = IngredientLimitAnalysis.class)
    private String ingredientRule;

    /**
     * 六级分类 - 单独校验
     */
    @ExcelProperty(value = "六级分类", index = 7)
    @RationalImportField(checkClass = Category6LvAnalysis.class)
    private String categoryPath;

    /**
     * 医保要求
     */
    @ExcelProperty(value = "医保要求", index = 8)
    @Length(max = 1000, message = "医保要求最大长度1000位")
    private String medicareRemark;


    /**
     * 是否启用 - 单独校验
     */
    @ExcelProperty(value = "是否启用", index = 9)
    @Size(max = 64, message = "是否启用超出最大长度2限制")
    private String status;


    @Override
    public void valid() {
        if (RationalStatusEnum.valueOfName(status) == null) {
            this.errMsg += "是否启用仅支持填写“启用”/“禁用”";
        }
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public void setCommonName(String commonName) {
        this.commonName = commonName;
    }

    public void setSexLimitStr(String sexLimitStr) {
        this.sexLimitStr = sexLimitStr;
    }

    public void setAgeRule(String ageRule) {
        this.ageRule = ageRule;
    }

    public void setAgeRangeRule(String ageRangeRule) {
        this.ageRangeRule = ageRangeRule;
    }

    public void setHealthRule(String healthRule) {
        this.healthRule = healthRule;
    }

    public void setWomenRule(String womenRule) {
        this.womenRule = womenRule;
    }

    public void setIngredientRule(String ingredientRule) {
        this.ingredientRule = ingredientRule;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public void setMedicareRemark(String medicareRemark) {
        this.medicareRemark = medicareRemark;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
