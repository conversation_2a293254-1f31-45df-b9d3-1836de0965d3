package com.xyy.saas.inquiry.common.api.rational;


import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageDrugRuleDto;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageRuleDrugQueryDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.UsageAndDosageReviewDto;

/**
 * 用法用量审查api
 *
 * <AUTHOR>
 * @Date 9/2/24 4:57 PM
 */
public interface SaasUsageAndDosageReviewApi {

    CommonResult<Boolean> save(UsageAndDosageReviewDto usageAndDosageReviewDto);

    CommonResult<PageResult<UsageAndDosageReviewDto>> pageQuery(UsageAndDosageReviewDto usageAndDosageReviewDto);

    CommonResult<Boolean> updateEnableStatus(UsageAndDosageReviewDto usageAndDosageReviewDto);

    CommonResult<Boolean> deleteById(UsageAndDosageReviewDto usageAndDosageReviewDto);

    CommonResult<UsageAndDosageDrugRuleDto> queryRuleByDrug(UsageAndDosageRuleDrugQueryDto usageAndDosageRuleDrugQueryDto);
}
