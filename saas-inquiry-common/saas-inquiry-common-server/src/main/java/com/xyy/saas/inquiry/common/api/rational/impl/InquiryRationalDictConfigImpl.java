package com.xyy.saas.inquiry.common.api.rational.impl;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.xyy.saas.inquiry.common.api.rational.InquiryRationalDictConfigApi;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalCategoryDetailDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalComponentDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalDictConfigDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalProductCommonDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRationalCategoryVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRationalDictConfigVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.RationalDictConfigVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasCategoryTreeVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasCategoryVo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalCategoryDetail;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalDictConfig;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalProduct;
import com.xyy.saas.inquiry.common.service.productmid.InquiryMidCategoryApiImpl;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalCategoryDetailService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalDictConfigService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalProductService;
import com.xyy.saas.inquiry.common.util.BeanUtil;
import com.xyy.saas.inquiry.enums.rational.RationalDictSysType;
import com.xyy.saas.inquiry.enums.rational.RationalDictType;
import com.xyy.saas.inquiry.pojo.excel.ImportReqDto;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import com.xyy.saas.inquiry.util.excel.EasyExcelUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author:chenxiaoyi
 * @Date:2023/11/06 18:45
 */
@Slf4j
@Service
public class InquiryRationalDictConfigImpl implements InquiryRationalDictConfigApi {

    @Resource
    private InquiryRationalDictConfigService inquiryRationalDictConfigService;


    @Resource
    private InquiryRationalCategoryDetailService inquiryRationalCategoryDetailService;

    @Resource
    private InquiryRationalProductService productService;

    @Resource
    private InquiryMidCategoryApiImpl inquiryMidCategoryApi;

    @Autowired
    private EasyExcelUtil easyExcelUtil;

    // 六级分类限制上限，默认20个
    @Value("${rational.category.limit:20}")
    private Integer rationalCategoryLimit;

    @Override
    public PageResult<InquiryRationalDictConfigVo> queryRationalDictConfig(RationalDictConfigVO dictConfigVO) {

        InquiryRationalDictConfigDto dictConfigDto = BeanUtil.copyToBean(dictConfigVO, InquiryRationalDictConfigDto.class);
        PageResult<InquiryRationalDictConfig> pageResult = inquiryRationalDictConfigService.pageQueryByCondition(dictConfigDto);
        if (pageResult == null || CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }
        return new PageResult<>(BeanUtil.copyListProperties(pageResult.getList(), InquiryRationalDictConfigVo.class), pageResult.getTotal());
    }

    // ************************** 成分 ******************


    @Override
    public CommonResult<List<InquiryRationalDictConfigVo>> getDefaultAllergy() {
        // 获取默认成分
        List<InquiryRationalDictConfig> rationalDictConfigs = inquiryRationalDictConfigService.queryByCondition(InquiryRationalDictConfigDto.builder()
            .type(RationalDictType.COMPONENT.getType()).sysType(RationalDictSysType.SYSTEM.getType()).build());

        if (CollectionUtils.isNotEmpty(rationalDictConfigs)) {
            return CommonResult.success(BeanUtil.copyListProperties(rationalDictConfigs, InquiryRationalDictConfigVo.class));
        }
        // 数据为空返回默认 [青霉素,头孢]
        return CommonResult.success(Stream.of(InquiryRationalDictConfigVo.builder().type(RationalDictType.COMPONENT.getType()).name("青霉素").id(1).build(),
            InquiryRationalDictConfigVo.builder().type(RationalDictType.COMPONENT.getType()).name("头孢").id(2).build()).collect(toList()));
    }


    @Override
    public CommonResult<List<InquiryRationalDictConfigVo>> getRecommendAllergy() {
        // 获取系统成分
        List<InquiryRationalDictConfig> systems = inquiryRationalDictConfigService.queryByCondition(InquiryRationalDictConfigDto.builder()
            .type(RationalDictType.COMPONENT.getType()).sysType(RationalDictSysType.SYSTEM.getType()).build());
        // 获取推荐成分
        List<InquiryRationalDictConfig> recommendeds = inquiryRationalDictConfigService.queryByCondition(InquiryRationalDictConfigDto.builder()
            .type(RationalDictType.COMPONENT.getType()).sysType(RationalDictSysType.RECOMMENDED.getType()).build());

        systems.addAll(recommendeds);

        return CommonResult.success(BeanUtil.copyListProperties(systems, InquiryRationalDictConfigVo.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> irritabilityCreateOrUpdate(RationalDictConfigVO dictConfigDto) {
        List<InquiryRationalDictConfig> rationalDictConfigs = inquiryRationalDictConfigService.queryByCondition(
            InquiryRationalDictConfigDto.builder().type(RationalDictType.COMPONENT.getType()).names(Collections.singletonList(dictConfigDto.getName())).build());
        if (dictConfigDto.getId() == null) {
            if (CollectionUtils.isNotEmpty(rationalDictConfigs)) {
                return CommonResult.error("新增失败,该成分已存在!");
            }
            dictConfigDto.setCreateUser(LoginUserContextUtils.getLoginUserId().toString());
            dictConfigDto.setValue(dictConfigDto.getName());
            InquiryRationalDictConfig rationalDictConfig = new InquiryRationalDictConfig();
            BeanUtils.copyProperties(dictConfigDto, rationalDictConfig);
            inquiryRationalDictConfigService.insertSelective(rationalDictConfig);
            return CommonResult.success(null);
        }

        InquiryRationalDictConfig inquiryRationalDictConfig = inquiryRationalDictConfigService.queryById(dictConfigDto.getId());
        if (inquiryRationalDictConfig == null || !Objects.equals(inquiryRationalDictConfig.getType(), RationalDictType.COMPONENT.getType())) {
            return CommonResult.error("编辑失败,该成分不存在,请刷新后重试");
        }

        InquiryRationalDictConfig rationalDictConfig = rationalDictConfigs.stream().filter(r -> !Objects.equals(r.getId(), dictConfigDto.getId())).findAny().orElse(null);
        if (rationalDictConfig != null) {
            return CommonResult.error("编辑失败,该成分已存在!");
        }

        BeanUtils.copyProperties(dictConfigDto, inquiryRationalDictConfig);
        inquiryRationalDictConfig.setValue(dictConfigDto.getName());
        inquiryRationalDictConfigService.updateSelective(inquiryRationalDictConfig);
        return CommonResult.success(null);
    }

    @Override
    public ImportResultDto irritabilityImportByExcel(ImportReqDto importReqDto) {

        importReqDto.setExcelName("成分导入").setLimitCount(5000);

        return easyExcelUtil.importData(importReqDto, InquiryRationalComponentDto.class, inquiryRationalDictConfigService::irritabilityImportByExcel);
    }

    // ************************** 类别 ******************

    /**
     * 1.分页查类别主表 2.查类别：分类详情表 3.查分类的父级链路 4.组装到每个类别中
     *
     * @param dictConfigDto 查询对象
     * @return
     */
    @Override
    public PageResult<InquiryRationalDictConfigVo> queryCategoryRationalDictConfig(RationalDictConfigVO dictConfigDto) {
        // 分页 - 查询类别主表
        PageResult<InquiryRationalDictConfig> categoryPageInfo = inquiryRationalDictConfigService.pageQueryByCondition(BeanUtil.copyToBean(dictConfigDto, InquiryRationalDictConfigDto.class));

        List<InquiryRationalDictConfig> list = categoryPageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return PageResult.empty();
        }
        // 查询 类别对应得分类详情表
        List<InquiryRationalCategoryDetail> categoryDetails = inquiryRationalCategoryDetailService.queryByCondition(InquiryRationalCategoryDetailDto
            .builder().categoryIds(list.stream().map(InquiryRationalDictConfig::getId).collect(toList())).yn(CommonStatusEnum.ENABLE.getStatus()).build());
        // < 类别x,List<分类末级id> >
        Map<Integer, List<InquiryRationalCategoryDetail>> categoryMap = categoryDetails.stream().collect(groupingBy(InquiryRationalCategoryDetail::getCategoryId));

        // 查询中台六级分类上游链路
        List<Integer> classifyIds = categoryDetails.stream().map(InquiryRationalCategoryDetail::getClassifyId).collect(toList());
        Map<Integer, List<SaasCategoryVo>> parentPathMap = inquiryMidCategoryApi.queryCategoryParentPathByIds(classifyIds);

        List<InquiryRationalDictConfigVo> configVoList = BeanUtil.copyListProperties(list, InquiryRationalDictConfigVo.class);
        // 组装中台六级分类
        for (InquiryRationalDictConfigVo configVo : configVoList) {
            List<SaasCategoryVo> categoryVos = new ArrayList<>();
            List<InquiryRationalCategoryDetail> categoryDetailList = categoryMap.get(configVo.getId());
            if (CollectionUtils.isEmpty(categoryDetailList)) {
                continue;
            }
            categoryDetailList.stream().sorted(Comparator.comparing(InquiryRationalCategoryDetail::getId)).forEach(categoryDetail -> {
                SaasCategoryVo saasCategoryVo = new SaasCategoryVo();
                saasCategoryVo.setId(categoryDetail.getClassifyId());
                saasCategoryVo.setPathName(parentPathMap.getOrDefault(categoryDetail.getClassifyId(), new ArrayList<>()).stream().map(SaasCategoryVo::getTitle).collect(joining(">")));
                categoryVos.add(saasCategoryVo);
            });

            configVo.setCategorys(categoryVos);
        }
        return new PageResult<>(configVoList, categoryPageInfo.getTotal());
    }

    @Override
    public PageResult<InquiryRationalDictConfigVo> categoryQuerySelectors(RationalDictConfigVO dictConfigDto) {
        final PageResult<InquiryRationalDictConfig> pageResult = inquiryRationalDictConfigService.pageQueryByCondition(BeanUtil.copyToBean(dictConfigDto, InquiryRationalDictConfigDto.class));
        if (pageResult == null || CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }
        return new PageResult<>(BeanUtil.copyListProperties(pageResult.getList(), InquiryRationalDictConfigVo.class), pageResult.getTotal());
    }

    @Override
    public InquiryRationalDictConfigVo queryCategoryRationalDictDetail(Integer id) {
        if (id == null) {
            return null;
        }
        InquiryRationalDictConfig inquiryRationalDictConfig = inquiryRationalDictConfigService.queryById(id);
        if (inquiryRationalDictConfig == null) {
            return null;
        }

        InquiryRationalDictConfigVo dictConfigVo = new InquiryRationalDictConfigVo();
        BeanUtils.copyProperties(inquiryRationalDictConfig, dictConfigVo);

        List<InquiryRationalCategoryDetail> categoryDetails = inquiryRationalCategoryDetailService.queryByCondition(
            InquiryRationalCategoryDetailDto.builder().categoryId(inquiryRationalDictConfig.getId()).yn(CommonStatusEnum.ENABLE.getStatus()).build());

        if (CollectionUtils.isNotEmpty(categoryDetails)) {
            // 查询组装中台六级分类
            Map<Integer, List<SaasCategoryVo>> parentPathByIds = inquiryMidCategoryApi.queryCategoryParentPathByIds(categoryDetails.stream().map(InquiryRationalCategoryDetail::getClassifyId).collect(toList()));

            final ArrayList<SaasCategoryVo> saasCategoryVos = new ArrayList<>();
            for (InquiryRationalCategoryDetail detail : categoryDetails) {
                List<SaasCategoryVo> categoryVos = parentPathByIds.get(detail.getClassifyId());
                if (CollectionUtils.isEmpty(categoryVos)) {
                    continue;
                }
                SaasCategoryVo categoryVo = new SaasCategoryVo();
                categoryVo.setId(detail.getClassifyId());
                categoryVo.setPathName(categoryVos.stream().map(SaasCategoryVo::getTitle).collect(Collectors.joining(">")));
                saasCategoryVos.add(categoryVo);
            }

//            List<SaasCategoryVo> categoryVos = parentPathByIds.entrySet().stream().map((entry) -> {
//                SaasCategoryVo categoryVo = new SaasCategoryVo();
//                categoryVo.setId(entry.getKey());
//                categoryVo.setPathName(entry.getValue().stream().map(SaasCategoryVo::getTitle).collect(Collectors.joining(">")));
//                return categoryVo;
//            }).collect(toList());

            dictConfigVo.setCategorys(saasCategoryVos);
        } else {
            dictConfigVo.setCategorys(new ArrayList<>());
        }

        return dictConfigVo;
    }


    @Override
    public InquiryRationalDictConfigVo categoryQueryCommonName(Integer id) {
        InquiryRationalDictConfigVo dictConfigVo = queryCategoryRationalDictDetail(id);
        if (dictConfigVo == null) {
            return null;
        }
        // 根据分类 查询组装商品数据
        List<SaasCategoryVo> categorys = dictConfigVo.getCategorys();

        if (CollectionUtils.isEmpty(categorys)) {
            return dictConfigVo;
        }

        List<Integer> classifyIds = categorys.stream().map(SaasCategoryVo::getId).collect(toList());
        // 找到六级分类下游,批量查商品
        Map<Integer, List<SaasCategoryVo>> childPathMap = inquiryMidCategoryApi.queryCategoryChildPathByIds(classifyIds);

        for (SaasCategoryVo category : categorys) {
            List<String> childLvIds = childPathMap.get(category.getId()).stream().map(SaasCategoryVo::getId).map(String::valueOf).collect(toList());
            if (CollectionUtils.isNotEmpty(childLvIds)) {
                // 查商品
                List<InquiryRationalProduct> products = productService.queryByCondition(InquiryRationalProductCommonDto.builder().categoryLv6Ids(childLvIds).yn(CommonStatusEnum.ENABLE.getStatus()).build());
                category.setProductName(products.stream().map(InquiryRationalProduct::getCommonName).collect(Collectors.joining("、")));
            }
        }
        return dictConfigVo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> categoryCreateOrUpdate(RationalDictConfigVO dictConfigDto) {

        if (CollectionUtils.isNotEmpty(dictConfigDto.getCategorys()) && dictConfigDto.getCategorys().size() > rationalCategoryLimit) {
            return CommonResult.error("一个类别最多添加" + rationalCategoryLimit + "个六级分类");
        }
        List<InquiryRationalDictConfig> rationalDictConfigs = inquiryRationalDictConfigService.queryByCondition(InquiryRationalDictConfigDto
            .builder()
            .type(RationalDictType.CATEGORY.getType())
            .names(Collections.singletonList(dictConfigDto.getName()))
            .build());

        InquiryRationalDictConfig dictConfig = new InquiryRationalDictConfig();
        BeanUtils.copyProperties(dictConfigDto, dictConfig);
        dictConfig.setValue(dictConfigDto.getName());

        if (dictConfigDto.getId() == null) {
            if (CollectionUtils.isNotEmpty(rationalDictConfigs)) {
                return CommonResult.error("新增失败,该类别已存在!");
            }
            dictConfig.setCreator(dictConfigDto.getUpdateUser());
            inquiryRationalDictConfigService.insertSelective(dictConfig);
        } else {
            InquiryRationalDictConfig rationalDictConfig = rationalDictConfigs.stream().filter(r -> !Objects.equals(r.getId(), dictConfigDto.getId())).findAny().orElse(null);
            if (rationalDictConfig != null) {
                return CommonResult.error("编辑失败,该类别已存在!");
            }
            InquiryRationalDictConfig inquiryRationalDictConfig = inquiryRationalDictConfigService.queryById(dictConfigDto.getId());
            if (inquiryRationalDictConfig == null || !Objects.equals(inquiryRationalDictConfig.getType(), RationalDictType.CATEGORY.getType())) {
                return CommonResult.error("编辑失败,该类别不存在,请刷新后重试");
            }

            BeanUtils.copyProperties(dictConfig, inquiryRationalDictConfig, "id");
            inquiryRationalDictConfigService.updateSelective(inquiryRationalDictConfig);
            // 删除明细
            List<InquiryRationalCategoryDetail> categoryDetails = inquiryRationalCategoryDetailService.queryByCondition(InquiryRationalCategoryDetailDto.builder().categoryId(dictConfigDto.getId()).build());
            if (CollectionUtils.isNotEmpty(categoryDetails)) {
                inquiryRationalCategoryDetailService.deleteByIds(categoryDetails.stream().map(InquiryRationalCategoryDetail::getId).collect(toList()));
            }
        }

        if (CollectionUtils.isNotEmpty(dictConfigDto.getCategorys())) {
            // 新增明细
            List<InquiryRationalCategoryDetail> categoryDetails = dictConfigDto.getCategorys().stream().map(c -> {
                InquiryRationalCategoryDetail detail = new InquiryRationalCategoryDetail();
                detail.setCategoryId(dictConfig.getId());
                detail.setClassifyId(c);
                return detail;
            }).collect(toList());
            inquiryRationalCategoryDetailService.insertBatch(categoryDetails);
        }

        return CommonResult.success(null);
    }


    @Override
    public ImportResultDto categoryImportByExcel(ImportReqDto importReqDto) {

        importReqDto.setExcelName("类别导入").setLimitCount(5000);

        return easyExcelUtil.importData(importReqDto, InquiryRationalCategoryVo.class, inquiryRationalDictConfigService::categoryImportByExcel);
    }


    @Override
    public PageResult<SaasCategoryVo> queryCategoryPathByNameFuzzy(SaasCategoryVo saasCategoryVo) {
        PageResult<List<SaasCategoryVo>> pageInfo = inquiryMidCategoryApi.queryCategoryPathByNameFuzzy(saasCategoryVo.getTitle(), saasCategoryVo.getPageNo(), saasCategoryVo.getPageSize());
        final List<List<SaasCategoryVo>> list = pageInfo.getList();
        pageInfo.setList(null);
        PageResult<SaasCategoryVo> voPageInfo = new PageResult<>();
        BeanUtils.copyProperties(pageInfo, voPageInfo);

        if (CollectionUtils.isEmpty(list)) {
            voPageInfo.setList(new ArrayList<>());
            return voPageInfo;
        }

        List<SaasCategoryVo> saasCategoryVos = list.stream().map(c -> {
            SaasCategoryVo categoryVo = new SaasCategoryVo();
            String pathName = c.stream().sorted(Comparator.comparing(SaasCategoryVo::getLevel)).map(SaasCategoryVo::getTitle).collect(joining(">"));
            Integer id = c.stream().sorted(Comparator.comparing(SaasCategoryVo::getLevel).reversed()).findFirst().get().getId();
            categoryVo.setPathName(pathName);
            categoryVo.setId(id);
            categoryVo.setParent(c);
            return categoryVo;
        }).collect(toList());
        voPageInfo.setList(saasCategoryVos);
        return voPageInfo;
    }

    @Override
    public CommonResult<List<SaasCategoryVo>> categoryQuery6LvList(SaasCategoryVo saasCategoryVo) {
        List<SaasCategoryVo> categoryVos = inquiryMidCategoryApi.queryCategoryByParentId(saasCategoryVo.getId());
        if (CollectionUtils.isEmpty(categoryVos)) {
            return CommonResult.error("查询中台六级分类数据为空");
        }
        return CommonResult.success(categoryVos);
    }


    @Override
    public CommonResult<SaasCategoryTreeVo> categoryQuery6LvByProductName(SaasCategoryVo saasCategoryVo) {
        List<List<SaasCategoryVo>> categoryByProductName = inquiryMidCategoryApi.queryProductCategoryByProductName(saasCategoryVo.getProductName());
        if (CollectionUtils.isEmpty(categoryByProductName)) {
            return CommonResult.success(null);
        }

        // 传入一组六级分类,组装树结构
        List<Integer> categoryPathIds = categoryByProductName.get(0).stream().sorted(Comparator.comparing(SaasCategoryVo::getLevel)).map(SaasCategoryVo::getId).collect(toList());
        SaasCategoryTreeVo categoryTreeVo = inquiryMidCategoryApi.queryCategoryBuildTree(categoryPathIds);
        return CommonResult.success(categoryTreeVo);
    }

    @Override
    public CommonResult<SaasCategoryTreeVo> query6LvLinkByEndId(SaasCategoryVo saasCategoryVo) {

        Map<Integer, List<SaasCategoryVo>> saasCategoryVoMap = inquiryMidCategoryApi.queryCategoryParentPathByIds(Lists.newArrayList(saasCategoryVo.getId()));

        if (MapUtils.isEmpty(saasCategoryVoMap)) {
            return CommonResult.success(new SaasCategoryTreeVo());
        }

        List<Integer> categoryPathIds = saasCategoryVoMap.values().stream().findFirst().orElse(Lists.newArrayList()).stream().sorted(Comparator.comparing(SaasCategoryVo::getLevel)).map(SaasCategoryVo::getId).collect(toList());

        return CommonResult.success(inquiryMidCategoryApi.queryCategoryBuildTree(categoryPathIds));
    }

}
