package com.xyy.saas.inquiry.common.controller.admin.rational.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Schema(description = "管理后台 - 商品诊断关联 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductDiagnosisRelationRespVO extends BaseDto {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "13662")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "诊断编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("诊断编码")
    private String diagnosisCode;

    private String diagnosisName;

    private String showName;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("商品名称")
    private String productName;

    @Schema(description = "频率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("频率")
    private Integer cnt;

    @Schema(description = "排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序")
    private Integer rn;

    @Schema(description = "权重", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("权重")
    private BigDecimal weight;

    @Schema(description = "是否有效 0 有效 1 无效")
    @ExcelProperty("是否有效 0 有效 1 无效")
    private Integer yn;

}