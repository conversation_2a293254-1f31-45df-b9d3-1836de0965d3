package com.xyy.saas.inquiry.common.api.rational.impl;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.common.api.rational.InquiryProductRuleSearchApi;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryProductResponseDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryProductRuleDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryProductRuleDtoSearchParam;
import com.xyy.saas.inquiry.common.util.SwitchUtil;
import com.xyy.saas.inquiry.enums.rational.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.ByQueryResponse;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchAggregations;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.NativeQueryBuilder;
import org.springframework.data.domain.PageRequest;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.WildcardQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.ExistsQuery;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsAggregate;
import co.elastic.clients.elasticsearch._types.aggregations.StringTermsBucket;
import co.elastic.clients.elasticsearch._types.aggregations.LongTermsAggregate;
import co.elastic.clients.elasticsearch._types.aggregations.LongTermsBucket;
import co.elastic.clients.elasticsearch._types.aggregations.TopHitsAggregate;
import co.elastic.clients.json.JsonData;
import co.elastic.clients.elasticsearch._types.FieldValue;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InquiryProductRuleSearchImpl implements InquiryProductRuleSearchApi<InquiryProductRuleDto> {


    private static final int MAX_SIZE = 100000;

    @Autowired
    private ElasticsearchOperations elasticsearchOperations;

    @Override
    public Boolean indexCreate() {
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_PRODUCT_RULE.getIndexName());
            
            // 设置索引的settings
            Document settings = Document.create()
                    .append("number_of_shards", 1)
                    .append("number_of_replicas", 1)
                    .append("refresh_interval", "1s")
                    .append("max_result_window", 1000000);
            
            // 设置索引的mappings
            Document mapping = Document.create()
                    .append("properties", Document.create()
                            .append("id", Document.create().append("type", "integer"))
                            .append("productCommonId", Document.create().append("type", "integer"))
                            .append("commonName", Document.create().append("type", "keyword"))
                            .append("categoryLv6Id", Document.create().append("type", "keyword"))
                            .append("limitDrugType", Document.create().append("type", "integer"))
                            .append("param", Document.create().append("type", "keyword"))
                            .append("caution", Document.create().append("type", "integer"))
                            .append("rangeBegin", Document.create().append("type", "integer"))
                            .append("rangeEnd", Document.create().append("type", "integer"))
                            .append("status", Document.create().append("type", "byte"))
                            .append("yn", Document.create().append("type", "byte"))
                            .append("createUser", Document.create().append("type", "keyword"))
                            .append("updateUser", Document.create().append("type", "keyword"))
                            .append("createTime", Document.create().append("format", "yyyy-MM-dd HH:mm:ss").append("type", "date"))
                            .append("updateTime", Document.create().append("format", "yyyy-MM-dd HH:mm:ss").append("type", "date"))
                    );
            
            return elasticsearchOperations.indexOps(indexCoordinates).create(settings, mapping);
        } catch (Exception e) {
            log.error("创建索引失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public Map<String, Object> getMapping() {
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_PRODUCT_RULE.getIndexName());
            return elasticsearchOperations.indexOps(indexCoordinates).getMapping();
        } catch (Exception e) {
            log.error("获取索引映射失败", e);
            return null;
        }
    }

    @Override
    public Boolean indexDelete() {
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_PRODUCT_RULE.getIndexName());
            return elasticsearchOperations.indexOps(indexCoordinates).delete();
        } catch (Exception e) {
            log.error("删除索引失败", e);
            return false;
        }
    }

    @Override
    public Boolean indexExists() {
        try {
            IndexCoordinates indexCoordinates = IndexCoordinates.of(RemoteIndexEnum.YKQ_REMOTE_PRODUCT_RULE.getIndexName());
            return elasticsearchOperations.indexOps(indexCoordinates).exists();
        } catch (Exception e) {
            log.error("检查索引是否存在失败", e);
            return false;
        }
    }

    @Override
    public boolean add(InquiryProductRuleDto productRule) {
        try {
            log.info("保存商品规则的参数：{}", JSON.toJSONString(productRule));
            IndexCoordinates indexCoordinates = IndexCoordinates.of(IndexAliasEnum.YKQ_REMOTE_PRODUCT_RULE.getReadAlias());
            
            InquiryProductRuleDto savedDoc = elasticsearchOperations.save(productRule, indexCoordinates);
            log.info("保存ES成功，文档ID：{}", savedDoc.getId());
            return savedDoc != null;
        } catch (Exception e) {
            log.error("保存商品规则失败", e);
            return false;
        }
    }

    @Override
    public boolean update(InquiryProductRuleDto productRule) {
        try {
            log.info("修改商品规则的参数：{}", JSON.toJSONString(productRule));
            IndexCoordinates indexCoordinates = IndexCoordinates.of(IndexAliasEnum.YKQ_REMOTE_PRODUCT_RULE.getReadAlias());
            
            InquiryProductRuleDto updatedDoc = elasticsearchOperations.save(productRule, indexCoordinates);
            log.info("修改ES成功，文档ID：{}", updatedDoc.getId());
            return updatedDoc != null;
        } catch (Exception e) {
            log.error("修改商品规则失败", e);
            return false;
        }
    }

    @Override
    public boolean batchInsert(List<InquiryProductRuleDto> datas) {
        try {
            log.info("批量新增商品规则的参数：{}", JSON.toJSONString(datas));
            IndexCoordinates indexCoordinates = IndexCoordinates.of(IndexAliasEnum.YKQ_REMOTE_PRODUCT_RULE.getReadAlias());
            
            Iterable<InquiryProductRuleDto> savedDocs = elasticsearchOperations.save(datas, indexCoordinates);
            log.info("批量新增ES成功，保存文档数量：{}", datas.size());
            return savedDocs != null;
        } catch (Exception e) {
            log.error("批量新增商品规则失败", e);
            return false;
        }
    }

    @Override
    public boolean batchDel(List<Integer> ids) {
        try {
            log.info("删除商品规则的参数：{}", JSON.toJSONString(ids));
            IndexCoordinates indexCoordinates = IndexCoordinates.of(IndexAliasEnum.YKQ_REMOTE_PRODUCT_RULE.getReadAlias());
            
            List<String> stringIds = ids.stream().map(String::valueOf).collect(Collectors.toList());
            
            // 构建 ids 查询
            co.elastic.clients.elasticsearch._types.query_dsl.Query idsQuery = 
                co.elastic.clients.elasticsearch._types.query_dsl.Query.of(q -> q
                    .ids(i -> i.values(stringIds))
                );
            
            NativeQuery query = new NativeQueryBuilder()
                .withQuery(idsQuery)
                .build();


            ByQueryResponse deletedResult = elasticsearchOperations.delete(query, InquiryProductRuleDto.class, indexCoordinates);
            log.info("批量删除ES成功，删除数量：{}，耗时：{}ms", deletedResult.getDeleted(), deletedResult.getTook());
            return deletedResult.getDeleted() > 0;
        } catch (Exception e) {
            log.error("批量删除商品规则失败", e);
            return false;
        }
    }

    @Override
    public CommonResult<PageResult<InquiryProductResponseDto>> pageSearchInquiryProductRuleAgg(InquiryProductRuleDtoSearchParam param) {
        //添加默认分页信息
        if(param.getPageNo()<1 || param.getPageSize()<1){
            param.setPageNo(1);
            param.setPageSize(10000);
        }
        //获取商品规则查询参数
        co.elastic.clients.elasticsearch._types.query_dsl.Query typeQuery = getTypeSearchBuild(param);
        List<Integer> typeHitId = new ArrayList<>();
        if(param.getExpectBucketNum() > 0){
            typeHitId = typeAgg(typeQuery,param);
        }
        //成分为全部匹配时，过滤结果
        if(CollectionUtils.isNotEmpty(typeHitId) && param.getIngredientQueryType() == InquiryProductRuleConstant.MATCH_ALL){
            //过滤商品id
            typeHitId = filterHitId(typeHitId,param.getIngredientList());
        }
        //获取商品基础参数查询
        List comIds = Optional.ofNullable(param.getProductCommonIds()).orElse(new ArrayList<>());
        comIds.addAll(typeHitId);
        param.setProductCommonIds(comIds);
        co.elastic.clients.elasticsearch._types.query_dsl.Query commonQuery = getCommonSearchBuild(param);
        if(param.getExpectBucketNum() >0 && typeHitId.size() == 0){
            //说明条件未命中结果，直接返回
            return CommonResult.success(PageResult.empty());
        }
        List<Integer> result = prodAgg(commonQuery);
        //分页
        List<InquiryProductResponseDto> dtos = Lists.newArrayList();
        int endSet = getOffsetEnd(param,result.size());
        int offset = (param.getPageNo() - 1) * param.getPageSize();
        for(int i= offset;i< endSet;i++){
            InquiryProductResponseDto dto = new InquiryProductResponseDto();
            dto.setProductCommonId(result.get(i));
            dtos.add(dto);
        }
        PageResult page = new PageResult();
        page.setTotal((long) result.size());
        page.setList(dtos);
        return CommonResult.success( page);
    }

    private List<Integer> filterHitId(List<Integer> typeHitId, List<Integer> ingredientList) {
        List<Integer> result = new ArrayList<>();
        InquiryProductRuleDtoSearchParam searchParam = new InquiryProductRuleDtoSearchParam();
        searchParam.setProductCommonIds(typeHitId);
        CommonResult<PageResult<InquiryProductRuleDto>> apiResult = pageSearchInquiryProductRuleCommon(searchParam);
        if(org.springframework.util.ObjectUtils.isEmpty(apiResult) || org.springframework.util.ObjectUtils.isEmpty(apiResult.getData())){
            //不处理
            return typeHitId;
        }
        List<InquiryProductRuleDto> ruleDtos = apiResult.getData().getList();
        Map<Integer,List<InquiryProductRuleDto>> prodMap = ruleDtos.stream().collect(Collectors.groupingBy(InquiryProductRuleDto::getProductCommonId));
        prodMap.forEach((k,v)->{
            List<Integer> ingrRule = v.stream().filter(obj -> obj.getLimitDrugType().equals(LimitTypeEnum.INGREDIENT_LIMIT.getType())).map(InquiryProductRuleDto::getParam).map(Integer::parseInt).collect(Collectors.toList());
            if(ingrRule.containsAll(ingredientList)){
                result.add(k);
            }
        });
        return result;
    }

    private List<Integer> typeAgg(co.elastic.clients.elasticsearch._types.query_dsl.Query query, InquiryProductRuleDtoSearchParam param){
        List<Integer> resultId = new ArrayList<>();
        try {
            // 构建聚合查询
            NativeQuery searchQuery = new NativeQueryBuilder()
                    .withQuery(query)
                    .withAggregation("typeAgg",
                            co.elastic.clients.elasticsearch._types.aggregations.Aggregation.of(a -> a
                                    .terms(t -> t
                                            .field(InquiryProductRuleFieldEnum.LIMIT_DRUG_TYPE.getFieldName())
                                            .size(MAX_SIZE)
                                    )
                                    .aggregations("topHits", 
                                            co.elastic.clients.elasticsearch._types.aggregations.Aggregation.of(th -> th
                                                    .topHits(top -> top
                                                            .size(MAX_SIZE)
                                                            .source(s -> s
                                                                    .filter(f -> f
                                                                            .includes(InquiryProductRuleFieldEnum.PRODUCT_COMMON_ID.getFieldName())
                                                                    )
                                                            )
                                                    )
                                            )
                                    )
                            )
                    )
                    .build();
            
            IndexCoordinates indexCoordinates = IndexCoordinates.of(IndexAliasEnum.YKQ_REMOTE_PRODUCT_RULE.getReadAlias());
            SearchHits<InquiryProductRuleDto> searchHits = elasticsearchOperations.search(searchQuery, InquiryProductRuleDto.class, indexCoordinates);
            
            ElasticsearchAggregations aggregations = (ElasticsearchAggregations) searchHits.getAggregations();
            if (aggregations != null) {
                LongTermsAggregate typeAgg = aggregations.get("typeAgg").aggregation().getAggregate().lterms();
                List<LongTermsBucket> buckets = typeAgg.buckets().array();
                
                if(CollectionUtils.isEmpty(buckets) || param.getExpectBucketNum() != buckets.size()){
                    return resultId;
                }
                
                for(LongTermsBucket bucket : buckets){
                    List<Integer> productId = new ArrayList<>();
                    TopHitsAggregate topHits = bucket.aggregations().get("topHits").topHits();
                    
                    for(var hit : topHits.hits().hits()){
                        Object productCommonId = hit.source().toJson().asJsonObject().get(InquiryProductRuleFieldEnum.PRODUCT_COMMON_ID.getFieldName());
                        if(productCommonId != null){
                            productId.add(Integer.parseInt(productCommonId.toString().replace("\"", "")));
                        }
                    }
                    
                    if(resultId.size() == 0){
                        resultId = productId;
                    }
                    List<Integer> intersection = resultId.stream().filter(productId::contains).collect(Collectors.toList());
                    resultId = intersection;
                }
            }
            return resultId;
        } catch (Exception e) {
            log.error("typeAgg查询失败", e);
            return resultId;
        }
    }

    private List<Integer> prodAgg(co.elastic.clients.elasticsearch._types.query_dsl.Query query){
        List<Integer> resultId = new ArrayList<>();
        try {
            // 构建聚合查询
            NativeQuery searchQuery = new NativeQueryBuilder()
                    .withQuery(query)
                    .withAggregation("productAgg",
                            co.elastic.clients.elasticsearch._types.aggregations.Aggregation.of(a -> a
                                    .terms(t -> t
                                            .field(InquiryProductRuleFieldEnum.PRODUCT_COMMON_ID.getFieldName())
                                            .size(MAX_SIZE)
                                    )
                            )
                    )
                    .build();
            
            IndexCoordinates indexCoordinates = IndexCoordinates.of(IndexAliasEnum.YKQ_REMOTE_PRODUCT_RULE.getReadAlias());
            SearchHits<InquiryProductRuleDto> searchHits = elasticsearchOperations.search(searchQuery, InquiryProductRuleDto.class, indexCoordinates);
            
            ElasticsearchAggregations aggregations = (ElasticsearchAggregations) searchHits.getAggregations();
            if (aggregations != null) {
                LongTermsAggregate productAgg = aggregations.get("productAgg").aggregation().getAggregate().lterms();
                List<LongTermsBucket> buckets = productAgg.buckets().array();
                
                if(CollectionUtils.isEmpty(buckets)){
                    return resultId;
                }
                
                for(LongTermsBucket bucket : buckets){
                    resultId.add(Integer.parseInt(String.valueOf(bucket.key())));
                }
            }
            return resultId;
        } catch (Exception e) {
            log.error("prodAgg查询失败", e);
            return resultId;
        }
    }

    private int getOffsetEnd(InquiryProductRuleDtoSearchParam param, int totalSize) {
        int pageNum = param.getPageNo();
        int pageSize = param.getPageSize();
        if(totalSize < pageSize){
            return totalSize;
        }
        int end = pageNum * pageSize;
        return Math.min(totalSize, end);
    }

    @Override
    public CommonResult<PageResult<InquiryProductRuleDto>> pageSearchInquiryProductRuleCommon(InquiryProductRuleDtoSearchParam param) {
        //添加默认分页信息
        if(param.getPageNo()<1 || param.getPageSize()<1){
            param.setPageNo(1);
            param.setPageSize(10000);
        }
        
        try {
            // 构建查询
            NativeQuery searchQuery = new NativeQueryBuilder()
                    .withQuery(getCommonSearchBuild(param))
                    .withPageable(PageRequest.of(param.getPageNo() - 1, param.getPageSize()))
                    .build();
            
            IndexCoordinates indexCoordinates = IndexCoordinates.of(IndexAliasEnum.YKQ_REMOTE_PRODUCT_RULE.getReadAlias());
            SearchHits<InquiryProductRuleDto> searchHits = elasticsearchOperations.search(searchQuery, InquiryProductRuleDto.class, indexCoordinates);
            
            if(searchHits.isEmpty()){
                return CommonResult.success(new PageResult<>());
            }

            List<InquiryProductRuleDto> ruleDtos = new ArrayList<>();
            for(SearchHit<InquiryProductRuleDto> hit : searchHits){
                ruleDtos.add(hit.getContent());
            }
            
            PageResult page = new PageResult();
            page.setTotal(searchHits.getTotalHits());
            page.setList(ruleDtos);
            return CommonResult.success(page);
        } catch (Exception e) {
            log.error("查询商品规则失败", e);
            throw new RuntimeException(e);
        }
    }

    private co.elastic.clients.elasticsearch._types.query_dsl.Query getTypeSearchBuild(InquiryProductRuleDtoSearchParam param) {
        BoolQuery.Builder boolQuery = new BoolQuery.Builder();
        int expectBucketNum = 0;
        //性别用药
        if(ObjectUtils.isNotEmpty(param.getSexLimit())){
            BoolQuery.Builder sexBool = new BoolQuery.Builder();
            sexBool.filter(TermQuery.of(t -> t.field(InquiryProductRuleFieldEnum.LIMIT_DRUG_TYPE.getFieldName()).value(LimitTypeEnum.SEX_LIMIT.getType()))._toQuery());
            sexBool.filter(TermQuery.of(t -> t.field(InquiryProductRuleFieldEnum.PARAM.getFieldName()).value(param.getSexLimit()))._toQuery());
            boolQuery.should(sexBool.build()._toQuery());
            expectBucketNum++;
        }
        //年龄段限制
        if(CollectionUtils.isNotEmpty(param.getAgeLimits())){
            BoolQuery.Builder ageBool = new BoolQuery.Builder();
            ageBool.filter(TermQuery.of(t -> t.field(InquiryProductRuleFieldEnum.LIMIT_DRUG_TYPE.getFieldName()).value(LimitTypeEnum.AGE_LIMIT.getType()))._toQuery());
            ageBool.filter(TermsQuery.of(t -> t.field(InquiryProductRuleFieldEnum.PARAM.getFieldName()).terms(terms -> terms.value(param.getAgeLimits().stream().map(FieldValue::of).collect(Collectors.toList()))))._toQuery());
            boolQuery.should(ageBool.build()._toQuery());
            expectBucketNum++;
        }
        //年龄区间限制
        if(ObjectUtils.isNotEmpty(param.getRangeBegin()) && ObjectUtils.isNotEmpty(param.getRangeEnd())){
            BoolQuery.Builder bl = new BoolQuery.Builder();
            bl.filter(TermQuery.of(t -> t.field(InquiryProductRuleFieldEnum.LIMIT_DRUG_TYPE.getFieldName()).value(LimitTypeEnum.AGE_RANGE_LIMIT.getType()))._toQuery());
            bl.filter(RangeQuery.of(r -> r.field(InquiryProductRuleFieldEnum.RANGE_BEGIN.getFieldName()).lte(JsonData.of(param.getRangeEnd())))._toQuery());
            bl.filter(RangeQuery.of(r -> r.field(InquiryProductRuleFieldEnum.RANGE_END.getFieldName()).gt(JsonData.of(param.getRangeBegin())))._toQuery());
            boolQuery.should(bl.build()._toQuery());
            expectBucketNum++;
        }
        //肝肾异常限制
        if(CollectionUtils.isNotEmpty(param.getHealthLimit())){
            List<Integer> healthList = param.getHealthLimit();
            BoolQuery.Builder healBool = new BoolQuery.Builder();
            healBool.filter(TermQuery.of(t -> t.field(InquiryProductRuleFieldEnum.LIMIT_DRUG_TYPE.getFieldName()).value(LimitTypeEnum.HEALTH_LIMIT.getType()))._toQuery());
            healBool.filter(TermsQuery.of(t -> t.field(InquiryProductRuleFieldEnum.PARAM.getFieldName()).terms(terms -> terms.value(healthList.stream().map(FieldValue::of).collect(Collectors.toList()))))._toQuery());
            //不包含未设置场景
            if(!healthList.stream().anyMatch(value -> value.equals(InquiryProductRuleConstant.NOT_SET))){
                healBool.filter(RangeQuery.of(r -> r.field(InquiryProductRuleFieldEnum.CAUTION.getFieldName()).gt(JsonData.of(0)))._toQuery());
            }
            boolQuery.should(healBool.build()._toQuery());
            expectBucketNum++;
        }

        //妊娠哺乳期限制
        if(CollectionUtils.isNotEmpty(param.getWomenLimit())){
            List<Integer> womenList = param.getWomenLimit();
            BoolQuery.Builder womenBool = new BoolQuery.Builder();
            womenBool.filter(TermQuery.of(t -> t.field(InquiryProductRuleFieldEnum.LIMIT_DRUG_TYPE.getFieldName()).value(LimitTypeEnum.WOMEN_LIMIT.getType()))._toQuery());
            womenBool.filter(TermsQuery.of(t -> t.field(InquiryProductRuleFieldEnum.PARAM.getFieldName()).terms(terms -> terms.value(param.getWomenLimit().stream().map(FieldValue::of).collect(Collectors.toList()))))._toQuery());
            //不包含未设置场景
            if(!womenList.stream().anyMatch(value -> value.equals(InquiryProductRuleConstant.NOT_SET))){
                womenBool.filter(RangeQuery.of(r -> r.field(InquiryProductRuleFieldEnum.CAUTION.getFieldName()).gt(JsonData.of(0)))._toQuery());
            }
            boolQuery.should(womenBool.build()._toQuery());
            expectBucketNum++;
        }

        //成分限制
        if(ObjectUtils.isNotEmpty(param.getIngredientQueryType())){
            BoolQuery.Builder ingreBool = new BoolQuery.Builder();
            ingreBool.filter(TermQuery.of(t -> t.field(InquiryProductRuleFieldEnum.LIMIT_DRUG_TYPE.getFieldName()).value(LimitTypeEnum.INGREDIENT_LIMIT.getType()))._toQuery());
            if(param.getIngredientQueryType().equals(InquiryProductRuleConstant.NOT_SET)){
                ingreBool.filter(TermsQuery.of(t -> t.field(InquiryProductRuleFieldEnum.PARAM.getFieldName()).terms(terms -> terms.value(Arrays.asList(FieldValue.of(InquiryProductRuleConstant.NOT_SET)))))._toQuery());
            }
            //目前设计匹配全部不好实现，这里先把数据查出来，再过滤
            if(param.getIngredientQueryType().equals(InquiryProductRuleConstant.MATCH_ANY) || param.getIngredientQueryType().equals(InquiryProductRuleConstant.MATCH_ALL)){
                ingreBool.filter(TermsQuery.of(t -> t.field(InquiryProductRuleFieldEnum.PARAM.getFieldName()).terms(terms -> terms.value(param.getIngredientList().stream().map(FieldValue::of).collect(Collectors.toList()))))._toQuery());
            }
            boolQuery.should(ingreBool.build()._toQuery());
            expectBucketNum++;
        }
        param.setExpectBucketNum(expectBucketNum);
        return boolQuery.build()._toQuery();
    }

    private co.elastic.clients.elasticsearch._types.query_dsl.Query getCommonSearchBuild(InquiryProductRuleDtoSearchParam param) {
        BoolQuery.Builder boolQuery = new BoolQuery.Builder();
        //通用名
        if(StringUtils.isNotBlank(param.getCommonName())){
            boolQuery.filter(WildcardQuery.of(w -> w.field(InquiryProductRuleFieldEnum.COMMON_NAME.getFieldName()).value("*".concat(SwitchUtil.replaceEsWildLikeStr(param.getCommonName())).concat("*")))._toQuery());
        }
        //选择了六级分类
        if(CollectionUtils.isNotEmpty(param.getCategoryLv6Ids())){
            boolQuery.filter(TermsQuery.of(t -> t.field(InquiryProductRuleFieldEnum.CATEGORY_LV6_ID.getFieldName()).terms(terms -> terms.value(param.getCategoryLv6Ids().stream().map(FieldValue::of).collect(Collectors.toList()))))._toQuery());
        }
        //是否启用
        if(ObjectUtils.isNotEmpty(param.getStatus())){
            boolQuery.filter(TermQuery.of(t -> t.field(InquiryProductRuleFieldEnum.STATUS.getFieldName()).value(param.getStatus()))._toQuery());
        }

        //医保备注是否有值
        if(ObjectUtils.isNotEmpty(param.getMedicareRemarkQueryType())){
            //无数据
            if(param.getMedicareRemarkQueryType().equals(InquiryProductRuleConstant.NOT_SET)){
                BoolQuery.Builder should = new BoolQuery.Builder();
                BoolQuery.Builder item = new BoolQuery.Builder();
                item.mustNot(ExistsQuery.of(e -> e.field(InquiryProductRuleFieldEnum.MEDICARE_REMARK.getFieldName()))._toQuery());
                should.should(item.build()._toQuery());
                should.should(TermQuery.of(t -> t.field(InquiryProductRuleFieldEnum.MEDICARE_REMARK.getFieldName()).value(""))._toQuery());
                boolQuery.must(should.build()._toQuery());
            }
            //有数据
            if(param.getMedicareRemarkQueryType().equals(InquiryProductRuleConstant.MATCH_ANY)){
                boolQuery.must(ExistsQuery.of(e -> e.field(InquiryProductRuleFieldEnum.MEDICARE_REMARK.getFieldName()))._toQuery());
                boolQuery.mustNot(TermQuery.of(t -> t.field(InquiryProductRuleFieldEnum.MEDICARE_REMARK.getFieldName()).value(""))._toQuery());
            }
        }


        //通用名集合
        if(CollectionUtils.isNotEmpty(param.getProductCommonNames())){
            boolQuery.filter(TermsQuery.of(t -> t.field(InquiryProductRuleFieldEnum.COMMON_NAME.getFieldName()).terms(terms -> terms.value(param.getProductCommonNames().stream().map(FieldValue::of).collect(Collectors.toList()))))._toQuery());
        }

        //通用名id集合
        if(CollectionUtils.isNotEmpty(param.getProductCommonIds())){
            boolQuery.filter(TermsQuery.of(t -> t.field(InquiryProductRuleFieldEnum.PRODUCT_COMMON_ID.getFieldName()).terms(terms -> terms.value(param.getProductCommonIds().stream().map(FieldValue::of).collect(Collectors.toList()))))._toQuery());
        }
        return boolQuery.build()._toQuery();
    }

}
