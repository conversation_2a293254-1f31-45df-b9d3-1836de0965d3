package com.xyy.saas.inquiry.common.controller.admin.rational;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.common.api.rational.SaasUsageAndDosageReviewApi;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageDrugRuleDto;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageRuleDrugQueryDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.UsageAndDosageReviewDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用法用量审查
 *
 * <AUTHOR>
 * @Date 9/9/24 3:44 PM
 */
@Tag(name = "管理后台 - 合理用药 - 用法用量审查")
@RestController
@RequestMapping("/usageAndDosage/review")
@Validated
public class SaasUsageAndDosageReviewController{

    @Resource
    private SaasUsageAndDosageReviewApi saasUsageAndDosageReviewApi;

    /**
     * 保存用法用量审查
     *
     * @param usageAndDosageReviewDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 9/9/24 3:45 PM
     */
    @PostMapping(value = "/save")
    public CommonResult<Boolean> save(@RequestBody UsageAndDosageReviewDto usageAndDosageReviewDto){
        return saasUsageAndDosageReviewApi.save(usageAndDosageReviewDto);
    }

    /**
     * 分页查询用法用量审查
     *
     * @param usageAndDosageReviewDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 9/9/24 3:45 PM
     */
    @PostMapping(value = "/pageQuery")
    public CommonResult<PageResult<UsageAndDosageReviewDto>> pageQuery(@RequestBody UsageAndDosageReviewDto usageAndDosageReviewDto){

        return saasUsageAndDosageReviewApi.pageQuery(usageAndDosageReviewDto);
    }

    /**
     * 修改用法用量审查状态
     *
     * @param usageAndDosageReviewDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 9/9/24 3:45 PM
     */
    @PostMapping(value = "/updateEnableStatus")
    public CommonResult<Boolean> updateEnableStatus(@RequestBody UsageAndDosageReviewDto usageAndDosageReviewDto){

        return saasUsageAndDosageReviewApi.updateEnableStatus(usageAndDosageReviewDto);
    }

    /**
     * 删除用法用量审查状态
     *
     * @param usageAndDosageReviewDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 9/9/24 3:45 PM
     */
    @PostMapping(value = "/deleteById")
    public CommonResult<Boolean> deleteById(@RequestBody UsageAndDosageReviewDto usageAndDosageReviewDto){

        return saasUsageAndDosageReviewApi.deleteById(usageAndDosageReviewDto);
    }

    /**
     * 根据药品查询用法用量审查规则
     *
     * @param usageAndDosageRuleDrugQueryDto
     * @return com.xyy.saas.remote.web.core.utils.ResultVO<java.lang.Boolean>
     * <AUTHOR> 9/9/24 3:45 PM
     */
    @PostMapping(value = "/queryRuleByDrug")
    public CommonResult<UsageAndDosageDrugRuleDto> queryRuleByDrug(@RequestBody UsageAndDosageRuleDrugQueryDto usageAndDosageRuleDrugQueryDto){

        return saasUsageAndDosageReviewApi.queryRuleByDrug(usageAndDosageRuleDrugQueryDto);
    }


}
