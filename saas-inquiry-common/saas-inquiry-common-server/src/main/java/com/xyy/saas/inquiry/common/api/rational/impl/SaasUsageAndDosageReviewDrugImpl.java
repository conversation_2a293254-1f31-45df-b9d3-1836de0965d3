package com.xyy.saas.inquiry.common.api.rational.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.alibaba.dubbo.config.annotation.Reference;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.common.api.rational.SaasUsageAndDosageReviewDrugApi;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasUsageAndDosageReviewDrugDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasUsageAndDosageReviewDrugQueryDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasUsageAndDosageReviewDrugSaveDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.UsageAndDosageReviewDto;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.SaasUsageAndDosageReviewDrugPo;
import com.xyy.saas.inquiry.common.service.rational.SaasUsageAndDosageReviewDrugService;
import com.xyy.saas.inquiry.common.service.rational.SaasUsageAndDosageReviewService;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.product.api.ProductStdlibApi;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductPageQueryDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用法用量审查药品
 *
 * <AUTHOR>
 * @Date 9/10/24 4:24 PM
 */
@Service
public class SaasUsageAndDosageReviewDrugImpl implements SaasUsageAndDosageReviewDrugApi {

    @DubboReference
    private ProductStdlibApi productStdlibApi;

    @Resource
    private SaasUsageAndDosageReviewDrugService saasUsageAndDosageReviewDrugService;

    @Resource
    private SaasUsageAndDosageReviewService saasUsageAndDosageReviewService;

    @Override
    public CommonResult<PageResult<SaasUsageAndDosageReviewDrugDto>> pageSearchCenterDrug(SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto) {

        if (StringUtils.isBlank(saasUsageAndDosageReviewDrugDto.getCommonName())) {
            return CommonResult.error("查询通用名不能为空");
        }

        StdlibProductPageQueryDto queryDto = new StdlibProductPageQueryDto();
        queryDto.setMixedNameQuery(saasUsageAndDosageReviewDrugDto.getCommonName());
        queryDto.setSpec(saasUsageAndDosageReviewDrugDto.getSpecification());
        queryDto.setPageNo(saasUsageAndDosageReviewDrugDto.getPageNo());
        queryDto.setPageSize(saasUsageAndDosageReviewDrugDto.getPageSize());


        PageResult<ProductStdlibDto> pageResult = productStdlibApi.queryProductStdlibPage(queryDto);


        if (ObjectUtil.isEmpty(pageResult) || CollectionUtils.isEmpty(pageResult.getList())) {
            return CommonResult.success(new PageResult<>());
        }

        List<ProductStdlibDto> productStdlibDtoList = pageResult.getList();

        List<SaasUsageAndDosageReviewDrugDto> saasUsageAndDosageReviewDrugDtoList = new ArrayList<>();

        // 通用名集合
        Set<String> commonNameSet = new HashSet<>();
        // 规格集合
        Set<String> specificationSet = new HashSet<>();

        for (ProductStdlibDto productSearchDataVo : productStdlibDtoList) {

            saasUsageAndDosageReviewDrugDtoList.add(SaasUsageAndDosageReviewDrugDto.builder()
                    .commonName(productSearchDataVo.getCommonName())
                    .specification(productSearchDataVo.getSpec()).build());

            if (StringUtils.isNotBlank(productSearchDataVo.getCommonName())) {

                commonNameSet.add(productSearchDataVo.getCommonName());

                if (StringUtils.isNotBlank(productSearchDataVo.getSpec())) {
                    specificationSet.add(productSearchDataVo.getSpec());
                }
            }
        }

        PageResult<SaasUsageAndDosageReviewDrugDto> pageInfo = new PageResult<>();
        pageInfo.setTotal(pageResult.getTotal());
        pageInfo.setList(saasUsageAndDosageReviewDrugDtoList);

        if (CollectionUtils.isEmpty(commonNameSet)) {
            return CommonResult.success(pageInfo);
        }

        List<SaasUsageAndDosageReviewDrugPo> saasUsageAndDosageReviewDrugPoList = saasUsageAndDosageReviewDrugService.queryByCondition(
            SaasUsageAndDosageReviewDrugQueryDto.builder().commonNameSet(commonNameSet).specificationSet(specificationSet).build());

        if (CollectionUtils.isEmpty(saasUsageAndDosageReviewDrugPoList)) {
            return CommonResult.success(pageInfo);
        }

        Map<String, SaasUsageAndDosageReviewDrugPo> saasUsageAndDosageReviewDrugPoMap = saasUsageAndDosageReviewDrugPoList.stream()
                .collect(Collectors.toMap(item -> item.getCommonName() + StringUtils.defaultString(item.getSpecification(), ""), Function.identity(), (v1, v2) -> v2));

        if (MapUtils.isNotEmpty(saasUsageAndDosageReviewDrugPoMap)) {
            for (SaasUsageAndDosageReviewDrugDto usageAndDosageReviewDrugDto : pageInfo.getList()) {
                // 通用名加规格
                String combinationName = usageAndDosageReviewDrugDto.getCommonName() + StringUtils.defaultString(usageAndDosageReviewDrugDto.getSpecification(), "");
                usageAndDosageReviewDrugDto.setIsRelation(saasUsageAndDosageReviewDrugPoMap.containsKey(combinationName));
                usageAndDosageReviewDrugDto.setPageNo(null);
                usageAndDosageReviewDrugDto.setPageSize(null);
            }
        }

        return CommonResult.success(pageInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<SaasUsageAndDosageReviewDrugSaveDto> save(SaasUsageAndDosageReviewDrugSaveDto saasUsageAndDosageReviewDrugSaveDto) {

        if (saasUsageAndDosageReviewDrugSaveDto == null || saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId() == null
                || saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId() <= 0 || CollectionUtils.isEmpty(saasUsageAndDosageReviewDrugSaveDto.getSaasUsageAndDosageReviewDrugDtoList())) {
            return CommonResult.error("参数异常");
        }

        for (SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto : saasUsageAndDosageReviewDrugSaveDto.getSaasUsageAndDosageReviewDrugDtoList()) {
            if (StringUtils.isBlank(saasUsageAndDosageReviewDrugDto.getCommonName())) {
                return CommonResult.error("药品通用名不能为空");
            }
        }

        // 根据通用名和规则查询用法用量审查药品
        List<SaasUsageAndDosageReviewDrugPo> saasUsageAndDosageReviewDrugPoList = this.queryByCommonNameAndSpecification(saasUsageAndDosageReviewDrugSaveDto.getSaasUsageAndDosageReviewDrugDtoList());

        // 存在宇别的审查的数据集合
        List<SaasUsageAndDosageReviewDrugDto> existByOtherReviewList = new ArrayList<>();

        Map<String, SaasUsageAndDosageReviewDrugPo> saasUsageAndDosageReviewDrugPoMap = saasUsageAndDosageReviewDrugPoList.stream()
                .collect(Collectors.toMap(item -> item.getCommonName() + StringUtils.defaultString(item.getSpecification(), ""), Function.identity(), (v1, v2) -> v2));

        List<SaasUsageAndDosageReviewDrugPo> insertSaasUsageAndDosageReviewDrugPoList = new ArrayList<>();

        if (MapUtils.isNotEmpty(saasUsageAndDosageReviewDrugPoMap)) {

            for (SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto : saasUsageAndDosageReviewDrugSaveDto.getSaasUsageAndDosageReviewDrugDtoList()) {

                // 通用名加规格
                String combinationName = saasUsageAndDosageReviewDrugDto.getCommonName() + StringUtils.defaultString(saasUsageAndDosageReviewDrugDto.getSpecification(), "");

                // 查询数据库是否添加了该通用名和规格
                SaasUsageAndDosageReviewDrugPo existData = saasUsageAndDosageReviewDrugPoMap.get(combinationName);

                // 当数据库已经存在该通用名和规格
                if (existData != null) {

                    // 当通用名和规格对应的审查id和当前添加的审查id一致则跳过
                    if (saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId().equals(existData.getUsageAndDosageReviewId())) {
                        continue;
                    }

                    existByOtherReviewList.add(SaasUsageAndDosageReviewDrugDto.builder().commonName(existData.getCommonName()).specification(existData.getSpecification()).build());
                    continue;
                }
                insertSaasUsageAndDosageReviewDrugPoList.add(SaasUsageAndDosageReviewDrugPo.builder()
                        .usageAndDosageReviewId(saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId())
                        .commonName(saasUsageAndDosageReviewDrugDto.getCommonName())
                        .specification(saasUsageAndDosageReviewDrugDto.getSpecification())
                        .build());
            }
        } else {

            for (SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto : saasUsageAndDosageReviewDrugSaveDto.getSaasUsageAndDosageReviewDrugDtoList()) {
                insertSaasUsageAndDosageReviewDrugPoList.add(SaasUsageAndDosageReviewDrugPo.builder()
                        .usageAndDosageReviewId(saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId())
                        .commonName(saasUsageAndDosageReviewDrugDto.getCommonName())
                        .specification(saasUsageAndDosageReviewDrugDto.getSpecification())
                        .build());
            }
        }

        if (CollectionUtils.isNotEmpty(insertSaasUsageAndDosageReviewDrugPoList)) {
            // 新增用法用量审查药品
            saasUsageAndDosageReviewDrugService.batchInsert(insertSaasUsageAndDosageReviewDrugPoList);
            // 修改用法用量审查药品数量
            saasUsageAndDosageReviewService.updateRelationDrugCount(Lists.newArrayList(saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId()), insertSaasUsageAndDosageReviewDrugPoList.size());
        }

        SaasUsageAndDosageReviewDrugSaveDto result = SaasUsageAndDosageReviewDrugSaveDto.builder()
                .usageAndDosageReviewId(saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId()).build();

        if (CollectionUtils.isNotEmpty(existByOtherReviewList)) {
            result.setSaasUsageAndDosageReviewDrugDtoList(existByOtherReviewList);
            String reviewName = "";
            UsageAndDosageReviewDto usageAndDosageReviewDtoCondition = new UsageAndDosageReviewDto();
            usageAndDosageReviewDtoCondition.setPageNo(1);
            usageAndDosageReviewDtoCondition.setPageSize(1);
            usageAndDosageReviewDtoCondition.setId(saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId());
            PageResult<UsageAndDosageReviewDto> usageAndDosageReviewDtoPageInfo = saasUsageAndDosageReviewService.pageQuery(usageAndDosageReviewDtoCondition);

            if (usageAndDosageReviewDtoPageInfo != null && CollectionUtils.isNotEmpty(usageAndDosageReviewDtoPageInfo.getList())) {
                UsageAndDosageReviewDto usageAndDosageReviewDto = usageAndDosageReviewDtoPageInfo.getList().get(0);
                reviewName = usageAndDosageReviewDto.getCommonName() + usageAndDosageReviewDto.getIngredientDosage();
            }

            String errorName = existByOtherReviewList.stream().map(item -> item.getCommonName() + StringUtils.defaultString(item.getSpecification(), "")).collect(Collectors.joining("，"));
            result.setErrorMsg("【" + errorName + "】已存在其他绑定关系，是否确定将绑定关系迁移到" + "【" + reviewName + "】");
        }

        return CommonResult.success(result);
    }

    @Override
    public CommonResult<PageResult<SaasUsageAndDosageReviewDrugDto>> pageQueryByCondition(SaasUsageAndDosageReviewDrugQueryDto saasUsageAndDosageReviewDrugQueryDto) {

        return CommonResult.success(saasUsageAndDosageReviewDrugService.pageQueryByCondition(saasUsageAndDosageReviewDrugQueryDto));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteById(SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto) {

        if (saasUsageAndDosageReviewDrugDto == null || saasUsageAndDosageReviewDrugDto.getId() == null || saasUsageAndDosageReviewDrugDto.getId() <= 0) {
            return CommonResult.error("参数异常");
        }

        List<SaasUsageAndDosageReviewDrugPo> saasUsageAndDosageReviewDrugPoList = saasUsageAndDosageReviewDrugService.queryByCondition(SaasUsageAndDosageReviewDrugQueryDto.builder().idList(Lists.newArrayList(saasUsageAndDosageReviewDrugDto.getId())).build());

        if (CollectionUtils.isEmpty(saasUsageAndDosageReviewDrugPoList)) {
            return CommonResult.error("需要删除的数据不存在");
        }

        saasUsageAndDosageReviewDrugDto.setIdList(Lists.newArrayList(saasUsageAndDosageReviewDrugDto.getId()));
        saasUsageAndDosageReviewDrugDto.setUpdateTime(new Date());

        saasUsageAndDosageReviewDrugService.deleteById(saasUsageAndDosageReviewDrugDto);
        // 修改用法用量审查关联药品数量
        saasUsageAndDosageReviewService.updateRelationDrugCount(Lists.newArrayList(saasUsageAndDosageReviewDrugPoList.getFirst().getUsageAndDosageReviewId()), -1);

        return CommonResult.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> forceSave(SaasUsageAndDosageReviewDrugSaveDto saasUsageAndDosageReviewDrugSaveDto) {

        if (saasUsageAndDosageReviewDrugSaveDto == null || CollectionUtils.isEmpty(saasUsageAndDosageReviewDrugSaveDto.getSaasUsageAndDosageReviewDrugDtoList())
                || saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId() == null
                || saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId() <= 0) {
            return CommonResult.error("参数异常");
        }

        for (SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto : saasUsageAndDosageReviewDrugSaveDto.getSaasUsageAndDosageReviewDrugDtoList()) {
            if (StringUtils.isBlank(saasUsageAndDosageReviewDrugDto.getCommonName())) {
                return CommonResult.error("药品通用名不能为空");
            }
        }

        // 根据通用名和规则查询用法用量审查药品
        List<SaasUsageAndDosageReviewDrugPo> saasUsageAndDosageReviewDrugPoList = this.queryByCommonNameAndSpecification(saasUsageAndDosageReviewDrugSaveDto.getSaasUsageAndDosageReviewDrugDtoList());

        Map<String, SaasUsageAndDosageReviewDrugPo> saasUsageAndDosageReviewDrugPoMap = saasUsageAndDosageReviewDrugPoList.stream()
                .collect(Collectors.toMap(item -> item.getCommonName() + StringUtils.defaultString(item.getSpecification(), ""), Function.identity(), (v1, v2) -> v2));

        // 需要删除的数据
        List<SaasUsageAndDosageReviewDrugPo> needDeleteUsageAndDosageReviewDrugPoList = new ArrayList<>();
        // 需要新增的数据
        List<SaasUsageAndDosageReviewDrugPo> insertSaasUsageAndDosageReviewDrugPoList = new ArrayList<>();

        if (MapUtils.isNotEmpty(saasUsageAndDosageReviewDrugPoMap)) {

            for (SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto : saasUsageAndDosageReviewDrugSaveDto.getSaasUsageAndDosageReviewDrugDtoList()) {

                // 通用名加规格
                String combinationName = saasUsageAndDosageReviewDrugDto.getCommonName() + StringUtils.defaultString(saasUsageAndDosageReviewDrugDto.getSpecification(), "");

                // 查询数据库是否添加了该通用名和规格
                SaasUsageAndDosageReviewDrugPo existData = saasUsageAndDosageReviewDrugPoMap.get(combinationName);

                // 当数据库已经存在该通用名和规格
                if (existData != null) {

                    // 当通用名和规格对应的审查id和当前添加的审查id一致则跳过
                    if (saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId().equals(existData.getUsageAndDosageReviewId())) {
                        continue;
                    }

                    // 添加需要删除的id
                    needDeleteUsageAndDosageReviewDrugPoList.add(existData);
                }
                insertSaasUsageAndDosageReviewDrugPoList.add(SaasUsageAndDosageReviewDrugPo.builder()
                        .usageAndDosageReviewId(saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId())
                        .commonName(saasUsageAndDosageReviewDrugDto.getCommonName())
                        .specification(saasUsageAndDosageReviewDrugDto.getSpecification())
                        .build());
            }
        } else {
            for (SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto : saasUsageAndDosageReviewDrugSaveDto.getSaasUsageAndDosageReviewDrugDtoList()) {
                insertSaasUsageAndDosageReviewDrugPoList.add(SaasUsageAndDosageReviewDrugPo.builder()
                        .usageAndDosageReviewId(saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId())
                        .commonName(saasUsageAndDosageReviewDrugDto.getCommonName())
                        .specification(saasUsageAndDosageReviewDrugDto.getSpecification())
                        .build());
            }

        }

        // 新增数据
        if (CollectionUtils.isNotEmpty(insertSaasUsageAndDosageReviewDrugPoList)) {
            // 新增用法用量审查药品
            saasUsageAndDosageReviewDrugService.batchInsert(insertSaasUsageAndDosageReviewDrugPoList);
            // 修改用法用量审查药品数量
            saasUsageAndDosageReviewService.updateRelationDrugCount(Lists.newArrayList(saasUsageAndDosageReviewDrugSaveDto.getUsageAndDosageReviewId()), insertSaasUsageAndDosageReviewDrugPoList.size());
        }

        // 删除数据
        if (CollectionUtils.isNotEmpty(needDeleteUsageAndDosageReviewDrugPoList)) {

            // 需要删除的用法用量审查药品id集合
            List<Long> needDeleteIdList = new ArrayList<>();
            // 需要修改的用法用量审查map
            Map<Long, Integer> needUpdateReviewIdMap = new HashMap<>();

            for (SaasUsageAndDosageReviewDrugPo saasUsageAndDosageReviewDrugPo : needDeleteUsageAndDosageReviewDrugPoList) {
                needDeleteIdList.add(saasUsageAndDosageReviewDrugPo.getId());
                if (needUpdateReviewIdMap.containsKey(saasUsageAndDosageReviewDrugPo.getUsageAndDosageReviewId())) {
                    Integer count = needUpdateReviewIdMap.get(saasUsageAndDosageReviewDrugPo.getUsageAndDosageReviewId());
                    needUpdateReviewIdMap.put(saasUsageAndDosageReviewDrugPo.getUsageAndDosageReviewId(), count + 1);
                } else {
                    needUpdateReviewIdMap.put(saasUsageAndDosageReviewDrugPo.getUsageAndDosageReviewId(), 1);
                }
            }

            SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto = SaasUsageAndDosageReviewDrugDto.builder()
                    .idList(needDeleteIdList)
                    .updateUser(saasUsageAndDosageReviewDrugSaveDto.getUpdateUser())
                    .updateTime(new Date()).build();
            // 删除用法用量审查药品数据
            saasUsageAndDosageReviewDrugService.deleteById(saasUsageAndDosageReviewDrugDto);
            // 修改用法用量审查关联药品数量
            needUpdateReviewIdMap.forEach((k, v) -> saasUsageAndDosageReviewService.updateRelationDrugCount(Lists.newArrayList(k), 0 - v));
        }

        return CommonResult.success(true);
    }

    /**
     * 根据通用名和规则查询用法用量审查药品
     *
     * @param saasUsageAndDosageReviewDrugDtoList
     * @return java.util.List<com.xyy.saas.remote.web.po.SaasUsageAndDosageReviewDrugPo>
     * <AUTHOR> 9/13/24 3:02 PM
     */
    private List<SaasUsageAndDosageReviewDrugPo> queryByCommonNameAndSpecification(List<SaasUsageAndDosageReviewDrugDto> saasUsageAndDosageReviewDrugDtoList) {

        if (CollectionUtils.isEmpty(saasUsageAndDosageReviewDrugDtoList)) {
            return Lists.newArrayList();
        }

        // 通用名集合
        Set<String> commonNameSet = new HashSet<>();
        // 规格集合
        Set<String> specificationSet = new HashSet<>();

        for (SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto : saasUsageAndDosageReviewDrugDtoList) {

            commonNameSet.add(saasUsageAndDosageReviewDrugDto.getCommonName());
            if (StringUtils.isBlank(saasUsageAndDosageReviewDrugDto.getSpecification())) {
                specificationSet.add(saasUsageAndDosageReviewDrugDto.getSpecification());
            }
        }

        // 查询数据库已经存在的通用名和规格
        return saasUsageAndDosageReviewDrugService.queryByCondition(SaasUsageAndDosageReviewDrugQueryDto.builder().commonNameSet(commonNameSet).specificationSet(specificationSet).build());
    }

}