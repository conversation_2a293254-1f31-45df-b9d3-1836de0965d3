package com.xyy.saas.inquiry.common.service.migration;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_BIND_OLD_HY_HAS_NO_BIND_FAIL;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_PDF_DOWN_FAIL;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery.Builder;
import co.elastic.clients.elasticsearch._types.query_dsl.RangeQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.TermsQueryField;
import co.elastic.clients.elasticsearch._types.query_dsl.WildcardQuery;
import co.elastic.clients.json.JsonData;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.common.constant.MigrationConstant;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationDetailVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationEsDto;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationExportVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationImInfoRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationInfoRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationQueryVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationRespVo;
import com.xyy.saas.inquiry.common.controller.admin.migration.vo.PrescriptionMigrationStoreExportVo;
import com.xyy.saas.inquiry.common.convert.migration.MigrationConvert;
import com.xyy.saas.inquiry.common.enums.migration.MigrationTypeEnum;
import com.xyy.saas.inquiry.common.enums.migration.PerscriptionStatusEnum;
import com.xyy.saas.inquiry.common.enums.migration.PrescriptionMigrationAuditStatusEnum;
import com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceEnum;
import com.xyy.saas.inquiry.common.enums.migration.PrescriptionSourceExtEnum;
import com.xyy.saas.inquiry.common.enums.migration.SourceAndClientTypeEnum;
import com.xyy.saas.inquiry.common.enums.migration.ThirdPlatformClientTypeEnum;
import com.xyy.saas.inquiry.common.mq.message.migration.MigrationPrescriptionEvent;
import com.xyy.saas.inquiry.common.mq.message.migration.dto.MigrationPrescriptionEventDto;
import com.xyy.saas.inquiry.common.mq.producer.migration.MigrationPrescription2EsMQProducer;
import com.xyy.saas.inquiry.common.util.SwitchUtil;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.pharmacist.api.migration.PrescriptionMigrationApi;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPrescriptionReqDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationDetailDto;
import com.xyy.saas.inquiry.pojo.migration.PrescriptionMigrationInfoDto;
import com.xyy.saas.inquiry.util.ThreadPoolManager;
import com.xyy.saas.inquiry.util.TimeWatchUtil;
import com.xyy.saas.inquiry.util.UrlConUtil;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.IndexedObjectInformation;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.IndexQueryBuilder;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

/**
 * 处方迁移服务实现类
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/20 21:14
 */
@Slf4j
@Service
public class MigrationPrescriptionServiceImpl implements MigrationPrescriptionService {


    @Resource
    private PrescriptionMigrationApi prescriptionMigrationApi;

    @Resource
    protected StringRedisTemplate stringRedisTemplate;

    @Resource
    private ElasticsearchOperations elasticsearchOperations;

    @Resource
    private MigrationPrescription2EsMQProducer migrationPrescription2EsMQProducer;

    @Resource
    private ConfigApi configApi;

    @Resource
    private MigrationPrescriptionFailRecordService failRecordService;

    @Autowired
    private TenantApi tenantApi;

    /**
     * 获取迁移延迟时间 默认1000ms
     */
    public Long getMigrationDelayTime() {
        return NumberUtils.toLong(configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_DELAY_TIME), 1000);
    }

    /**
     * 获取迁移分页大小 默认1000
     */
    private Integer getMigrationPageSize() {
        return NumberUtils.toInt(configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_PAGE_SIZE), 1000);
    }

    /**
     * 获取迁移开关 默认开启
     */
    private boolean getMigrationSwitch() {
        String byKey = configApi.getConfigValueByKey(MigrationConstant.MIGRATION_PRESCRIPTION_SWITCH);
        return StringUtils.isBlank(byKey) || StringUtils.equals(byKey, "1");
    }


    @Override
    public void migrationPrescription(MigrationPrescriptionReqDto reqDto) {
        log.info("开始处方迁移任务，参数：{}", JSONUtil.toJsonStr(reqDto));

        Boolean b = stringRedisTemplate.opsForValue().setIfAbsent(MigrationConstant.MIGRATION_PRESCRIPTION_FLAG_KEY, "1");
        if (Boolean.FALSE.equals(b)) {
            throw new RuntimeException("处方迁移任务正在执行中，请勿重复执行");
        }
        // 执行迁移id分发
        ThreadPoolManager.execute(() -> executeMigrationDistribution(reqDto));
    }

    // 开始执行迁移
    private void executeMigrationDistribution(MigrationPrescriptionReqDto reqDto) {

        Long minId = reqDto.getMinId();
        Long maxId = reqDto.getMaxId();
        String organSign = reqDto.getOrganSign();
        String rangeId = minId + "-" + maxId;

        try {
            // 根据 minId 按照 getMigrationPageSize() 步长递增累加
            Long currentMinId = minId;
            while (currentMinId < maxId) {
                // 检查迁移开关 - 在每次循环中判断
                if (!getMigrationSwitch()) {
                    log.warn("处方迁移到ES 开关已关闭，停止迁移任务");
                    break;
                }
                Long currentMaxId = Math.min(currentMinId + getMigrationPageSize(), maxId);
                // 创建分页消息
                MigrationPrescriptionEventDto eventDto = new MigrationPrescriptionEventDto()
                    .setMinId(currentMinId)
                    .setMaxId(currentMaxId)
                    .setRangeId(rangeId)
                    .setOrganSign(organSign);

                // migrationPrescription2Es(eventDto);
                // 发送MQ消息
                migrationPrescription2EsMQProducer.sendMessage(MigrationPrescriptionEvent.builder().msg(eventDto).build());

                log.info("处方迁移到ES,发送处方迁移MQ消息，ID范围：{}-{}，机构：{}", currentMinId, currentMaxId, organSign);

                // 延迟处理，避免对系统造成过大压力
                if (getMigrationDelayTime() > 0) {
                    try {
                        Thread.sleep(getMigrationDelayTime());
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("处方迁移到ES,延迟等待被中断", e);
                        break;
                    }
                }
                currentMinId = currentMaxId;
            }
            log.info("处方迁移到ES,任务分发完成，总范围：{}-{},机构：{}", minId, maxId, organSign);

        } catch (Exception e) {
            log.error("处方迁移到ES,任务执行失败，参数：{}", JSONUtil.toJsonStr(reqDto), e);
            // 记录失败信息到数据库
            failRecordService.createMqFailRecord(organSign, minId, maxId, e.getMessage());
        } finally {
            stringRedisTemplate.delete(MigrationConstant.MIGRATION_PRESCRIPTION_FLAG_KEY);
        }

    }


    @Override
    public void migrationPrescription2Es(MigrationPrescriptionEventDto msg) {
        // 检查迁移开关
        if (!getMigrationSwitch()) {
            log.warn("处方迁移开关已关闭，跳过ES迁移任务");
            return;
        }
        Long minId = msg.getMinId();
        Long maxId = msg.getMaxId();
        String organSign = msg.getOrganSign();

        log.info("处方迁移到ES，ID范围：{}-{},机构：{}", minId, maxId, organSign);

        try {
            incrementMigrationRedisCount(msg.getRangeId(), maxId, minId);

            // 执行老系统的查询
            List<PrescriptionMigrationInfoDto> migrationInfoDtos = TimeWatchUtil.excute(() -> prescriptionMigrationApi
                .queryPrescriptionMigrationInfo(MigrationConvert.INSTANCE.convertDto(msg)), "处方迁移到ES 查询老系统处方");

            if (CollUtil.isEmpty(migrationInfoDtos)) {
                log.info("处方迁移到ES未查询到处方数据，ID范围：{}-{},机构：{}", minId, maxId, organSign);
                return;
            }

            // 批量写入ES，支持更新已存在文档
            int successCount = TimeWatchUtil.excute(() -> batchSaveToEs(migrationInfoDtos), "处方迁移到ES 批量写入ES,数量:" + migrationInfoDtos.size());

            log.info("处方迁移到ES完成，ID范围：{}-{} , 机构：{}，成功数量：{}", minId, maxId, organSign, successCount);

            failRecordService.createEsSuccessRecord(organSign, minId, maxId, successCount);

        } catch (Exception e) {
            log.error("处方迁移到ES，失败，ID范围：{}-{}, 机构：{}", minId, maxId, organSign, e);
            failRecordService.createEsFailRecord(organSign, minId, maxId, e.getMessage());
        }
    }

    // Redis键定义 累加迁移数量
    private void incrementMigrationRedisCount(String rangId, Long maxId, Long minId) {
        String migrationCountKey = MigrationConstant.MIGRATION_PRESCRIPTION_ES_PROGRESS_KEY + rangId;
        stringRedisTemplate.opsForValue().increment(migrationCountKey, maxId - minId);
        stringRedisTemplate.expire(migrationCountKey, 30, TimeUnit.DAYS);
    }

    /**
     * 根据创建时间获取ES索引名称 格式：inquiry_history_prescription_yyyy
     */
    private String getIndexNameByCreateTime(String createTime) {
        if (StringUtils.isBlank(createTime)) {
            // 如果创建时间为空，使用当前年份
            return MigrationConstant.HISTORY_PRESCRIPTION_INDEX_PREFIX + LocalDateTime.now().getYear();
        }
        // 提取年份
        // LocalDateTime dateTime = createTime.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();

        return MigrationConstant.HISTORY_PRESCRIPTION_INDEX_PREFIX + createTime.substring(0, 4);
    }

    /**
     * 根据机构号获取路由键 直接使用organSign作为路由键，确保数据分布的唯一性
     */
    private String getRoutingByOrganSign(String organSign) {
        if (StrUtil.isBlank(organSign)) {
            return "default";
        }

        // 直接使用机构号作为路由键，保证同一机构的数据路由到同一分片
        return organSign;
    }

    /**
     * 批量保存数据到ES，支持更新已存在文档 优化：根据创建时间区分索引，根据机构号设置路由，按索引+路由进行二级分组
     */
    private int batchSaveToEs(List<PrescriptionMigrationInfoDto> migrationInfoDtos) {
        if (CollUtil.isEmpty(migrationInfoDtos)) {
            log.warn("处方迁移到ES 数据为空，跳过处理");
            return 0;
        }

        List<PrescriptionMigrationEsDto> prescriptionMigrationEsDtos = MigrationConvert.INSTANCE.convertInfo2EsDto(migrationInfoDtos);

        int successCount = 0;

        // 按索引+路由进行二级分组，确保相同路由的数据批量写入到同一分片
        Map<String, Map<String, List<PrescriptionMigrationEsDto>>> indexRoutingGroupMap = prescriptionMigrationEsDtos.stream()
            .collect(Collectors.groupingBy(
                info -> getIndexNameByCreateTime(info.getCreateTime()),
                Collectors.groupingBy(info -> getRoutingByOrganSign(info.getOrganSign()))
            ));

        // log.info("处方迁移到ES 开始批量保存，总数量：{}，索引数量：{}", migrationInfoDtos.size(), indexRoutingGroupMap.size());

        // 遍历每个索引
        for (Map.Entry<String, Map<String, List<PrescriptionMigrationEsDto>>> indexEntry : indexRoutingGroupMap.entrySet()) {
            String indexName = indexEntry.getKey();
            Map<String, List<PrescriptionMigrationEsDto>> routingGroupMap = indexEntry.getValue();

            // 确保索引存在
            IndexCoordinates indexCoordinates = IndexCoordinates.of(indexName);
            IndexOperations indexOps = elasticsearchOperations.indexOps(indexCoordinates);
            if (!indexOps.exists()) {
                log.info("处方迁移到ES 没有 ES索引 跳过：{}", indexName);
                continue;
            }

            // 遍历每个路由分组
            for (Map.Entry<String, List<PrescriptionMigrationEsDto>> routingEntry : routingGroupMap.entrySet()) {
                String routing = routingEntry.getKey();
                List<PrescriptionMigrationEsDto> routingData = routingEntry.getValue();

                try {
                    // 批量构建文档 - 使用save操作支持更新
                    List<IndexQuery> indexQueries = new ArrayList<>();
                    for (PrescriptionMigrationEsDto prescriptionInfo : routingData) {
                        // 验证必要字段
                        if (prescriptionInfo.getId() == null) {
                            log.warn("处方迁移到ES 跳过无效数据，ID为空：{}", prescriptionInfo.getGuid());
                            continue;
                        }

                        IndexQuery indexQuery = new IndexQueryBuilder()
                            .withId(String.valueOf(prescriptionInfo.getId()))
                            .withObject(prescriptionInfo)
                            .withRouting(routing)
                            .build();
                        indexQueries.add(indexQuery);
                    }

                    if (CollUtil.isEmpty(indexQueries)) {
                        log.warn("处方迁移到ES 索引：{}，路由：{} 无有效数据", indexName, routing);
                        continue;
                    }

                    // 批量写入ES（如果文档存在则更新）
                    List<IndexedObjectInformation> results = elasticsearchOperations.bulkIndex(indexQueries, indexCoordinates);
                    successCount += results.size();

                    // log.info("处方迁移到ES 批量保存ES成功，索引：{}，路由：{}，数量：{}/{}",
                    //     indexName, routing, results.size(), routingData.size());

                } catch (Exception e) {
                    log.error("处方迁移到ES 批量保存ES失败，索引：{}，路由：{}，数量：{}，错误：{}",
                        indexName, routing, routingData.size(), e.getMessage(), e);
                    throw e;
                }
            }
        }

        return successCount;
    }

    @Override
    @InquiryDateType
    public PageResult<PrescriptionMigrationRespVo> pageSystemQueryMigrationPrescription(PrescriptionMigrationQueryVo reqDto) {
        handleQueryVo(reqDto);

        PageResult<PrescriptionMigrationEsDto> pageResult = pageQueryMigrationPrescription(reqDto);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }

        List<PrescriptionMigrationRespVo> list = pageResult.getList().stream().map(esDto -> {
            PrescriptionMigrationRespVo vo = MigrationConvert.INSTANCE.convertRespVo(esDto);
            // 转换展示字段
            vo.setSourceInfo(SourceAndClientTypeEnum.convertSourceInfo(esDto.getClientType(), esDto.getSource()));
            vo.setStatusStr(PerscriptionStatusEnum.getMsg(esDto.getStatus()));
            vo.setAuditStatusStr(PrescriptionMigrationAuditStatusEnum.getMsg(esDto.getAuditStatus()));
            return vo;
        }).toList();
        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    @InquiryDateType
    public PageResult<PrescriptionMigrationRespVo> pageStoreQueryMigrationPrescription(PrescriptionMigrationQueryVo reqDto) {

        handleStoreQueryVo(reqDto);

        PageResult<PrescriptionMigrationEsDto> pageResult = pageQueryMigrationPrescription(reqDto);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return PageResult.empty();
        }

        List<PrescriptionMigrationRespVo> list = pageResult.getList().stream().map(esDto -> {
            PrescriptionMigrationRespVo vo = MigrationConvert.INSTANCE.convertRespVo(esDto);
            vo.setSourceInfo(PrescriptionSourceEnum.getSourceInfo(esDto.getSource(), esDto.getType()));
            vo.setStatusStr(PerscriptionStatusEnum.getDisplayStatus(esDto.getStatus()));
            vo.setAuditStatusStr(PerscriptionStatusEnum.getDisplayAuditStatus(esDto.getAuditStatus()));
            return vo;
        }).toList();

        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    public PageResult<PrescriptionMigrationEsDto> pageQueryMigrationPrescription(PrescriptionMigrationQueryVo queryVo) {

        setQueryOrganSignRoute(queryVo);

        try {
            // 1. 根据年份确定索引名称
            String indexName = getIndexNameByYear(queryVo.getYear());
            IndexCoordinates indexCoordinates = IndexCoordinates.of(indexName);

            // 2. 检查索引是否存在
            IndexOperations indexOps = elasticsearchOperations.indexOps(indexCoordinates);
            if (!indexOps.exists()) {
                log.warn("查询ES处方数据 索引不存在：{}", indexName);
                return new PageResult<>();
            }

            // 4. 创建分页请求，按创建时间降序排序
            PageRequest pageRequest = PageRequest.of(
                queryVo.getPageNo() - 1,
                queryVo.getPageSize(),
                Sort.by(Sort.Direction.DESC, "id")
            );

            // 使用原生查询，支持精确的 wildcard 模糊匹配
            co.elastic.clients.elasticsearch._types.query_dsl.Query nativeEsQuery = buildNativeSearchQuery(queryVo);
            org.springframework.data.elasticsearch.core.query.Query query = NativeQuery.builder()
                .withQuery(nativeEsQuery)
                .withPageable(pageRequest)
                // .withTrackTotalHits(TenantConstant.isSystemTenant() ? null : true)
                .withTrackTotalHits(true)
                .withRoute(StringUtils.isNotBlank(queryVo.getOrganSign()) ? getRoutingByOrganSign(queryVo.getOrganSign()) : null)
                .build();

            log.info("查询 DSL语句：{}", nativeEsQuery);

            // 打印查询条件用于调试
            log.info("查询ES处方数据 索引：{}，机构号：{}，年份：{}，分页：{}/{}",
                indexName, queryVo.getOrganSign(), queryVo.getYear(), queryVo.getPageNo(), queryVo.getPageSize());

            // 6. 执行查询
            SearchHits<PrescriptionMigrationEsDto> searchHits = elasticsearchOperations.search(
                query, PrescriptionMigrationEsDto.class, indexCoordinates
            );

            if (searchHits.isEmpty()) {
                return new PageResult<>();
            }

            // 8. 处理查询结果
            List<PrescriptionMigrationEsDto> results = new ArrayList<>();
            for (SearchHit<PrescriptionMigrationEsDto> hit : searchHits) {
                results.add(hit.getContent());
            }

            // 9. 构建分页结果
            PageResult<PrescriptionMigrationEsDto> pageResult = new PageResult<>();
            pageResult.setTotal(searchHits.getTotalHits());
            pageResult.setList(results);
            return pageResult;

        } catch (Exception e) {
            log.error("查询ES处方数据 失败，参数：{}，错误：{}", JSONUtil.toJsonStr(queryVo), e.getMessage(), e);
            return new PageResult<>();
        }
    }


    private void setQueryOrganSignRoute(PrescriptionMigrationQueryVo queryVo) {
        if (!TenantConstant.isSystemTenant()) {
            TenantDto tenantDto = tenantApi.getTenant();
            if (tenantDto == null || tenantDto.getExt() == null || StringUtils.isBlank(tenantDto.getExt().getOrganSign())) {
                throw exception(TENANT_BIND_OLD_HY_HAS_NO_BIND_FAIL);
            }
            queryVo.setOrganSign(tenantDto.getExt().getOrganSign());
        }
    }


    @Override
    @InquiryDateType
    public PrescriptionMigrationInfoRespVo queryMigrationPrescription(PrescriptionMigrationQueryVo queryVo) {

        PageResult<PrescriptionMigrationEsDto> pageResult = pageQueryMigrationPrescription(queryVo);

        if (pageResult.getList().isEmpty()) {
            log.warn("查询ES单个处方数据 未找到结果：ID={}, pref={}", queryVo.getId(), queryVo.getPref());
            return null;
        }

        PrescriptionMigrationEsDto infoDto = pageResult.getList().getFirst();
        PrescriptionMigrationInfoRespVo vo = MigrationConvert.INSTANCE.convertVo(infoDto);

        if (TenantConstant.isSystemTenant()) {
            vo.setSourceInfo(SourceAndClientTypeEnum.convertSourceInfo(vo.getClientType(), vo.getSource()));
            vo.setStatusStr(PerscriptionStatusEnum.getMsg(vo.getStatus()));
            vo.setAuditStatusStr(PrescriptionMigrationAuditStatusEnum.getMsg(vo.getAuditStatus()));
            if (JSONUtil.isTypeJSON(infoDto.getExt())) {
                JSONObject jsonObject = JSONObject.parseObject(infoDto.getExt());
                vo.setDisuseReason(jsonObject.getString("disuseReason"));
                vo.setDisuseTime(jsonObject.getString("disuseTime"));
                vo.setDisuseOperator(jsonObject.getString("disuseOperator"));
            }
        } else {
            vo.setSourceInfo(PrescriptionSourceEnum.getSourceInfo(vo.getSource(), vo.getType()));
            vo.setStatusStr(PerscriptionStatusEnum.getDisplayStatus(vo.getStatus()));
            vo.setAuditStatusStr(PerscriptionStatusEnum.getDisplayAuditStatus(vo.getAuditStatus()));
        }
        return vo;
    }

    /**
     * 根据年份获取索引名称
     */
    private String getIndexNameByYear(String year) {
        if (StrUtil.isBlank(year)) {
            // 如果年份为空，使用当前年份
            year = String.valueOf(LocalDateTime.now().getYear());
        }
        return MigrationConstant.HISTORY_PRESCRIPTION_INDEX_PREFIX + year;
    }


    /**
     * 构建原生 Elasticsearch 查询 使用 BoolQuery 组合多个条件，支持精确的模糊查询
     */
    private co.elastic.clients.elasticsearch._types.query_dsl.Query buildNativeSearchQuery(PrescriptionMigrationQueryVo queryVo) {
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();
        // ID相关查询
        buildQueryMust(boolQueryBuilder, "id", queryVo.getId());
        buildQueryMust(boolQueryBuilder, "pref", queryVo.getPref());
        buildQueryMust(boolQueryBuilder, "organSign", queryVo.getOrganSign());
        if (CollUtil.isNotEmpty(queryVo.getIds())) {
            boolQueryBuilder.filter(TermsQuery.of(t -> t
                .field("id")
                .terms(TermsQueryField.of(tf -> tf.value(queryVo.getIds().stream()
                    .map(id -> FieldValue.of(String.valueOf(id)))
                    .collect(Collectors.toList()))))
            )._toQuery());
        }
        // 文本模糊查询 - 使用 wildcard 查询实现 SQL LIKE '%value%' 效果
        buildQueryMatchPhrase(boolQueryBuilder, "drugstoreName", queryVo.getDrugstoreName());
        buildQueryMatchPhrase(boolQueryBuilder, "patientName", queryVo.getPatientName());
        buildQueryMatchPhrase(boolQueryBuilder, "productName", queryVo.getProductName());
        buildQueryMatchPhrase(boolQueryBuilder, "pharmacistName", queryVo.getPharmacistName());
        buildQueryMatchPhrase(boolQueryBuilder, "physicianName", queryVo.getPhysicianName());
        // = 其他精确匹配查询
        buildQueryMust(boolQueryBuilder, "inquiryNo", queryVo.getInquiryNo());
        buildQueryMust(boolQueryBuilder, "telephone", queryVo.getTelephone());
        buildQueryMust(boolQueryBuilder, "physicianId", queryVo.getPhysicianId());
        buildQueryMust(boolQueryBuilder, "inquiryGuid", queryVo.getInquiryGuid());
        buildQueryMust(boolQueryBuilder, "inquiryStatus", queryVo.getInquiryStatus());
        buildQueryMust(boolQueryBuilder, "auditStatus", queryVo.getAuditStatus());
        buildQueryMust(boolQueryBuilder, "auditType", queryVo.getAuditType());
        buildQueryMust(boolQueryBuilder, "medicineType", queryVo.getMedicineType());
        buildQueryMust(boolQueryBuilder, "type", queryVo.getType());
        buildQueryMust(boolQueryBuilder, "source", queryVo.getSource());
        buildQueryMust(boolQueryBuilder, "clientType", queryVo.getClientType());
        buildQueryMust(boolQueryBuilder, "inquirySource", queryVo.getInquirySource());
        // !=
        if (queryVo.getNeAuditType() != null) {
            boolQueryBuilder.mustNot(TermQuery.of(t -> t.field("auditType").value(queryVo.getNeAuditType()))._toQuery());
        }
        if (queryVo.getNeqType() != null) {
            boolQueryBuilder.mustNot(TermQuery.of(t -> t.field("type").value(queryVo.getNeqType()))._toQuery());
        }
        // 处方状态（支持单个和列表）
        buildListMust(queryVo, boolQueryBuilder);
        // 时间范围查询
        buildTime(queryVo, boolQueryBuilder);
        return boolQueryBuilder.build()._toQuery();
    }

    private static void buildTime(PrescriptionMigrationQueryVo queryVo, Builder boolQueryBuilder) {
        // 时间范围查询
        if (queryVo.getOutPrescriptionTime() != null && queryVo.getOutPrescriptionTime().length == 2) {
            LocalDateTime startTime = queryVo.getOutPrescriptionTime()[0];
            LocalDateTime endTime = queryVo.getOutPrescriptionTime()[1];
            if (startTime != null && endTime != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                boolQueryBuilder.must(RangeQuery.of(r -> r
                    .field("outPrescriptionTime")
                    .gte(JsonData.of(startTime.format(formatter)))
                    .lte(JsonData.of(endTime.format(formatter)))
                )._toQuery());
            }
        }

        if (queryVo.getApprovalTime() != null && queryVo.getApprovalTime().length == 2) {
            LocalDateTime startTime = queryVo.getApprovalTime()[0];
            LocalDateTime endTime = queryVo.getApprovalTime()[1];

            if (startTime != null && endTime != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

                boolQueryBuilder.must(RangeQuery.of(r -> r
                    .field("approvalTime")
                    .gte(JsonData.of(startTime.format(formatter)))
                    .lte(JsonData.of(endTime.format(formatter)))
                )._toQuery());

            }
        }
    }

    private static void buildListMust(PrescriptionMigrationQueryVo queryVo, Builder boolQueryBuilder) {
        if (queryVo.getStatus() != null) {
            boolQueryBuilder.must(TermQuery.of(t -> t.field("status").value(queryVo.getStatus()))._toQuery());
        } else if (CollUtil.isNotEmpty(queryVo.getStatusList())) {
            List<String> statusStrList = queryVo.getStatusList().stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
            boolQueryBuilder.filter(TermsQuery.of(t -> t
                .field("status")
                .terms(TermsQueryField.of(tf -> tf.value(
                    statusStrList.stream()
                        .map(v -> FieldValue.of(v))
                        .collect(Collectors.toList())
                )))
            )._toQuery());
        }

        // 审批状态
        if (queryVo.getAuditStatus() != null) {
            boolQueryBuilder.must(TermQuery.of(t -> t.field("auditStatus").value(queryVo.getAuditStatus()))._toQuery());
        } else if (CollUtil.isNotEmpty(queryVo.getAuditStatusList())) {
            List<String> statusStrList = queryVo.getAuditStatusList().stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
            boolQueryBuilder.filter(TermsQuery.of(t -> t
                .field("auditStatus")
                .terms(TermsQueryField.of(tf -> tf.value(
                    statusStrList.stream()
                        .map(v -> FieldValue.of(v))
                        .collect(Collectors.toList())
                )))
            )._toQuery());
        }
    }

    private void buildQueryMust(Builder boolQueryBuilder, String field, String value) {
        if (StrUtil.isNotBlank(value)) {
            boolQueryBuilder.must(TermQuery.of(t -> t.field(field).value(value))._toQuery());
        }
    }

    private void buildQueryMust(Builder boolQueryBuilder, String field, Integer value) {
        if (value != null) {
            boolQueryBuilder.must(TermQuery.of(t -> t.field(field).value(value))._toQuery());
        }
    }

    private void buildQueryMust(Builder boolQueryBuilder, String field, Long value) {
        if (value != null) {
            boolQueryBuilder.must(TermQuery.of(t -> t.field(field).value(value))._toQuery());
        }
    }

    /**
     * 效率更低，建议使用短语匹配， {"type":"text","fields":{"keyword":{"type":"keyword"}}} 使用text自动分词
     *
     * @param boolQueryBuilder
     * @param field
     * @param value
     */
    private void buildQueryWildcard(BoolQuery.Builder boolQueryBuilder, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            String escapedValue = SwitchUtil.replaceEsWildLikeStr(value);
            String wildcardValue = "*" + escapedValue + "*";
            boolQueryBuilder.filter(WildcardQuery.of(w -> w
                .field(field)
                .value(wildcardValue)
            )._toQuery());
        }
    }

    /**
     * 构建短语匹配查询
     *
     * @param boolQueryBuilder BoolQuery.Builder 对象
     * @param field            要查询的字段名
     * @param value            查询值
     */
    private void buildQueryMatchPhrase(Builder boolQueryBuilder, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            boolQueryBuilder.must(co.elastic.clients.elasticsearch._types.query_dsl.MatchPhraseQuery.of(m -> m
                .field(field)
                .query(value)
            )._toQuery());
        }
    }


    @Override
    public List<PrescriptionMigrationExportVo> exportSystemQueryMigrationPrescription(PrescriptionMigrationQueryVo reqDto) {

        handleQueryVo(reqDto);

        reqDto.setPageSize(5000);
        PageResult<PrescriptionMigrationEsDto> pageResult = pageQueryMigrationPrescription(reqDto);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return List.of();
        }

        List<PrescriptionMigrationExportVo> resultList = new ArrayList<>();

        for (PrescriptionMigrationEsDto info : pageResult.getList()) {
            try {
                // 1. 基础转换并填充中药信息
                PrescriptionMigrationExportVo baseVo = MigrationConvert.INSTANCE.convertExportVo(info);
                MigrationConvert.INSTANCE.fillTcmInfo(baseVo, info.getDosageAndUsage());
                baseVo.setAuditStatusStr(PrescriptionMigrationAuditStatusEnum.getMsg(info.getAuditStatus()));
                baseVo.setSourceInfo(SourceAndClientTypeEnum.convertSourceInfo(info.getClientType(), info.getSource()));
                baseVo.setTypeStr(MigrationTypeEnum.getMsg(info.getType()));

                // 2. 解析处方明细
                List<PrescriptionMigrationDetailDto> details = getPrescriptionMigrationDetailDtos(info);

                // 3. 生成导出记录
                if (CollUtil.isEmpty(details)) {
                    resultList.add(baseVo);
                } else {
                    for (int i = 0; i < details.size(); i++) {
                        PrescriptionMigrationExportVo exportVo = i == 0 ? baseVo : new PrescriptionMigrationExportVo();
                        if (i > 0) {
                            exportVo.setPref(baseVo.getPref());
                        }
                        MigrationConvert.INSTANCE.fillProductInfo(exportVo, details.get(i));

                        resultList.add(exportVo);
                    }
                }
            } catch (Exception e) {
                log.error("处理处方失败，ID：{}", info.getId(), e);
                resultList.add(MigrationConvert.INSTANCE.convertExportVo(info));
            }
        }

        return resultList;
    }

    @Override
    @InquiryDateType
    public List<PrescriptionMigrationStoreExportVo> exportStoreQueryMigrationPrescription(PrescriptionMigrationQueryVo reqDto) {
        reqDto.setPageSize(5000);
        handleStoreQueryVo(reqDto);

        PageResult<PrescriptionMigrationEsDto> pageResult = pageQueryMigrationPrescription(reqDto);

        if (CollUtil.isEmpty(pageResult.getList())) {
            return List.of();
        }

        List<PrescriptionMigrationExportVo> resultList = new ArrayList<>();

        for (PrescriptionMigrationEsDto info : pageResult.getList()) {
            try {
                // 解析处方明细
                List<PrescriptionMigrationDetailDto> details = getPrescriptionMigrationDetailDtos(info);

                // 生成导出记录 - 每行都显示完整处方信息
                if (CollUtil.isEmpty(details)) {
                    PrescriptionMigrationExportVo exportVo = MigrationConvert.INSTANCE.convertExportVo(info);
                    MigrationConvert.INSTANCE.fillTcmInfo(exportVo, info.getDosageAndUsage());
                    exportVo.setStatusStr(PerscriptionStatusEnum.getDisplayStatus(info.getStatus()));
                    exportVo.setPatientSex(StringUtils.equals(info.getPatientSex(), "1") ? "男" : "女");
                    exportVo.setAuditStatusStr(PerscriptionStatusEnum.getDisplayAuditStatus(info.getAuditStatus()));
                    resultList.add(exportVo);
                } else {
                    for (PrescriptionMigrationDetailDto detail : details) {
                        // 每个明细都创建完整的处方信息副本
                        PrescriptionMigrationExportVo exportVo = MigrationConvert.INSTANCE.convertExportVo(info);
                        MigrationConvert.INSTANCE.fillTcmInfo(exportVo, info.getDosageAndUsage());
                        MigrationConvert.INSTANCE.fillProductInfo(exportVo, detail);
                        exportVo.setStatusStr(PerscriptionStatusEnum.getDisplayStatus(info.getStatus()));
                        exportVo.setPatientSex(StringUtils.equals(info.getPatientSex(), "1") ? "男" : "女");
                        exportVo.setAuditStatusStr(PerscriptionStatusEnum.getDisplayAuditStatus(info.getAuditStatus()));
                        resultList.add(exportVo);
                    }
                }
            } catch (Exception e) {
                log.error("处理处方失败，ID：{}", info.getId(), e);
                // resultList.add(MigrationConvert.INSTANCE.convertExportVo(info));
            }
        }

        return MigrationConvert.INSTANCE.convertStoreExcelVos(resultList);
    }


    @Override
    @InquiryDateType
    public List<PrescriptionMigrationImInfoRespVo> queryMigrationPrescriptionImList(PrescriptionMigrationQueryVo reqDto) {
        PrescriptionMigrationInfoRespVo infoRespVo = queryMigrationPrescription(reqDto);
        if (infoRespVo == null) {
            return List.of();
        }

        List<PrescriptionMigrationImInfoRespVo> messageList = new ArrayList<>();

        // 获取时间范围
        LocalDateTime startTime = StringUtils.isNotBlank(infoRespVo.getStartTime()) ?
            LocalDateTime.parse(infoRespVo.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) :
            LocalDateTime.parse(infoRespVo.getOutPrescriptionTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime endTime = StringUtils.isNotBlank(infoRespVo.getEndTime()) ?
            LocalDateTime.parse(infoRespVo.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) :
            LocalDateTime.parse(infoRespVo.getOutPrescriptionTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        // 计算消息总数和时间间隔
        int totalMessages = 10;
        long totalSeconds = java.time.Duration.between(startTime, endTime).getSeconds();
        long intervalSeconds = totalSeconds / (totalMessages - 1);

        LocalDateTime currentTime = startTime;

        // 1. 医生：请问，您是否在线下医疗机构就诊过，并使用过此次预购药品？
        messageList.add(createMessage(infoRespVo.getPhysicianName(),
            "请问，您是否在线下医疗机构就诊过，并使用过此次预购药品？", currentTime));
        currentTime = currentTime.plusSeconds(intervalSeconds);

        // 2. 患者：是
        messageList.add(createMessage(infoRespVo.getPatientName(), "是", currentTime));
        currentTime = currentTime.plusSeconds(intervalSeconds);

        // 3. 医生：请问，您是否有过敏史?
        messageList.add(createMessage(infoRespVo.getPhysicianName(), "请问，您是否有过敏史?", currentTime));
        currentTime = currentTime.plusSeconds(intervalSeconds);

        // 4. 患者：根据allergySymptomExplain判断
        String allergyResponse = buildAllergyResponse(infoRespVo.getAllergySymptomExplain());
        messageList.add(createMessage(infoRespVo.getPatientName(), allergyResponse, currentTime));
        currentTime = currentTime.plusSeconds(intervalSeconds);

        // 5. 医生：请问，您的肝肾功能有无异常？
        messageList.add(createMessage(infoRespVo.getPhysicianName(), "请问，您的肝肾功能有无异常？", currentTime));
        currentTime = currentTime.plusSeconds(intervalSeconds);

        // 6. 患者：根据liverAndRenalFunction判断
        String liverKidneyResponse = buildLiverKidneyResponse(infoRespVo.getLiverAndRenalFunction());
        messageList.add(createMessage(infoRespVo.getPatientName(), liverKidneyResponse, currentTime));
        currentTime = currentTime.plusSeconds(intervalSeconds);

        // 7. 患者：用药申请
        String medicationRequest = buildMedicationRequest(infoRespVo);
        messageList.add(createMessage(infoRespVo.getPatientName(), medicationRequest, currentTime));
        currentTime = currentTime.plusSeconds(intervalSeconds);

        // 8. 医生：消息
        messageList.add(createMessage(infoRespVo.getPhysicianName(), "消息", currentTime));
        currentTime = currentTime.plusSeconds(intervalSeconds);

        // 9. 医生：您好，您的处方已开具，请查收
        messageList.add(createMessage(infoRespVo.getPhysicianName(), "您好，您的处方已开具，请查收", currentTime));
        currentTime = currentTime.plusSeconds(intervalSeconds);

        // 10. 医生：温馨提示
        messageList.add(createMessage(infoRespVo.getPhysicianName(), "温馨提示: 用药过程中如有不适，请及时就医，不适随诊", currentTime));

        return messageList;
    }


    @Override
    public void downloadStoreQueryMigrationPrescription(PrescriptionMigrationQueryVo reqDto, HttpServletResponse response) {
        // 查询处方
        reqDto.setPageSize(reqDto.getIds().size());
        handleStoreQueryVo(reqDto);
        PageResult<PrescriptionMigrationEsDto> pageResult = pageQueryMigrationPrescription(reqDto);

        if (CollUtil.isEmpty(pageResult.getList())) {
            throw exception(INQUIRY_PRESCRIPTION_PDF_DOWN_FAIL, "没有可以下载的处方");
        }

        // 批量下载文件
        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream(), StandardCharsets.UTF_8)) {

            response.setContentType("application/zip");

            pageResult.getList().forEach(prescription -> {
                String url = StringUtils.defaultIfBlank(prescription.getPrescriptionImage(), prescription.getDiagnosisPdfUrl());

                if (StringUtils.isNotBlank(url)) {
                    String fileName =
                        buildFileName(prescription.getPatientName() + "-" + prescription.getPref() + "-" + prescription.getOutPrescriptionTime().substring(0, 10),
                            url);

                    try (InputStream in = UrlConUtil.getStreamRetry("GET", url, 3000)) {
                        ZipEntry entry = new ZipEntry(fileName);
                        synchronized (zos) {
                            zos.putNextEntry(entry);
                            IOUtils.copy(in, zos);
                            zos.closeEntry();
                        }
                    } catch (IOException e) {
                    }
                }
            });
        } catch (IOException e) {
            log.error("文件写入Zip异常：{}", e.getMessage(), e);
            throw exception(INQUIRY_PRESCRIPTION_PDF_DOWN_FAIL, e);
        }
    }


    /**
     * 创建消息对象
     */
    private PrescriptionMigrationImInfoRespVo createMessage(String sendName, String sendContent, LocalDateTime sendTime) {
        PrescriptionMigrationImInfoRespVo message = new PrescriptionMigrationImInfoRespVo();
        message.setSendName(sendName);
        message.setSendContent(sendContent);
        message.setSendTime(sendTime);
        return message;
    }

    /**
     * 构建过敏史响应
     */
    private String buildAllergyResponse(String allergySymptomExplain) {
        if (StrUtil.isBlank(allergySymptomExplain)) {
            return "无";
        }
        return allergySymptomExplain;
    }

    /**
     * 构建肝肾功能响应
     */
    private String buildLiverKidneyResponse(String liverAndRenalFunction) {
        if (StrUtil.isBlank(liverAndRenalFunction)) {
            return "无异常";
        }

        try {
            int code = Integer.parseInt(liverAndRenalFunction);
            switch (code) {
                case 0:
                    return "无异常";
                case 1:
                    return "肝功能异常";
                case 2:
                    return "肾功能异常";
                case 3:
                    return "肝、肾功能异常";
                default:
                    return "无异常";
            }
        } catch (NumberFormatException e) {
            return "无异常";
        }
    }

    /**
     * 构建用药申请内容
     */
    private String buildMedicationRequest(PrescriptionMigrationInfoRespVo infoRespVo) {
        StringBuilder sb = new StringBuilder();

        // 患者基本信息
        sb.append(StrUtil.blankToDefault(infoRespVo.getPatientName(), "患者"))
            .append(" ")
            .append(StrUtil.equals(infoRespVo.getPatientSex(), "1") ? "男" : "女")
            .append(" ")
            .append(StrUtil.blankToDefault(infoRespVo.getPatientAge(), ""))
            .append("\n");

        // 病情描述
        sb.append("病情描述：")
            .append(StrUtil.blankToDefault(infoRespVo.getMainSuit(), "无"))
            .append("\n");

        // 诊断
        sb.append("诊断：")
            .append(StrUtil.blankToDefault(infoRespVo.getDiagnosis(), "无"))
            .append("\n");

        // 过敏史
        sb.append("过敏史：")
            .append(buildAllergyResponse(infoRespVo.getAllergySymptomExplain()))
            .append("\n");

        // 肝肾功能
        sb.append("肝、肾功能异常：")
            .append(buildLiverKidneyResponse(infoRespVo.getLiverAndRenalFunction()))
            .append("\n");

        // 用药类型
        String medicineTypeName = getMedicineTypeName(infoRespVo.getMedicineType());
        sb.append("用药类型：")
            .append(medicineTypeName)
            .append("\n");

        // 预购药品
        List<PrescriptionMigrationDetailVo> details = infoRespVo.getPrescriptionDetailsList();
        if (CollUtil.isNotEmpty(details)) {
            sb.append("预购药品：");
            for (int i = 0; i < details.size(); i++) {
                PrescriptionMigrationDetailVo detail = details.get(i);
                if (i > 0) {
                    sb.append("、");
                }
                sb.append(StrUtil.blankToDefault(detail.getProductName(), "药品"))
                    .append(" X ")
                    .append(detail.getQuantity() != null ? detail.getQuantity().intValue() : 1);
            }
            sb.append("\n");
            sb.append("共").append(details.size()).append("种药");
        } else {
            sb.append("预购药品：\n");
        }

        return sb.toString();
    }

    /**
     * 获取用药类型名称
     */
    private String getMedicineTypeName(Integer medicineType) {
        if (medicineType == null) {
            return "西药";
        }

        if (medicineType == 0) {
            return "西药";
        } else if (medicineType == 1) {
            return "中药";
        } else {
            return "西药";
        }
    }

    private static List<PrescriptionMigrationDetailDto> getPrescriptionMigrationDetailDtos(PrescriptionMigrationEsDto info) {
        List<PrescriptionMigrationDetailDto> details = Collections.emptyList();
        if (StrUtil.isNotBlank(info.getPrescriptionDetails())) {
            try {
                details = JSONUtil.toList(info.getPrescriptionDetails(), PrescriptionMigrationDetailDto.class);
            } catch (Exception ignored) {
            }
        }
        return details;
    }


    private void handleStoreQueryVo(PrescriptionMigrationQueryVo reqDto) {
        if (reqDto.getStatus() != null && reqDto.getStatus().equals(PerscriptionStatusEnum.STATUS_11.getCode())) {
            List<Integer> statusList = new ArrayList<>();
            List<Integer> tempStatusList = Arrays.stream(PerscriptionStatusEnum.values()).map(PerscriptionStatusEnum::getCode).collect(Collectors.toList());
            tempStatusList.remove(PerscriptionStatusEnum.STATUS_11.getCode());
            tempStatusList.remove(PerscriptionStatusEnum.CREATE.getCode());
            tempStatusList.remove(PerscriptionStatusEnum.CLOSED.getCode());
            tempStatusList.remove(PerscriptionStatusEnum.OUTTIME.getCode());
            if (CollUtil.isNotEmpty(statusList)) {
                statusList = statusList.stream().filter(tempStatusList::contains).collect(Collectors.toList());
            } else {
                statusList = tempStatusList;
            }
            reqDto.setStatusList(statusList);
            reqDto.setStatus(null);
        }
        if (reqDto.getAuditType() == null) {
            reqDto.setNeAuditType(99);
        }

        PrescriptionSourceExtEnum.setSourceAndType(reqDto);
    }


    /**
     * 处理业务查询参数
     *
     * @param queryVo
     */
    private void handleQueryVo(PrescriptionMigrationQueryVo queryVo) {
        if (queryVo.getSourceAndClientType() != null) {
            Integer sourceAndClientType = queryVo.getSourceAndClientType();
            SourceAndClientTypeEnum enumByValue = SourceAndClientTypeEnum.getEnumByValue(sourceAndClientType);
            if (enumByValue != null) {
                queryVo.setSource(enumByValue.getSourceValue());
                queryVo.setClientType(enumByValue.getClientTypeValue());
            } else if (ThirdPlatformClientTypeEnum.getByCode(sourceAndClientType) != null) {
                queryVo.setSource(ThirdPlatformClientTypeEnum.getByCode(sourceAndClientType).getSource());
                queryVo.setClientType(ThirdPlatformClientTypeEnum.getByCode(sourceAndClientType).getClientType());
            }
        }
        // 废弃处方查询参数转化
        if (Objects.equals(queryVo.getSource(), 13)) {
            queryVo.setStatus(null);
            queryVo.setAuditType(99);
        }
    }

    /**
     * 构建提取文件名称
     *
     * @param fileName 文件名
     * @param pdfUrl   图片地址
     * @return
     */
    public static String buildFileName(String fileName, String pdfUrl) {
        // 基础名称处理（过滤非法字符）
        String baseName = cn.hutool.core.io.FileUtil.cleanInvalid(fileName);
        // 扩展名提取（处理含参数的URL）
        String pureUrl = StrUtil.subBefore(pdfUrl, '?', false);
        String ext = cn.hutool.core.io.FileUtil.extName(pureUrl);
        return StrUtil.isBlank(ext) ? baseName : baseName + "." + ext;
    }
}
