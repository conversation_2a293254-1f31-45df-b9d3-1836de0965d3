package com.xyy.saas.inquiry.common.handler.rational;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.spring.SpringUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xyy.saas.inquiry.common.api.rational.InquiryProductRuleSearchApi;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryCompatibilityDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryDiagnosiscDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryProductRuleDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalCategoryDetailDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalDictConfigDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalProductCommonDto;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageDrugRuleDto;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageRuleDrugQueryDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.DosageLimitQueryVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryProductDetailVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryProductRuleDtoSearchParam;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryProductRuleVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.LimitDrugCatalogOperationVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationPageReqVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.ProductDiagnosisRelationRespVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasCategoryVo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryDrugCatalogLimitPo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalCategoryDetail;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalDictConfig;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalProduct;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalProductRule;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.SaasDosageLimitPo;
import com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryDrugCatalogLimitMapper;
import com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryRationalDictConfigMapper;
import com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryRationalProductMapper;
import com.xyy.saas.inquiry.common.dal.mysql.rational.SaasDosageLimitMapper;
import com.xyy.saas.inquiry.common.enums.rational.DiagnosSexType;
import com.xyy.saas.inquiry.common.enums.rational.LimitDrugCatalogNameTypeEnum;
import com.xyy.saas.inquiry.common.enums.rational.LimitDrugType;
import com.xyy.saas.inquiry.common.enums.rational.RationalDictType;
import com.xyy.saas.inquiry.common.handler.rational.strategy.audit.LimitRuleStrategy;
import com.xyy.saas.inquiry.common.service.productmid.InquiryMidCategoryApiImpl;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalCategoryDetailService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalCompatibilityDetailService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalDiagnosisService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalDictConfigService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRepeatUseDrugLimitService;
import com.xyy.saas.inquiry.common.service.rational.ProductDiagnosisRelationService;
import com.xyy.saas.inquiry.common.service.rational.SaasUsageAndDosageReviewService;
import com.xyy.saas.inquiry.common.util.StringUtil;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.rational.CautionLevel;
import com.xyy.saas.inquiry.enums.rational.HealthEnums;
import com.xyy.saas.inquiry.enums.rational.RationalFieldType;
import com.xyy.saas.inquiry.enums.rational.RationalSexEnums;
import com.xyy.saas.inquiry.enums.rational.RationalStatusEnum;
import com.xyy.saas.inquiry.enums.rational.WomenEnums;
import com.xyy.saas.inquiry.hospital.api.diagnosis.InquiryDiagnosisApi;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.pojo.RationalTipsVO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.api.ProductStdlibApi;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * @Desc 合理用药service
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/11/06 10:52
 */
@Slf4j
@Component
public class RationalService implements CommandLineRunner {

    @Resource(name = "inquiryProductRuleSearchImpl")
    private InquiryProductRuleSearchApi productRuleSearchApi;

    @Resource
    private ProductDiagnosisRelationService productDiagnosisRelationService;

    @Resource
    private InquiryRationalDiagnosisService inquiryRationalDiagnosisService;

    @Resource
    private InquiryRationalProductMapper inquiryRationalProductMapper;

    @Resource
    private InquiryRationalCompatibilityDetailService inquiryRationalCompatibilityDetailService;

    @Resource
    private InquiryRationalCategoryDetailService inquiryRationalCategoryDetailService;

    @Resource
    private InquiryRationalDictConfigService inquiryRationalDictConfigService;

    @Resource
    private InquiryMidCategoryApiImpl midCategoryApi;

    @DubboReference
    private ProductStdlibApi productStdlibApi;

    @DubboReference
    private InquiryDiagnosisApi inquiryDiagnosisApi;

    @DubboReference
    private TenantPackageCostApi tenantPackageCostApi;

    @Resource
    private InquiryRepeatUseDrugLimitService inquiryRepeatUseDrugLimitService;

    @Resource
    private SaasDosageLimitMapper saasDosageLimitMapper;

    @Resource
    private InquiryDrugCatalogLimitMapper inquiryDrugCatalogLimitMapper;

    @Resource
    private InquiryRationalDictConfigMapper inquiryRationalDictConfigMapper;

    @DubboReference
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    @DubboReference
    private TenantApi tenantApi;

    @Resource
    private SaasUsageAndDosageReviewService saasUsageAndDosageReviewService;

    private Map<Integer, LimitRuleStrategy> limitRuleStrategyMap = new HashMap<>();


    // 拼接字符
    private static final String JOINT_CHAR = "______";

    @Override
    public void run(String... args) {
        for (LimitDrugType limitDrugType : LimitDrugType.values()) {
            if(limitDrugType.getLimitRuleStrategyClass() == null){
                continue;
            }
            limitRuleStrategyMap.put(limitDrugType.getLimitType(), SpringUtils.getBean(limitDrugType.getLimitRuleStrategyClass()));
        }
    }


    public void setDetailParam(InquiryProductDetailVo result , List<InquiryRationalProductRule> productRules){
        if(CollectionUtils.isEmpty(productRules)){
            return;
        }
        //设置详情参数
        productRules.forEach(ruleConfig -> {
            LimitRuleStrategy limitRuleStrategy = limitRuleStrategyMap.get(ruleConfig.getLimitDrugType());
            limitRuleStrategy.setParam(result, ruleConfig);
        });
        //校验默认输出
        checkDefault(result);
    }

    private void checkDefault(InquiryProductDetailVo result) {
        List<InquiryProductRuleVo> healthRules = result.getHealthLimit();
        if(CollectionUtils.isNotEmpty(healthRules) && healthRules.size() == 1){
            Integer targetType = StringUtils.equals(healthRules.get(0).getParam(), HealthEnums.HEALTH_HEPAR_FAIL.getCode().toString()) ? HealthEnums.HEALTH_KIDNEY_FAIL.getCode() : HealthEnums.HEALTH_HEPAR_FAIL.getCode();
            healthRules.add(InquiryProductRuleVo.builder().param(targetType.toString()).limitType(LimitDrugType.healthLimit.getLimitType()).caution(CautionLevel.unlimited.getType()).build());
        }
        List<InquiryProductRuleVo> womenRules = result.getWomenLimit();
        if(CollectionUtils.isNotEmpty(womenRules) && womenRules.size() == 1){
            Integer targetType = StringUtils.equals(womenRules.get(0).getParam(), WomenEnums.WOMEN_ENCYESIS.getCode().toString()) ? WomenEnums.WOMEN_LACTATION.getCode() : WomenEnums.WOMEN_ENCYESIS.getCode();
            womenRules.add(InquiryProductRuleVo.builder().param(targetType.toString()).limitType(LimitDrugType.womenLimit.getLimitType()).caution(CautionLevel.unlimited.getType()).build());
        }
    }


    /**
     * 审核药品
     * @param patientInfo 患者信息
     * @param drugInfos 药品信息
     * @return
     */

    public List<RationalTipsVO> auditingDrug(PatientInfo patientInfo, List<DrugInfo> drugInfos,List<String> diagnosisCodes,List<String> diagnosisNames,String organSign) {
        List<RationalTipsVO> result = new ArrayList<>();
        if (patientInfo== null || CollectionUtils.isEmpty(drugInfos)) {
            return null;
        }
        // 填充完整的药品信息
        this.buildDrugInfo(drugInfos);
        //商品基本规则配置
        List<InquiryProductRuleDto> ruleDtos = new ArrayList<>();
        //drugInfos -> commonName;
        List<String> commonNames = drugInfos.stream().map(DrugInfo::getCommonName).collect(Collectors.toList());
        //查询商品信息
        List<InquiryRationalProduct>  products = inquiryRationalProductMapper.queryByCondition(InquiryRationalProductCommonDto.builder().commonNames(commonNames).build());
        if(CollectionUtils.isNotEmpty(products)){
            //获取医保目录的备注提醒
            List<RationalTipsVO>  diagTips = getTipsByMedicareRemark(products);
            Optional.ofNullable(diagTips).ifPresent(list ->result.addAll(list));
        }
        //查询商品基础规则
        InquiryProductRuleDtoSearchParam productQueryParam = new InquiryProductRuleDtoSearchParam();
        //查询商品基本规则信息
        productQueryParam.setProductCommonNames(commonNames);
        productQueryParam.setStatus(RationalStatusEnum.ON.getCode());
        CommonResult<PageResult<InquiryProductRuleDto>> apiResult  = productRuleSearchApi.pageSearchInquiryProductRuleCommon(productQueryParam);
        if(ObjectUtil.isNotEmpty(apiResult) && ObjectUtil.isNotEmpty(apiResult.getData()) && !CollectionUtils.isEmpty(apiResult.getData().getList())){
            ruleDtos = apiResult.getData().getList();
        }
        //校验基础规则
        ruleDtos.forEach(ruleConfig -> {
            LimitRuleStrategy limitRuleStrategy = limitRuleStrategyMap.get(ruleConfig.getLimitDrugType());
            RationalTipsVO tipsVO = limitRuleStrategy.limitDrug(patientInfo, ruleConfig);
            if (ObjectUtil.isNotEmpty(tipsVO)) {
                result.add(tipsVO);
            }
        });

        // 商品六级分类限制 - 依赖中台 - 新增药品无法处理
        RationalTipsVO  drugCatalogTip = getTipsByDrugCatalog(patientInfo,drugInfos);
        Optional.ofNullable(drugCatalogTip).ifPresent(result::add);

        //诊断禁忌
        List<RationalTipsVO>  diagTips = getTipsByDiagList(patientInfo,drugInfos);
        result.addAll(diagTips);
        //配伍禁忌
        List<RationalTipsVO>  compTips = getTipsByCompList(drugInfos);
        Optional.of(compTips).ifPresent(p -> result.addAll(compTips));
        // 重复用药
        RationalTipsVO repeatUseDrugLimitTip = this.getRepeatUseDrugLimitTip(drugInfos);
        Optional.ofNullable(repeatUseDrugLimitTip).ifPresent(result::add);
        // 用量限制
        RationalTipsVO dosageLimitTip = this.getDosageLimitTip(drugInfos);
        Optional.ofNullable(dosageLimitTip).ifPresent(result::add);
        // 用法用量审查
        List<RationalTipsVO> usageAndDosageReviewTips = this.getUsageAndDosageReviewTips(patientInfo, drugInfos);
        Optional.of(usageAndDosageReviewTips).ifPresent(p -> result.addAll(usageAndDosageReviewTips));
        //适应症审查
        List<RationalTipsVO> indicationReviewTips = this.indicationReviewTips(patientInfo, drugInfos,diagnosisCodes,diagnosisNames,organSign);
        Optional.of(indicationReviewTips).ifPresent(p -> result.addAll(indicationReviewTips));

        List<RationalTipsVO> tipRes = result.stream().filter(d -> !Objects.equals(d.getCaution(),CautionLevel.unlimited.getType())).collect(Collectors.toList());
        //根据警示级别排序，禁用排序最高
        Map<String,List<RationalTipsVO>> tipMap = tipRes.stream().filter(obj ->StringUtils.isNotBlank(obj.getCommonName())).collect(Collectors.groupingBy(RationalTipsVO::getCommonName));
        //排序集合
        Map<String,Integer> groupMaxMap = new HashMap<>();
        tipMap.forEach((k,v)->{
           v.stream().map(RationalTipsVO::getCaution).mapToInt(Integer::valueOf).max().ifPresent(p->groupMaxMap.put(k,p));
        });
        // 将map转换为List
        List<Map.Entry<String, Integer>> list = new LinkedList<>(groupMaxMap.entrySet());
        // 使用Comparator根据value排序
        list.sort(Map.Entry.comparingByValue(Comparator.reverseOrder()));
        List<RationalTipsVO> resp = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : list) {
            List<RationalTipsVO> rationalTipsVOS = tipMap.get(entry.getKey());
            List<RationalTipsVO> res = rationalTipsVOS.stream().sorted(Comparator.comparing(RationalTipsVO::getCaution).reversed()).collect(Collectors.toList());
            resp.add(RationalTipsVO.builder().commonName(entry.getKey()).caution(entry.getValue()).list(res).build());
        }
        return resp;
    }


    /**
     * 根据问诊信息查询接诊医院
     * @param inquiryWayType
     * @param prescriptionType
     * @return
     */
    private String getHospitalPrefByInquiryInfo(Integer inquiryWayType , Integer prescriptionType){
        if(ObjectUtil.isEmpty(inquiryWayType)){
            return null;
        }
        TenantPackageCostDto dto = tenantPackageCostApi.getPreTenantCost(InquiryWayTypeEnum.fromCode(inquiryWayType), InquiryBizTypeEnum.DRUGSTORE_INQUIRY, prescriptionType);
        return dto.getHospitalPrefs().getFirst();
    }


    private RationalTipsVO getTipsByDrugCatalog(PatientInfo patientInfo, List<DrugInfo> drugInfos) {

        if (CollectionUtils.isEmpty(drugInfos)) {
            return null;
        }

        // 获取本次问诊就诊医院
        String hospitalPref = getHospitalPrefByInquiryInfo(patientInfo.getInquiryWayType(), patientInfo.getPrescriptionType());

        try {
            TenantDto tenantDto = tenantApi.getTenant();
            // 根据门店的省市区和互联网医院查询对应的药品限制分类
            LimitDrugCatalogOperationVo limitDrugCatalogOperationDto = LimitDrugCatalogOperationVo.builder()
                .provinceCode(tenantDto.getProvinceCode())
                .cityCode(tenantDto.getCityCode())
                .areaCode(tenantDto.getAreaCode())
                .hospitalPref(hospitalPref).build();
            List<InquiryDrugCatalogLimitPo> inquiryDrugCatalogLimitPoList = inquiryDrugCatalogLimitMapper.queryList(limitDrugCatalogOperationDto);

            if (CollectionUtils.isEmpty(inquiryDrugCatalogLimitPoList)) {
                return null;
            }

            // 黑名单分类集合
            List<Integer> limitCatalogIds = inquiryDrugCatalogLimitPoList.stream()
                .filter(item ->
                    LimitDrugCatalogNameTypeEnum.LIMIT_CATALOG.getCode().equals(item.getLimitType())
                        && item.getCatalogId() != null)
                .map(InquiryDrugCatalogLimitPo::getCatalogId)
                .collect(Collectors.toList());

            // 白名单分类集合
            List<Integer> limitWhiteCatalogIds = inquiryDrugCatalogLimitPoList.stream()
                .filter(item ->
                    LimitDrugCatalogNameTypeEnum.LIMIT_WHITE_CATALOG.getCode().equals(item.getLimitType())
                        && item.getCatalogId() != null)
                .map(InquiryDrugCatalogLimitPo::getCatalogId)
                .collect(Collectors.toList());

            // 先剔除白名单,所有的分类id都不在白名单
            List<DrugInfo> drugInfoList = drugInfos.stream()
                .filter(d -> Stream.of(d.getFirstCategoryId(), d.getSecondCategoryId(), d.getThirdCategoryId(), d.getFourthCategoryId(), d.getFiveCategoryId(), d.getSixCategoryId()).filter(Objects::nonNull)
                    .noneMatch(limitWhiteCatalogIds::contains)).collect(Collectors.toList());

            String drugNames = drugInfoList.stream()
                .filter(d -> Stream.of(d.getFirstCategoryId(), d.getSecondCategoryId(), d.getThirdCategoryId(), d.getFourthCategoryId(), d.getFiveCategoryId(), d.getSixCategoryId()).filter(Objects::nonNull)
                    .anyMatch(limitCatalogIds::contains)).map(DrugInfo::getCommonName).distinct().collect(Collectors.joining(","));

            if (StringUtils.isBlank(drugNames)) {
                return null;
            }

            Integer caution = CautionLevel.forbidden.getType();
            String cautionStr = CautionLevel.forbidden.getTipsName();
            String desc = "根据互联网医院管理细则,此类药品 【" + drugNames + "】 禁止开方!";

            // 限制药品分类
            InquiryRationalDictConfigDto inquiryRationalDictConfigDto = InquiryRationalDictConfigDto.builder()
                .type(RationalDictType.LIMIT_DRUG_CATALOG.getType())
                .names(Lists.newArrayList(LimitDrugCatalogNameTypeEnum.LIMIT_CATALOG_CONFIG.getName()))
                .yn(CommonStatusEnum.ENABLE.getStatus()).build();
            List<InquiryRationalDictConfig> inquiryRationalDictConfigList = inquiryRationalDictConfigMapper.queryByCondition(inquiryRationalDictConfigDto);
            if (CollectionUtils.isNotEmpty(inquiryRationalDictConfigList)) {

                InquiryRationalDictConfig inquiryRationalDictConfig = inquiryRationalDictConfigList.get(0);

                if (StringUtils.isNotBlank(inquiryRationalDictConfig.getValue()) && NumberUtils.toInt(inquiryRationalDictConfig.getValue(), -1) != -1) {
                    caution = Integer.valueOf(inquiryRationalDictConfig.getValue());
                    cautionStr = CautionLevel.getTipsNameByType(Integer.valueOf(inquiryRationalDictConfig.getValue()));
                }
                if (StringUtils.isNotBlank(inquiryRationalDictConfig.getDescription())) {
                    desc = StringUtils.replaceEach(inquiryRationalDictConfig.getDescription(), new String[]{"{限制级别}", "{药品名称}"}, new String[]{cautionStr, drugNames});
                }
            }

            return RationalTipsVO.builder()
                .commonName(LimitDrugType.drugCatalogLimit.getDesc())
                .caution(caution)
                .cautionStr(cautionStr)
                .limitType(LimitDrugType.drugCatalogLimit.getLimitType())
                .limitTypeStr(LimitDrugType.drugCatalogLimit.getDesc())
                .desc(desc)
                .build();

        }catch (Exception e){
            log.error("合理用药药品分类审查异常,入参:{}",JSON.toJSONString(drugInfos),e);
        }

        return null;
    }

    private List<RationalTipsVO> indicationReviewTips(PatientInfo patientInfo, List<DrugInfo> drugInfos, List<String> diagnosisCodes,List<String> diagnosisNames,String organSign) {

        if(CollectionUtils.isEmpty(drugInfos) || CollectionUtils.isEmpty(diagnosisCodes)){
            return Lists.newArrayList();
        }
        TenantDto tenantDto = tenantApi.getTenant();
        // 区域门店配置是否走超适应症审查
        InquiryOptionConfigRespDto inquiryOptionConfig = inquiryOptionConfigApi.getInquiryOptionConfig(tenantDto, InquiryOptionTypeEnum.FORM_OFF_LABEL_REVIEW);
        if(inquiryOptionConfig == null || ObjectUtil.notEqual(inquiryOptionConfig.getFormOffLabelReview(), Boolean.TRUE)){
            // 不走直接返回
            return Lists.newArrayList();
        }
        List<RationalTipsVO> result = Lists.newArrayList();

        ProductDiagnosisRelationPageReqVO pageReqVO = new ProductDiagnosisRelationPageReqVO();
        pageReqVO.setProductNames(drugInfos.stream().map(DrugInfo::getCommonName).toList());
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(1000);
        PageResult<ProductDiagnosisRelationRespVO> productDiagnosisRelationPage = productDiagnosisRelationService.getProductDiagnosisRelationPage(pageReqVO);
        if(ObjectUtil.isEmpty(productDiagnosisRelationPage) || productDiagnosisRelationPage.getList().isEmpty()){
            return new ArrayList<>();
        }
        List<ProductDiagnosisRelationRespVO> recDiags = productDiagnosisRelationPage.getList();
        //诊断总列表
        List<String> diagnosisAll= recDiags.stream().map(ProductDiagnosisRelationRespVO::getDiagnosisCode).collect(Collectors.toList());

        StringBuilder desc = new StringBuilder();

        if(CollectionUtils.isEmpty(diagnosisAll)){
            return Lists.newArrayList();
        }

        //判断是否超过药品适应症
        try {
            for (int i = 0; i < diagnosisCodes.size(); i++) {
                if (!diagnosisAll.contains(diagnosisCodes.get(i))) {
                    desc.append("【").append(diagnosisNames.get(i)).append("】超出药品适应症\n");
                }
            }
        } catch (Exception e) {
            log.error("indicationReviewTips#error3:{}",e);
            return Lists.newArrayList();
        }
        Map<String, List<ProductDiagnosisRelationRespVO>> recommendResultMap = recDiags.stream().collect(Collectors.groupingBy(ProductDiagnosisRelationRespVO::getProductName));
        //判断是否存在超适应症用药   即每个药品都有对应适应症
        for (Map.Entry<String, List<ProductDiagnosisRelationRespVO>> entry : recommendResultMap.entrySet()) {
            if(CollectionUtils.isEmpty(entry.getValue())){
                continue;
            }
            List<String> diagnosisTemp = entry.getValue().stream().map(ProductDiagnosisRelationRespVO::getDiagnosisCode).toList();
            boolean flag = true;
            for (String diagnosisCode : diagnosisCodes) {
                if (diagnosisTemp.contains(diagnosisCode)){
                    flag = false;
                    break;
                }
            }
            if(flag){
                desc.append("【").append(entry.getKey()).append("】存在超适应症用药\n");
            }
        }


      if(StringUtils.isNotBlank(desc.toString())){
          result.add(RationalTipsVO.builder().commonName("超适应症").caution(CautionLevel.forbidden.getType())
                  .cautionStr(CautionLevel.forbidden.getTipsName())
                  .limitType(LimitDrugType.indicationLimit.getLimitType())
                  .limitTypeStr(LimitDrugType.indicationLimit.getDesc())
                  .desc(desc.toString())
                  .build());
      }
      return result;
    }

    /**
     * 获取用法用量审查tips
     *
     * @param patientInfo
     * @param drugInfos
     * @return java.util.List<com.xyy.saas.remote.web.handler.rational.RationalTipsVO>
     * <AUTHOR> 9/18/24 3:00 PM
     */
    private List<RationalTipsVO> getUsageAndDosageReviewTips(PatientInfo patientInfo, List<DrugInfo> drugInfos) {

        log.info("InquiryHandler#getUsageAndDosageReviewTips -> 获取用法用量审查tips，入参：patientInfo {}, drugInfos {}", JSON.toJSONString(patientInfo), JSON.toJSONString(drugInfos));

        if (patientInfo == null || CollectionUtils.isEmpty(drugInfos)
            || ObjectUtil.notEqual(MedicineTypeEnum.ASIAN_MEDICINE.getCode(), patientInfo.getMedicineType().intValue())) {
            return Lists.newArrayList();
        }


        List<UsageAndDosageRuleDrugQueryDto.UsageAndDosageRuleDrugDetailQueryDto> usageAndDosageRuleDrugDetailQueryDtoList = drugInfos.stream()
                .filter(item -> StringUtils.isNotBlank(item.getCommonName()))
                .map(item ->{
                            UsageAndDosageRuleDrugQueryDto.UsageAndDosageRuleDrugDetailQueryDto usageAndDosageRuleDrugDetailQueryDto = new UsageAndDosageRuleDrugQueryDto.UsageAndDosageRuleDrugDetailQueryDto();
                            usageAndDosageRuleDrugDetailQueryDto.setCommonName(item.getCommonName()) ;
                            usageAndDosageRuleDrugDetailQueryDto.setSpecification(item.getSpecification());
                            return usageAndDosageRuleDrugDetailQueryDto;
                        }).collect(Collectors.toList());

        UsageAndDosageRuleDrugQueryDto usageAndDosageRuleDrugQueryDto = UsageAndDosageRuleDrugQueryDto.builder()
                .age(StringUtil.convertStr(patientInfo.getAge()))
                .usageAndDosageRuleDrugDetailQueryDtoList(usageAndDosageRuleDrugDetailQueryDtoList).build();

        // 查询药品通用名和规则对应的用法用量审查规则
        UsageAndDosageDrugRuleDto usageAndDosageDrugRuleDto = saasUsageAndDosageReviewService.queryRuleByDrug(usageAndDosageRuleDrugQueryDto);

        if (usageAndDosageDrugRuleDto == null || CollectionUtils.isEmpty(usageAndDosageDrugRuleDto.getUsageAndDosageDrugRuleDetailDtoList())) {
            return Lists.newArrayList();
        }

        Map<String, UsageAndDosageDrugRuleDto.UsageAndDosageDrugRuleDetailDto> usageAndDosageDrugRuleDetailDtoMap = usageAndDosageDrugRuleDto.getUsageAndDosageDrugRuleDetailDtoList().stream().filter(item -> StringUtils.isNotBlank(item.getCommonName()))
                .collect(Collectors.toMap(item -> item.getCommonName() + JOINT_CHAR + StringUtils.defaultString(item.getSpecification(), ""), Function.identity(), (v1, v2) -> v2));

        if (MapUtils.isEmpty(usageAndDosageDrugRuleDetailDtoMap)) {
            return Lists.newArrayList();
        }

        List<RationalTipsVO> rationalTipsVOList = new ArrayList<>();

        for (DrugInfo item : drugInfos) {

            if (StringUtils.isBlank(item.getCommonName())) {
                continue;
            }

            String commonNameAndSpecification = item.getCommonName() + JOINT_CHAR + StringUtils.defaultString(item.getSpecification(), "");

            if (!usageAndDosageDrugRuleDetailDtoMap.containsKey(commonNameAndSpecification)) {
                continue;
            }

            UsageAndDosageDrugRuleDto.UsageAndDosageDrugRuleDetailDto usageAndDosageDrugRuleDetailDto = usageAndDosageDrugRuleDetailDtoMap.get(commonNameAndSpecification);

            if (!usageAndDosageDrugRuleDetailDto.getDirections().equals(item.getDirections())) {

                rationalTipsVOList.add(RationalTipsVO.builder().commonName(item.getCommonName())
                        .caution(CautionLevel.forbidden.getType())
                        .cautionStr(CautionLevel.forbidden.getTipsName())
                        .limitType(LimitDrugType.directionsLimit.getLimitType())
                        .limitTypeStr(LimitDrugType.directionsLimit.getDesc())
                        .desc("推荐用药方法【" + usageAndDosageDrugRuleDetailDto.getDirections() + "】")
                        .build());
            }

            if (!"适量".equals(usageAndDosageDrugRuleDetailDto.getSingleUnit())) {

                BigDecimal singleDose = StringUtil.allConvertBigDecimalStr(item.getSingleDose());

                if (singleDose == null || usageAndDosageDrugRuleDetailDto.getMaxSingleDose().compareTo(singleDose) < 0
                        || usageAndDosageDrugRuleDetailDto.getMinSingleDose().compareTo(singleDose) > 0) {

                    rationalTipsVOList.add(RationalTipsVO.builder().commonName(item.getCommonName())
                            .caution(CautionLevel.forbidden.getType())
                            .cautionStr(CautionLevel.forbidden.getTipsName())
                            .limitType(LimitDrugType.singleDoseLimit.getLimitType())
                            .limitTypeStr(LimitDrugType.singleDoseLimit.getDesc())
                            .desc("推荐单次用量【" + usageAndDosageDrugRuleDetailDto.getMinSingleDose().stripTrailingZeros().toPlainString() + "-" + usageAndDosageDrugRuleDetailDto.getMaxSingleDose().stripTrailingZeros().toPlainString() + usageAndDosageDrugRuleDetailDto.getSingleUnit() + "】")
                            .build());
                }
            }

            if (!usageAndDosageDrugRuleDetailDto.getSingleUnit().equals(item.getSingleUnit())) {

                rationalTipsVOList.add(RationalTipsVO.builder().commonName(item.getCommonName())
                        .caution(CautionLevel.forbidden.getType())
                        .cautionStr(CautionLevel.forbidden.getTipsName())
                        .limitType(LimitDrugType.singleUnitLimit.getLimitType())
                        .limitTypeStr(LimitDrugType.singleUnitLimit.getDesc())
                        .desc("推荐剂量单位【" + usageAndDosageDrugRuleDetailDto.getSingleUnit() + "】")
                        .build());
            }

            if (!usageAndDosageDrugRuleDetailDto.getUseFrequencyListList().contains(item.getUseFrequency())) {

                rationalTipsVOList.add(RationalTipsVO.builder().commonName(item.getCommonName())
                        .caution(CautionLevel.forbidden.getType())
                        .cautionStr(CautionLevel.forbidden.getTipsName())
                        .limitType(LimitDrugType.useFrequencyLimit.getLimitType())
                        .limitTypeStr(LimitDrugType.useFrequencyLimit.getDesc())
                        .desc("推荐用药频率" + String.join("、", usageAndDosageDrugRuleDetailDto.getUseFrequencyListList()) + "")
                        .build());
            }

        }

        return rationalTipsVOList;
    }

    /**
     * 填充完整的药品信息
     *
     * @param drugInfoList
     * @return void
     * <AUTHOR> 4/30/24 5:36 PM
     */
    private void buildDrugInfo(List<DrugInfo> drugInfoList) {

        if (CollectionUtils.isEmpty(drugInfoList)) {
            return;
        }

        List<String> prefList = drugInfoList.stream().filter(item -> item != null && StringUtils.isNotBlank(item.getPref())).map(DrugInfo::getPref).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(prefList)) {
            return;
        }

        try {
            StdlibProductSearchDto searchDto = new StdlibProductSearchDto();
            searchDto.setMidStdlibIdList(prefList.stream().map(Long::valueOf).distinct().toList());
            List<ProductStdlibDto> queryProductRst = productStdlibApi.searchProductStdlibList(searchDto,2000);

            Map<Long, ProductStdlibDto> productStandardVoMap = queryProductRst.stream().collect(Collectors.toMap(ProductStdlibDto::getMidStdlibId, Function.identity(), (v1, v2) -> v2));

            for (DrugInfo drugInfo : drugInfoList) {
                //标准库id
                Long pref = Long.valueOf(drugInfo.getPref());
                if (productStandardVoMap.containsKey(pref)) {
                    ProductStdlibDto prod = productStandardVoMap.get(pref);
                    drugInfo.setSpecification(prod.getSpec());
                    drugInfo.setPackageUnit(prod.getUnit());
                    drugInfo.setFirstCategoryId(prod.getFirstCategory() != null ? Integer.valueOf(prod.getFirstCategory()) : null);
                    drugInfo.setSecondCategoryId(prod.getSecondCategory() != null ? Integer.valueOf(prod.getSecondCategory()) : null);
                    drugInfo.setThirdCategoryId(prod.getThirdCategory() != null ? Integer.valueOf(prod.getThirdCategory()) : null);
                    drugInfo.setFourthCategoryId(prod.getFourthCategory() != null ? Integer.valueOf(prod.getFourthCategory()) : null);
                    drugInfo.setFiveCategoryId(prod.getFiveCategory() != null ? Integer.valueOf(prod.getFiveCategory()) : null);
                    drugInfo.setSixCategoryId(prod.getSixCategory() != null ? Integer.valueOf(prod.getSixCategory()) : null);
                }
            }
        } catch (Exception e) {
            log.error("ERROR::InquiryHandler#checkNormalMedicines -> 查询预购药品用法用量信息异常 ",e);
        }

    }

    /**
     * 用量限制
     *
     * @param drugInfoList
     * @return com.xyy.saas.remote.web.handler.rational.RationalTipsVO
     * <AUTHOR> 4/30/24 4:22 PM
     */
    private RationalTipsVO getDosageLimitTip(List<DrugInfo> drugInfoList) {

        if (CollectionUtils.isEmpty(drugInfoList)) {
            return null;
        }

        StringBuilder stringBuilder = new StringBuilder();

        try {
            List<String> prefList = drugInfoList.stream().filter(item -> item != null && StringUtils.isNotBlank(item.getPref())).map(DrugInfo::getPref).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(prefList)) {
                return null;
            }

            List<SaasDosageLimitPo> saasDosageLimitPoList = saasDosageLimitMapper.queryByCondition(DosageLimitQueryVo.builder().standardIdList(prefList).build());

            if (CollectionUtils.isEmpty(saasDosageLimitPoList)) {
                return null;
            }

            Map<String, Integer> totalDoseLimitMap = saasDosageLimitPoList.stream().filter(item -> item != null && item.getTotalDoseLimit() != null)
                    .collect(Collectors.toMap(SaasDosageLimitPo::getStandardId, SaasDosageLimitPo::getTotalDoseLimit, (v1, v2) -> v2));

            for (DrugInfo drugInfo : drugInfoList) {

                if (drugInfo.getQuantity() != null && totalDoseLimitMap.containsKey(drugInfo.getPref()) && drugInfo.getQuantity().compareTo(BigDecimal.valueOf(totalDoseLimitMap.get(drugInfo.getPref()))) > 0 ) {
                    stringBuilder.append("药品超量，【").append(drugInfo.getCommonName()).append("】最大开方数量限制为").append(totalDoseLimitMap.get(drugInfo.getPref())).append(drugInfo.getPackageUnit()).append("\n");
                }
            }
        } catch (Exception e) {
            log.error("获取重复用药提示异常,入参:{}", JSON.toJSONString(drugInfoList), e);
        }

        String desc = stringBuilder.toString();

        if (desc.length() == 0) {
            return null;
        }

        desc = desc.substring(0, desc.length() - 1);

        return RationalTipsVO.builder().commonName("超量审查").caution(CautionLevel.remind.getType())
                .cautionStr(CautionLevel.remind.getTipsName())
                .limitType(LimitDrugType.dosageLimit.getLimitType())
                .limitTypeStr(LimitDrugType.dosageLimit.getDesc())
                .desc(desc)
                .build();
    }

    /**
     * 获取重复用药提示
     *
     * @param drugInfoList
     * @return java.util.List<com.xyy.saas.remote.web.handler.rational.RationalTipsVO>
     * <AUTHOR> 4/29/24 3:42 PM
     */
    private RationalTipsVO getRepeatUseDrugLimitTip(List<DrugInfo> drugInfoList) {

        if (CollectionUtils.isEmpty(drugInfoList)) {
            return null;
        }

        // 全局开关关闭  则不走重复用药审查
        InquiryOptionGlobalConfigRespDto globalConfig = inquiryOptionConfigApi.getInquiryOptionGlobalConfig(new InquiryOptionConfigQueryDto());
        if (globalConfig != null && ObjectUtil.equals(globalConfig.getProcRepeatDrugSwitch(),CommonStatusEnum.DISABLE.getStatus())) {
            return null;
        }

        StringBuilder stringBuilder = new StringBuilder();

        try {
            // 获取分类规则下的重复药品
            Set<List<String>> repeatDrugCommonNameSetByClassifyRule = this.getRepeatDrugCommonNameByClassifyRule(drugInfoList);
            log.info("getRepeatUseDrugLimitTip#getRepeatDrugCommonNameByClassifyRule:{}", JSON.toJSONString(repeatDrugCommonNameSetByClassifyRule));

            // 获取全局配置下的通用名重复的药品
            Set<List<String>> repeatDrugCommonNameSetByGlobalConfig = this.getRepeatDrugCommonNameByGlobalConfig(drugInfoList);
            log.info("getRepeatUseDrugLimitTip#getRepeatDrugCommonNameByGlobalConfig:{}", JSON.toJSONString(repeatDrugCommonNameSetByGlobalConfig));

            repeatDrugCommonNameSetByClassifyRule.addAll(repeatDrugCommonNameSetByGlobalConfig);

            if (CollectionUtils.isEmpty(repeatDrugCommonNameSetByClassifyRule)) {
                return null;
            }

            // 去除重复
            List<List<String>> allRepeatDrugCommonNameList = this.removeDuplication(repeatDrugCommonNameSetByClassifyRule);

            if (CollectionUtils.isEmpty(allRepeatDrugCommonNameList)) {
                return null;
            }

            for (List<String> repeatDrugCommonNameList : allRepeatDrugCommonNameList) {

                for (String repeatDrugCommonName : repeatDrugCommonNameList) {
                    stringBuilder.append("【").append(repeatDrugCommonName).append("】与");
                }

                stringBuilder.deleteCharAt(stringBuilder.length() - 1);
                stringBuilder.append("存在重复用药\n");
            }
        } catch (Exception e){
            log.error("获取重复用药提示异常,入参:{}", JSON.toJSONString(drugInfoList), e);
        }

        String desc = stringBuilder.toString();

        if (desc.length() == 0) {
            return null;
        }

        desc = desc.substring(0, desc.length() - 1);

        return RationalTipsVO.builder().commonName("重复用药审查").caution(CautionLevel.remind.getType())
                .cautionStr(CautionLevel.remind.getTipsName())
                .limitType(LimitDrugType.repeatUseDrug.getLimitType())
                .limitTypeStr(LimitDrugType.repeatUseDrug.getDesc())
                .desc(desc)
                .build();
    }

    /**
     * 去除重复
     *
     * @param allRepeatDrugCommonNameSet
     * @return void
     * <AUTHOR> 4/30/24 2:45 PM
     */
    private List<List<String>> removeDuplication(Set<List<String>> allRepeatDrugCommonNameSet) {

        if (CollectionUtils.isEmpty(allRepeatDrugCommonNameSet)) {
            return Lists.newArrayList();
        }

        List<List<String>> sortRepeatDrugPrefList = allRepeatDrugCommonNameSet.stream()
                .sorted((o1, o2) -> Integer.compare(o2.size(), o1.size()))
                .collect(Collectors.toList());

        Set<String> beIncludedDrugSet = new HashSet<>();

        for (int x = 0; x < sortRepeatDrugPrefList.size(); x++) {

            for (int y = x + 1; y < sortRepeatDrugPrefList.size(); y++) {

                String xString = String.join(",", sortRepeatDrugPrefList.get(x));
                String yString = String.join(",", sortRepeatDrugPrefList.get(y));

                if (xString.length() != yString.length() && xString.contains(yString)) {
                    beIncludedDrugSet.add(yString);
                }
            }
        }

        sortRepeatDrugPrefList.removeIf(next -> beIncludedDrugSet.contains(String.join(",", next)));

        return sortRepeatDrugPrefList;
    }

    /**
     * 获取全局配置下的通用名重复的药品
     *
     * @param drugInfoList
     * @return java.util.Set<java.util.List<java.lang.String>>
     * <AUTHOR> 4/30/24 2:10 PM
     */
    private Set<List<String>> getRepeatDrugCommonNameByGlobalConfig(List<DrugInfo> drugInfoList) {

        if (CollectionUtils.isEmpty(drugInfoList)) {
            return new HashSet<>();
        }
        InquiryOptionGlobalConfigRespDto globalConfig = inquiryOptionConfigApi.getInquiryOptionGlobalConfig(new InquiryOptionConfigQueryDto());
        if (globalConfig == null || ObjectUtil.notEqual(globalConfig.getProcRepeatDrugSwitch(),Boolean.TRUE)) {
            return new HashSet<>();
        }

        Map<String, List<String>> commonNameMap = drugInfoList.stream().collect(Collectors.groupingBy(DrugInfo::getCommonName,
                Collectors.mapping(DrugInfo::getCommonName, Collectors.toList())));

        Set<List<String>> repeatDrugCommonNameSet = new HashSet<>();

        commonNameMap.forEach((k, v) ->{
            if (v.size() > 1) {
                Collections.sort(v);
                repeatDrugCommonNameSet.add(v);
            }
        });

        return repeatDrugCommonNameSet;
    }

    /**
     * 获取分类规则下的重复药品
     *
     * @param drugInfoList
     * @return void
     * <AUTHOR> 4/30/24 1:34 PM
     */
    private Set<List<String>> getRepeatDrugCommonNameByClassifyRule(List<DrugInfo> drugInfoList) {

        // 获取药品四级五级六级分类(在运营端新增重复用药分类的时候只能从第四级分类开始选，所以这里只用取药品的四级五级和六级分类)
        Map<String, List<Integer>> fourthAndFiveAndSixCategoryMap = this.getFourthAndFiveAndSixCategoryMap(drugInfoList);

        if (MapUtils.isEmpty(fourthAndFiveAndSixCategoryMap)) {
            return Sets.newHashSet();
        }

        List<Integer> fourthAndFiveAndSixCategoryIdList = fourthAndFiveAndSixCategoryMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());

        // 获取全部的重复用药分类规则
        List<List<Integer>> allRule = inquiryRepeatUseDrugLimitService.getAllRule(fourthAndFiveAndSixCategoryIdList);

        Map<String, String> commonNameMap = drugInfoList.stream().collect(Collectors.toMap(DrugInfo::getPref, DrugInfo::getCommonName, (v1, v2) -> v2));

        if (MapUtils.isEmpty(fourthAndFiveAndSixCategoryMap) || CollectionUtils.isEmpty(allRule)) {
            return Sets.newHashSet();
        }

        Set<List<String>> allRepeatDrugCommonNameSet = new HashSet<>();

        for (List<Integer> rule : allRule) {

            List<String> repeatDrugPrefList = new ArrayList<>();

            for (Integer classifyId : rule) {

                fourthAndFiveAndSixCategoryMap.forEach((k, v) -> {
                    if (v.contains(classifyId)) {
                        repeatDrugPrefList.add(k);
                    }
                });
            }

            if (repeatDrugPrefList.size() > 1) {

                List<String> repeatDrugCommonNameList = new ArrayList<>();

                for (String repeatDrugPref : repeatDrugPrefList) {

                    if (!commonNameMap.containsKey(repeatDrugPref)) {
                        continue;
                    }

                    repeatDrugCommonNameList.add(commonNameMap.get(repeatDrugPref));
                }

                Collections.sort(repeatDrugCommonNameList);
                allRepeatDrugCommonNameSet.add(repeatDrugCommonNameList);
            }
        }

        return allRepeatDrugCommonNameSet;
    }

    /**
     * 获取药品四级五级六级分类
     *
     * @param drugInfoList
     * @return java.util.List<java.lang.Integer>
     * <AUTHOR> 4/29/24 3:55 PM
     */
    private Map<String, List<Integer>> getFourthAndFiveAndSixCategoryMap(List<DrugInfo> drugInfoList) {

        if (CollectionUtils.isEmpty(drugInfoList)) {
            return Maps.newHashMap();
        }


        // 从商品侧查询药品用法用量信息,并把四级分类,五级分类,六级分类id取出来
        Map<String, List<Integer>> fourthAndFiveAndSixCategoryMap = new HashMap<>();

        for (DrugInfo drugInfo : drugInfoList) {

            List<Integer> fourthAndFiveAndSixCategoryList = new ArrayList<>();

            if (drugInfo.getFourthCategoryId() != null && drugInfo.getFourthCategoryId() > 0) {
                fourthAndFiveAndSixCategoryList.add(drugInfo.getFourthCategoryId());
            }

            if (drugInfo.getFiveCategoryId() != null && drugInfo.getFiveCategoryId() > 0) {
                fourthAndFiveAndSixCategoryList.add(drugInfo.getFiveCategoryId());
            }

            if (drugInfo.getSixCategoryId() != null && drugInfo.getSixCategoryId() > 0) {
                fourthAndFiveAndSixCategoryList.add(drugInfo.getSixCategoryId());
            }

            if (CollectionUtils.isNotEmpty(fourthAndFiveAndSixCategoryList)) {
                fourthAndFiveAndSixCategoryMap.put(drugInfo.getPref(), fourthAndFiveAndSixCategoryList);
            }

        }

        return fourthAndFiveAndSixCategoryMap;
    }

    private List<RationalTipsVO> getTipsByMedicareRemark(List<InquiryRationalProduct> products) {
        List<RationalTipsVO> result = new ArrayList<>();
        for(InquiryRationalProduct rationalProduct : products){
            if(StringUtils.isBlank(rationalProduct.getMedicareRemark())){
                continue;
            }
            RationalTipsVO res = RationalTipsVO.builder()
                    .commonName(rationalProduct.getCommonName())
                    .caution(CautionLevel.remind.getType())
                    .cautionStr(CautionLevel.remind.getTipsName())
                    .limitType(LimitDrugType.medicareRemind.getLimitType())
                    .limitTypeStr(LimitDrugType.medicareRemind.getDesc())
                    .desc(rationalProduct.getMedicareRemark())
                    .build();
            result.add(res);
        }
        return result;
    }

    private List<RationalTipsVO> getTipsByCompList(List<DrugInfo> drugInfos) {
        List<RationalTipsVO> result = new ArrayList<>();
        //获取通用名集合
        List<String> commonNames = drugInfos.stream().map(DrugInfo::getCommonName).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(commonNames) && commonNames.size() < 2){
            return new ArrayList<>();
        }
        //获取当前通用名集合涉及的配伍禁忌
        List<InquiryCompatibilityDto> compatibilityDtos = inquiryRationalCompatibilityDetailService.queryCompatibilityDetail(commonNames);
        if(CollectionUtils.isEmpty(compatibilityDtos)){
            return new ArrayList<>();
        }
        InquiryRationalProductCommonDto query = InquiryRationalProductCommonDto.builder().commonNames(commonNames).build();
        List<InquiryRationalProduct> products = inquiryRationalProductMapper.queryByCondition(query);
        //拿到所有商品的六级分类的id集合
        List<Integer> lv6Ids = products.stream().filter(product1 -> StringUtils.isNotBlank(product1.getCategoryLv6Id())).map(InquiryRationalProduct::getCategoryLv6Id).map(Integer::parseInt).collect(Collectors.toList());
        Map<Integer, List<SaasCategoryVo>>  childMap =  midCategoryApi.queryCategoryParentPathByIds(lv6Ids);
        Set<Integer> lv6Set = new HashSet<>();
        childMap.forEach((key, value) -> {
            lv6Set.add(key);
            value.forEach(vo -> lv6Set.add(vo.getId()));
        });
        List<InquiryRationalCategoryDetail>  categoryDetails = inquiryRationalCategoryDetailService.queryByCondition(InquiryRationalCategoryDetailDto.builder().classifyIds(new ArrayList<>(lv6Set)).build());
        //拿到所有分类id
        List<Integer> dictIds = categoryDetails.stream().map(InquiryRationalCategoryDetail::getCategoryId).distinct().collect(Collectors.toList());
        //查询所有分类信息
        List<InquiryRationalDictConfig> dictConfigs = inquiryRationalDictConfigService.queryByCondition(InquiryRationalDictConfigDto.builder().ids(dictIds).build());
        //<分类id,分类名称>
        Map<Integer,String> cateMap = dictConfigs.stream().collect(Collectors.toMap(InquiryRationalDictConfig::getId,InquiryRationalDictConfig::getName,(k1, k2) ->k2));
        //<通用名，set<类别id>>
        Map<String,Set<Integer>> nameCateMapping = new HashMap<>();
        //填充通用名类别映射map
        products.stream().filter(p-> StringUtils.isNotBlank(p.getCategoryLv6Id())).forEach(product -> {
            //获取当前商品对应的六级分类
            List<SaasCategoryVo> saasCategoryVos =  childMap.getOrDefault(StringUtil.convertStr(product.getCategoryLv6Id()),new ArrayList<>());
            //获取当前商品所有的六级分类
            List<Integer> prodLv6Ids = saasCategoryVos.stream().map(SaasCategoryVo::getId).distinct().collect(Collectors.toList());
            prodLv6Ids.add(Integer.valueOf(product.getCategoryLv6Id()));
            //根据六级分类id匹配对应的类别
            Set<Integer> cateSet = categoryDetails.stream().filter(detail-> prodLv6Ids.contains(detail.getClassifyId())).map(InquiryRationalCategoryDetail::getCategoryId).distinct().collect(Collectors.toSet());
            nameCateMapping.put(product.getCommonName(),cateSet);
        });
        commonNames.forEach(str ->{
            if(!nameCateMapping.containsKey(str)){
                nameCateMapping.put(str,new HashSet<>());
            }
        });
        //先获取所有类别
        Set<Integer> allCate = nameCateMapping.values().stream().flatMap(Set::stream).collect(Collectors.toSet());
        Set<String> compatibilitSet = new HashSet<>();
        nameCateMapping.forEach((k,v) ->{
            //取差值
            Set<Integer> otherCate = allCate.stream()
                    .filter(integer -> !v.contains(integer))
                    .collect(Collectors.toSet());
            List<String> otherProduct = commonNames.stream().filter(str -> !str.equals(k)).collect(Collectors.toList());
            compatibilityDtos.forEach(comb->{
                //类别
                if(RationalFieldType.CATEGORY.getType().equals(comb.getFirstType()) && v.contains(Integer.valueOf(comb.getFirstParam()))){
                    //判断配伍对象的类型
                    if(RationalFieldType.CATEGORY.getType().equals(comb.getSecondType())){
                        fillRes( comb,otherCate, nameCateMapping ,k, compatibilitSet ,result);
                    }
                    if(RationalFieldType.COMMON_NAME.getType().equals(comb.getSecondType())){
                        fillResCommonName( otherProduct ,comb,k, compatibilitSet ,result);
                    }
                }
                //通用名
                if(RationalFieldType.COMMON_NAME.getType().equals(comb.getFirstType()) && StringUtils.equals(k,comb.getFirstParam())){
                    //判断配伍对象的类型
                    if(RationalFieldType.CATEGORY.getType().equals(comb.getSecondType())){
                        fillRes( comb,otherCate, nameCateMapping ,k, compatibilitSet ,result);
                    }
                    if(RationalFieldType.COMMON_NAME.getType().equals(comb.getSecondType())){
                        fillResCommonName( otherProduct ,comb,k, compatibilitSet ,result);
                    }
                }
            });
        });
        // 过滤 通用名 (最高的保留一条)
        Map<String, RationalTipsVO> rationalTipsVOMap = result.stream()
                .collect(Collectors.toMap(c -> c.getSecName() + "#" + c.getProductName(), Function.identity()
                        , BinaryOperator.maxBy(Comparator.comparing(RationalTipsVO::getCaution))));

        return new ArrayList<>(rationalTipsVOMap.values());
    }

    private void fillResCommonName(List<String> otherProduct,InquiryCompatibilityDto comb,String k, Set<String> compatibilitSet ,List<RationalTipsVO> result){
        //配伍对象为类型匹配
        if(otherProduct.contains(comb.getSecondParam())){
            Boolean needAdd = getNeedFlag(compatibilitSet,comb);
            if(needAdd){
                result.add(buildTipByCompaCand(comb,k,comb.getSecondParam()));
            }
        }
    }

    private void fillRes(InquiryCompatibilityDto comb,Set<Integer> otherCate, Map<String,Set<Integer>> nameCateMapping ,String k, Set<String> compatibilitSet ,List<RationalTipsVO> result){
        //配伍对象为类型匹配
        if(otherCate.contains(Integer.valueOf(comb.getSecondParam()))){
            String secName =nameCateMapping.entrySet().stream().filter(stringSetEntry -> stringSetEntry.getValue().contains(StringUtil.convertStr(comb.getSecondParam()))).map(Map.Entry::getKey).
                    filter(str-> !StringUtils.equals(str,k)).findFirst().orElse("");
            Boolean needAdd = getNeedFlag(compatibilitSet,comb);
            if(needAdd){
                result.add(buildTipByCompaCand(comb,k,secName));
            }
        }
    }

    private Boolean getNeedFlag(Set<String> compatibilitSet, InquiryCompatibilityDto comb) {
        String planA = comb.getCompatId() + "@" + comb.getFirstParam() + "@" + comb.getSecondParam();
        String planB = comb.getCompatId() + "@" + comb.getSecondParam() + "@" + comb.getFirstParam();
        if(compatibilitSet.contains(planA) || compatibilitSet.contains(planB)){
            return false;
        }
        compatibilitSet.add(planA);
        compatibilitSet.add(planB);
        return true;
    }

    private RationalTipsVO buildTipByCompaCand(InquiryCompatibilityDto comb,String k ,String secName) {
        /*String firstParam = RationalFieldType.CATEGORY.getType() == firstType ? cateMap.get(Integer.valueOf(comb.getFirstParam())) : comb.getFirstParam();
        String secPram = RationalFieldType.CATEGORY.getType() == secType ? cateMap.get(Integer.valueOf(comb.getSecondParam())) : comb.getSecondParam();*/
        return RationalTipsVO.builder().commonName("配伍禁忌").caution(comb.getCaution())
                .cautionStr(CautionLevel.getTipsNameByType(comb.getCaution()))
                .limitType(LimitDrugType.incompatibility.getLimitType())
                .productName(k).secName(secName)
                .limitTypeStr(LimitDrugType.getDescByType(LimitDrugType.incompatibility.getLimitType()))
                .desc(k+"与"+secName+","+comb.getDescription())
                .build();
    }

    private List<RationalTipsVO> getTipsByDiagList(PatientInfo patientInfo,List<DrugInfo> drugInfos){
        // 构建商品信息诊断
        List<RationalTipsVO> diagProductTips = getDiagProductTips(patientInfo, drugInfos);
        List<RationalTipsVO> result = new ArrayList<>(diagProductTips);
        // 构建性别信息诊断
        RationalTipsVO diagSexTip = getDiagSexTips(patientInfo);
        if(diagSexTip!=null){
            result.add(diagSexTip);
        }
        // 过滤  诊断 + 通用名 (最高的保留一条)
        Map<String, RationalTipsVO> rationalTipsVOMap = result.stream()
                .collect(Collectors.toMap(c -> c.getDiagnosName() + "#" + c.getProductName(), Function.identity()
                        , BinaryOperator.maxBy(Comparator.comparing(RationalTipsVO::getCaution))));

        return new ArrayList<>(rationalTipsVOMap.values());

//        return result;
    }

    private RationalTipsVO getDiagSexTips(PatientInfo patientInfo) {
        if(CollectionUtils.isEmpty(patientInfo.getDiagnosisCodes())){
            return null;
        }

        InquiryDiagnosisDto infoDto = InquiryDiagnosisDto.builder().diagnosisCodes(patientInfo.getDiagnosisCodes()).diagnosisTypes(List.of(1,2,3)).status(CommonStatusEnum.ENABLE.getStatus()).build();
        List<InquiryDiagnosisDto> ykqDiagnosisInfoDtos = inquiryDiagnosisApi.queryDiagnosisByCondition(infoDto);
        // 过滤掉性别限制的
        List<InquiryDiagnosisDto> limitDiags = ykqDiagnosisInfoDtos.stream()
                .filter(d -> !DiagnosSexType.typeOfCodes(patientInfo.getSex()).contains(d.getSexLimit()))
                .collect(Collectors.toList());

        log.info("getDiagSexTips,limitDiags:{}",JSON.toJSONString(limitDiags));
        if(CollectionUtils.isEmpty(limitDiags)){
            return null;
        }
        String limitStr = limitDiags.stream().map(InquiryDiagnosisDto::getShowName).collect(Collectors.joining("、"));
        return RationalTipsVO.builder().commonName(LimitDrugType.getDescByType(LimitDrugType.diagnosis.getLimitType())).caution(CautionLevel.forbidden.getType())
                .cautionStr(CautionLevel.forbidden.getTipsName())
                .limitType(LimitDrugType.diagnosis.getLimitType())
                .limitTypeStr(LimitDrugType.getDescByType(LimitDrugType.diagnosis.getLimitType()))
                .productName("")
                .desc(RationalSexEnums.getNameByCode(patientInfo.getSex()) + "性患者不允许使用诊断(" + limitStr + ")")
                .build();
    }

    private List<RationalTipsVO> getDiagProductTips(PatientInfo patientInfo, List<DrugInfo> drugInfos) {
        List<RationalTipsVO> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(patientInfo.getDiagnosisNames())){
            return result;
        }
        List<InquiryDiagnosiscDto> diagnosiscDtos = inquiryRationalDiagnosisService.queryDiagnosisConfig(patientInfo.getDiagnosisNames());
        if(CollectionUtils.isEmpty(diagnosiscDtos)){
            return result;
        }
        //获取通用名集合
        List<String> commonNames = drugInfos.stream().map(DrugInfo::getCommonName).collect(Collectors.toList());

        //根据通用名查询商品信息获取六级分类末级id
        List<InquiryRationalProduct> productList = inquiryRationalProductMapper.queryByCondition(InquiryRationalProductCommonDto.builder().commonNames(commonNames).build());
        Map<String, String> categoryProductMap = productList.stream().filter(p -> StringUtils.isNotBlank(p.getCategoryLv6Id())).collect(Collectors.toMap(InquiryRationalProduct::getCommonName, InquiryRationalProduct::getCategoryLv6Id, (a, b) -> a));
        // 根据当前商品的六级分类，找上游所有 分类
        List<Integer> lv6IdList = productList.stream().filter(p->StringUtils.isNotBlank(p.getCategoryLv6Id())).map(InquiryRationalProduct::getCategoryLv6Id).map(Integer::parseInt).distinct().collect(Collectors.toList());

        // 看这些分类存在 在哪些类别上
        Map<Integer, List<SaasCategoryVo>> parentPathByIds = midCategoryApi.queryCategoryParentPathByIds(lv6IdList);

        List<Integer> parendIds = parentPathByIds.entrySet().stream()
                .flatMap(entry -> entry.getValue().stream().map(SaasCategoryVo::getId))
                .collect(Collectors.toList());

        // 查六级分类在哪些类别上
        List<InquiryRationalCategoryDetail> categoryDetails =  CollectionUtils.isEmpty(parendIds) ? new ArrayList<>() :inquiryRationalCategoryDetailService.queryByCondition(InquiryRationalCategoryDetailDto.builder().classifyIds(parendIds).build());

        // <类别id , <六级分类>>
        Map<Integer, List<Integer>> categoryMap = new HashMap<>();

        for (InquiryRationalCategoryDetail categoryDetail : categoryDetails) {
            categoryMap.putIfAbsent(categoryDetail.getCategoryId(), new ArrayList<>());
            List<Integer> caList = categoryMap.get(categoryDetail.getCategoryId());
            List<Integer> cKey = parentPathByIds.entrySet().stream().filter(entry -> entry.getValue().stream().anyMatch(c -> Objects.equals(c.getId(), categoryDetail.getClassifyId()))).map(Map.Entry::getKey).collect(Collectors.toList());
            caList.addAll(cKey);
        }

        diagnosiscDtos.forEach(obj ->{
            String products = obj.getCommonProducts();
            List<String> diaProducts = StringUtils.isNotBlank(products) ? Arrays.stream(products.split(",")).collect(Collectors.toList()):new ArrayList<>();
            diaProducts.forEach(str -> commonNames.forEach(name ->{
                if(StringUtils.equals(str,name)){
                    result.add(buildTipByProductCommonName(obj,name));
                }
            }));

            // 找类别下的冲突
            if(StringUtils.isNotBlank(obj.getCategories())){
                Arrays.stream(obj.getCategories().split(",")).forEach(c->{
                    for (String commonName : commonNames) {
                        if(CollectionUtils.isNotEmpty(diaProducts) && diaProducts.contains(commonName)){
                            continue;
                        }
                        // 当前商品的六级分类
                        String category = categoryProductMap.get(commonName);

                        List<Integer> cds = categoryMap.get(StringUtil.convertStr(c));

                        // 类别下的 六级分类下
                        if(StringUtils.isNotBlank(category) && CollectionUtils.isNotEmpty(cds)){
                            if(cds.contains(StringUtil.convertStr(category))){
                                result.add(buildTipByProductCommonName(obj,commonName));
                            }
                        }
                    }
                });
            }
        });
        return result;
    }

    private RationalTipsVO buildTipByCategory(InquiryDiagnosiscDto obj, SaasCategoryVo vo) {
        return RationalTipsVO.builder().commonName(LimitDrugType.getDescByType(LimitDrugType.diagnosis.getLimitType())).caution(obj.getCaution())
                .cautionStr(CautionLevel.getTipsNameByType(obj.getCaution()))
                .limitType(LimitDrugType.diagnosis.getLimitType())
                .limitTypeStr(LimitDrugType.getDescByType(LimitDrugType.diagnosis.getLimitType()))
                .desc(obj.getDiagnosisCodes()+"患者"+CautionLevel.getTipsNameByType(obj.getCaution())+vo.getTitle()+","+obj.getDescription())
                .build();
    }

    private RationalTipsVO buildTipByProductCommonName(InquiryDiagnosiscDto obj, String name) {
        return RationalTipsVO.builder().commonName(LimitDrugType.getDescByType(LimitDrugType.diagnosis.getLimitType())).caution(obj.getCaution())
                .cautionStr(CautionLevel.getTipsNameByType(obj.getCaution()))
                .limitType(LimitDrugType.diagnosis.getLimitType())
                .limitTypeStr(LimitDrugType.getDescByType(LimitDrugType.diagnosis.getLimitType()))
                .diagnosName(obj.getDiagnosisCodes())
                .productName(name)
                .desc(obj.getDiagnosisCodes()+"患者"+CautionLevel.getTipsNameByType(obj.getCaution())+name+","+obj.getDescription())
                .build();
    }

}
