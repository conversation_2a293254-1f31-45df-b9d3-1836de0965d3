package com.xyy.saas.inquiry.common.convert.rational;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.common.api.rational.dto.DosageLimitDto;
import com.xyy.saas.inquiry.common.api.rational.dto.LimitDrugCatalogDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRepeatUseDrugLimitVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasCategoryVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasUsageAndDosageReviewDrugDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.UsageAndDosageReviewDto;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryDrugCatalogLimitPo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRepeatUseDrugLimitPo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.SaasDosageLimitPo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.SaasUsageAndDosageReviewDrugPo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.SaasUsageAndDosageReviewPo;
import com.xyy.saas.inquiry.product.api.mid.dto.SaasCategoryDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;
import java.util.Map;

/**
 * @Author: xucao
 * @DateTime: 2025/8/8 17:03
 * @Description: 合理用药相关类型转换器
 **/
@Mapper
public interface RationalConvert {
    RationalConvert INSTANCE = Mappers.getMapper(RationalConvert.class);

    List<SaasCategoryVo> convertDTO2VO(List<SaasCategoryDto> dtoList);

    List<List<SaasCategoryVo>> convertDTOS2VOS(List<List<SaasCategoryDto>> dtoList);

    Map<Integer, List<List<SaasCategoryVo>>> convertMap (Map<Integer, List<List<SaasCategoryDto>>> map);

    Map<String, List<List<SaasCategoryVo>>> convertStrMap (Map<String, List<List<SaasCategoryDto>>> map);

    PageResult<List<SaasCategoryVo>> convertPage(PageResult<List<SaasCategoryDto>> listPageResult);

    PageResult<InquiryRepeatUseDrugLimitVo> convertPageInfo(PageResult<InquiryRepeatUseDrugLimitPo> pageResult);

    PageResult<LimitDrugCatalogDto> convertLimitDrugCatalogPageInfo(PageResult<InquiryDrugCatalogLimitPo> pageResult);

    SaasDosageLimitPo convertDTO2PO(DosageLimitDto dto);

    PageResult<DosageLimitDto> convertDosgPage(PageResult<SaasDosageLimitPo> saasDosageLimitPoPageResult);

    PageResult<SaasUsageAndDosageReviewDrugDto> convertUsageAndDosageReviewDrugPage(PageResult<SaasUsageAndDosageReviewDrugPo> saasUsageAndDosageReviewDrugPoPageResult);

    PageResult<UsageAndDosageReviewDto> convertUsageAndDosageReviewPage(PageResult<SaasUsageAndDosageReviewPo> saasUsageAndDosageReviewPoPageResult);
}
