package com.xyy.saas.inquiry.common.service.rational.impl;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageDrugRuleDto;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageRuleDrugQueryDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasUsageAndDosageReviewDrugQueryDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasUsageAndDosageReviewRuleQueryDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.UsageAndDosageReviewDto;
import com.xyy.saas.inquiry.common.convert.rational.RationalConvert;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.SaasUsageAndDosageReviewDrugPo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.SaasUsageAndDosageReviewPo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.SaasUsageAndDosageReviewRulePo;
import com.xyy.saas.inquiry.common.dal.mysql.rational.SaasUsageAndDosageReviewMapper;
import com.xyy.saas.inquiry.common.service.rational.SaasUsageAndDosageReviewDrugService;
import com.xyy.saas.inquiry.common.service.rational.SaasUsageAndDosageReviewRuleService;
import com.xyy.saas.inquiry.common.service.rational.SaasUsageAndDosageReviewService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * 用法用量审查(SaasUsageAndDosageReview)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-02 16:15:00
 */
@Slf4j
@Service
public class SaasUsageAndDosageReviewServiceImpl implements SaasUsageAndDosageReviewService {

    @Resource
    private SaasUsageAndDosageReviewMapper saasUsageAndDosageReviewMapper;

    @Resource
    private SaasUsageAndDosageReviewRuleService saasUsageAndDosageReviewRuleService;

    @Resource
    private SaasUsageAndDosageReviewDrugService saasUsageAndDosageReviewDrugService;

    // 拼接字符
    private static final String JOINT_CHAR = "______";


    @Value("${useFrequency.list.sort.map:{\n"
        + "    \"1次/6月\": 10,\n"
        + "    \"1次/月\": 20,\n"
        + "    \"1次/4周\": 30,\n"
        + "    \"1次/3周\": 40,\n"
        + "    \"每两周一次\": 50,\n"
        + "    \"隔周一次\": 60,\n"
        + "    \"1次/周\": 70,\n"
        + "    \"2次/周\": 80,\n"
        + "    \"3次/周\": 90,\n"
        + "    \"隔天1次\": 100,\n"
        + "    \"1次/天\": 110,\n"
        + "    \"立即服用\": 120,\n"
        + "    \"睡前服用\": 130,\n"
        + "    \"必要时\": 140,\n"
        + "    \"紧急时\": 150,\n"
        + "    \"其它\": 160,\n"
        + "    \"2次/天\": 170,\n"
        + "    \"1次/12小时\": 180,\n"
        + "    \"3次/天\": 190,\n"
        + "    \"1次/8小时\": 200,\n"
        + "    \"4次/天\": 210,\n"
        + "    \"1次/6小时\": 220,\n"
        + "    \"5次/天\": 230,\n"
        + "    \"1次/4.5小时\": 240,\n"
        + "    \"6次/天\": 250,\n"
        + "    \"1次/4小时\": 260,\n"
        + "    \"1次/3小时\": 270,\n"
        + "    \"10次/天\": 280,\n"
        + "    \"1次/2小时\": 290,\n"
        + "    \"12次/天\": 300,\n"
        + "    \"1次/小时\": 310\n"
        + "}}")
    private String useFrequencyListSortMap;

    /**
     * 新增数据
     *
     * @param saasUsageAndDosageReviewPo 实例对象
     * @return 实例对象
     */
    @Override
    public SaasUsageAndDosageReviewPo insert(SaasUsageAndDosageReviewPo saasUsageAndDosageReviewPo) {

        saasUsageAndDosageReviewMapper.insert(saasUsageAndDosageReviewPo);

        return saasUsageAndDosageReviewPo;
    }

    /**
     * 修改数据
     *
     * @param saasUsageAndDosageReviewPo 实例对象
     * @return 实例对象
     */
    @Override
    public Boolean update(SaasUsageAndDosageReviewPo saasUsageAndDosageReviewPo) {

        return saasUsageAndDosageReviewMapper.updateById(saasUsageAndDosageReviewPo) > 0;
    }

    @Override
    public PageResult<UsageAndDosageReviewDto> pageQuery(UsageAndDosageReviewDto usageAndDosageReviewDto) {

        PageResult<SaasUsageAndDosageReviewPo> poPageInfo = saasUsageAndDosageReviewMapper.queryByCondition(usageAndDosageReviewDto);

        return RationalConvert.INSTANCE.convertUsageAndDosageReviewPage(poPageInfo);
    }

    @Override
    public List<SaasUsageAndDosageReviewPo> queryByCondition(UsageAndDosageReviewDto usageAndDosageReviewDto) {
        return saasUsageAndDosageReviewMapper.queryList(usageAndDosageReviewDto);
    }

    @Override
    public boolean updateRelationRuleCount(List<Long> idList, Integer count) {

        if (CollectionUtils.isEmpty(idList) || count == null) {
            return false;
        }

        saasUsageAndDosageReviewMapper.updateRelationRuleCount(idList, count);

        return true;
    }

    @Override
    public boolean updateRelationDrugCount(List<Long> idList, Integer count) {

        if (CollectionUtils.isEmpty(idList) || count == null) {
            return false;
        }

        saasUsageAndDosageReviewMapper.updateRelationDrugCount(idList, count);

        return true;
    }

    @Override
    public UsageAndDosageDrugRuleDto queryRuleByDrug(UsageAndDosageRuleDrugQueryDto usageAndDosageRuleDrugQueryDto) {

        log.info("SaasUsageAndDosageReviewServiceImpl#queryRuleByDrug -> param {}", JSON.toJSONString(usageAndDosageRuleDrugQueryDto));

        if (usageAndDosageRuleDrugQueryDto == null
                || usageAndDosageRuleDrugQueryDto.getAge() == null
                || usageAndDosageRuleDrugQueryDto.getAge() <= 0
                || CollectionUtils.isEmpty(usageAndDosageRuleDrugQueryDto.getUsageAndDosageRuleDrugDetailQueryDtoList())) {
            return UsageAndDosageDrugRuleDto.builder().build();
        }

        Set<String> commonNameSet = new HashSet<>();
        Set<String> specificationSet = new HashSet<>();

        for (UsageAndDosageRuleDrugQueryDto.UsageAndDosageRuleDrugDetailQueryDto item : usageAndDosageRuleDrugQueryDto.getUsageAndDosageRuleDrugDetailQueryDtoList()) {
            if (StringUtils.isNotBlank(item.getCommonName())) {
                commonNameSet.add(item.getCommonName());
            }
            if (StringUtils.isNotBlank(item.getSpecification())) {
                specificationSet.add(item.getSpecification());
            }
        }

        if (CollectionUtils.isEmpty(commonNameSet)) {
            return UsageAndDosageDrugRuleDto.builder().build();
        }

        // 根据通用名和规则查询全部的药品数据
        List<SaasUsageAndDosageReviewDrugPo> saasUsageAndDosageReviewDrugPoList = saasUsageAndDosageReviewDrugService.queryByCondition(
            SaasUsageAndDosageReviewDrugQueryDto.builder().commonNameSet(commonNameSet).specificationSet(specificationSet).build());

        if (CollectionUtils.isEmpty(saasUsageAndDosageReviewDrugPoList)) {
            return UsageAndDosageDrugRuleDto.builder().build();
        }

        // 拼接入参的通用名和规格
        List<String> allCommonNameAndSpecificationList = usageAndDosageRuleDrugQueryDto.getUsageAndDosageRuleDrugDetailQueryDtoList().stream()
                .map(item -> StringUtils.defaultString(item.getCommonName(), "") + JOINT_CHAR + StringUtils.defaultString(item.getSpecification(), ""))
                .distinct()
                .collect(Collectors.toList());

        // 过滤出入参条件对应的药品信息数据
        Map<String, Long> allCommonNameAndSpecificationMap = saasUsageAndDosageReviewDrugPoList.stream()
                .filter(item -> allCommonNameAndSpecificationList.contains(item.getCommonName() + JOINT_CHAR + item.getSpecification()))
                .collect(Collectors.toMap(item -> item.getCommonName() + JOINT_CHAR + item.getSpecification(), SaasUsageAndDosageReviewDrugPo::getUsageAndDosageReviewId, (v1, v2) -> v2));

        // 根据药品对应的审查id查询对应的审查规则
        List<SaasUsageAndDosageReviewRulePo> saasUsageAndDosageReviewRulePoList = saasUsageAndDosageReviewRuleService.queryByCondition(
            SaasUsageAndDosageReviewRuleQueryDto.builder().usageAndDosageReviewIdList(Lists.newArrayList(allCommonNameAndSpecificationMap.values())).build());

        if (CollectionUtils.isEmpty(saasUsageAndDosageReviewRulePoList)) {
            return UsageAndDosageDrugRuleDto.builder().build();
        }

        // 根据年龄筛选出对应的审查规则，每个审查都能根据年龄筛选出唯一一个审查规则
        List<SaasUsageAndDosageReviewRulePo> filterSaasUsageAndDosageReviewRulePoList = saasUsageAndDosageReviewRulePoList.stream()
                .filter(item -> usageAndDosageRuleDrugQueryDto.getAge() >= item.getMinAge() && usageAndDosageRuleDrugQueryDto.getAge() <= item.getMaxAge())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filterSaasUsageAndDosageReviewRulePoList)) {
            return UsageAndDosageDrugRuleDto.builder().build();
        }

        List<Long> usageAndDosageReviewIdList = filterSaasUsageAndDosageReviewRulePoList.stream().map(SaasUsageAndDosageReviewRulePo::getUsageAndDosageReviewId).distinct().collect(Collectors.toList());

        // 查询审查信息
        List<SaasUsageAndDosageReviewPo> saasUsageAndDosageReviewPoList = saasUsageAndDosageReviewMapper.queryList(UsageAndDosageReviewDto.builder().idList(usageAndDosageReviewIdList).enableStatus(CommonStatusEnum.ENABLE.getStatus()).build());

        if (CollectionUtils.isEmpty(saasUsageAndDosageReviewPoList)) {
            return UsageAndDosageDrugRuleDto.builder().build();
        }

        // 审查规则map
        Map<Long, SaasUsageAndDosageReviewRulePo> saasUsageAndDosageReviewRulePoMap = filterSaasUsageAndDosageReviewRulePoList.stream().collect(Collectors.toMap(SaasUsageAndDosageReviewRulePo::getUsageAndDosageReviewId, Function.identity(), (v1, v2) -> v2));

        // 审查map
        Map<Long, SaasUsageAndDosageReviewPo> saasUsageAndDosageReviewPoMap = saasUsageAndDosageReviewPoList.stream().collect(Collectors.toMap(SaasUsageAndDosageReviewPo::getId, Function.identity(), (v1, v2) -> v2));

        List<UsageAndDosageDrugRuleDto.UsageAndDosageDrugRuleDetailDto> resultList = new ArrayList<>();

        for (Map.Entry<String, Long> mapItem : allCommonNameAndSpecificationMap.entrySet()) {

            String commonNameAndSpecificationName = mapItem.getKey();
            Long usageAndDosageReviewId = mapItem.getValue();

            if (!saasUsageAndDosageReviewRulePoMap.containsKey(usageAndDosageReviewId) || !saasUsageAndDosageReviewPoMap.containsKey(usageAndDosageReviewId)) {
                continue;
            }

            String[] split = commonNameAndSpecificationName.split(JOINT_CHAR);

            String useFrequencyListJson = saasUsageAndDosageReviewRulePoMap.get(usageAndDosageReviewId).getUseFrequencyListJson();

            resultList.add(UsageAndDosageDrugRuleDto.UsageAndDosageDrugRuleDetailDto.builder()
                    .commonName(split[0])
                    .specification(split.length > 1 ? split[1] : "")
                    .minSingleDose(saasUsageAndDosageReviewRulePoMap.get(usageAndDosageReviewId).getMinSingleDose())
                    .maxSingleDose(saasUsageAndDosageReviewRulePoMap.get(usageAndDosageReviewId).getMaxSingleDose())
                    .ageRiskTipsSwitch(saasUsageAndDosageReviewRulePoMap.get(usageAndDosageReviewId).getAgeRiskTipsSwitch())
                    .useFrequencyListList(this.sortUseFrequencyListList(useFrequencyListJson))
                    .directions(saasUsageAndDosageReviewPoMap.get(usageAndDosageReviewId).getDirections())
                    .singleUnit(saasUsageAndDosageReviewPoMap.get(usageAndDosageReviewId).getSingleUnit())
                    .build());
        }

        log.info("SaasUsageAndDosageReviewServiceImpl#queryRuleByDrug -> result {}", JSON.toJSONString(resultList));

        return UsageAndDosageDrugRuleDto.builder().usageAndDosageDrugRuleDetailDtoList(resultList).build();
    }

    /**
     * 用药频率排序
     *
     * @param useFrequencyListJson
     * @return java.util.List<java.lang.String>
     * <AUTHOR> 10/14/24 8:09 PM
     */
    private List<String> sortUseFrequencyListList(String useFrequencyListJson) {

        if (StringUtils.isBlank(useFrequencyListJson)) {
            return Lists.newArrayList();
        }

        List<String> useFrequencyListJsonList = JSONObject.parseArray(useFrequencyListJson, String.class);

        if (StringUtils.isBlank(useFrequencyListSortMap)) {
            return useFrequencyListJsonList;
        }

        Map<String, Integer> allUseFrequencyListJsonSortMap = JSON.parseObject(useFrequencyListSortMap, new TypeReference<HashMap<String, Integer>>() {
        });

        Map<String, Integer> useFrequencyListJsonSortMap = new HashMap<>();
        for (String useFrequency : useFrequencyListJsonList) {

            if (allUseFrequencyListJsonSortMap.containsKey(useFrequency)) {
                useFrequencyListJsonSortMap.put(useFrequency, allUseFrequencyListJsonSortMap.get(useFrequency));
                continue;
            }

            useFrequencyListJsonSortMap.put(useFrequency, 99999);
        }

        // 将Map转换为一个List，List中的元素是Map.Entry对象
        List<Map.Entry<String, Integer>> list = new ArrayList<>(useFrequencyListJsonSortMap.entrySet());

        // 根据值对List进行排序
        list.sort(Map.Entry.comparingByValue());

        return list.stream().map(Map.Entry::getKey).collect(Collectors.toList());
    }
}
