package com.xyy.saas.inquiry.common.controller.admin.rational.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.enums.rational.RationalStatusEnum;
import com.xyy.saas.inquiry.pojo.excel.ImportExcelVoDto;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2023/11/06 18:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InquiryRationalCategoryVo extends ImportExcelVoDto {


    /**
     * 索引 扩展导入用
     */
    private Integer index;

    /**
     * 类别 eg:抗生素
     */
    @ExcelProperty(value = "类别", index = 0)
    @Size(max = 50, message = "类别超出最大长度50限制")
    @NotBlank(message = "类别名称必填")
    private String name;

    /**
     * 六级分类 eg: 中西成药 > 抗感冒用药 > 流行性感冒 > 头痛，四肢酸痛、乏力 > 连花清瘟 > 连花清瘟胶囊
     */
    @ExcelProperty(value = "六级分类", index = 1)
    private String categoryPath;
    /**
     * 状态：0禁用，1启用
     */
    @ExcelProperty(value = "是否启用", index = 2)
    @Size(max = 64, message = "是否启用超出最大长度2限制")
    private String status;


    /**
     * 分类层级
     */
    private List<SaasCategoryVo> categoryVoList;

    @Override
    public void valid() {
        if (RationalStatusEnum.valueOfName(status) == null) {
            this.errMsg += "是否启用仅支持填写“启用”/“禁用”";
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        InquiryRationalCategoryVo that = (InquiryRationalCategoryVo) o;
        return Objects.equals(index, that.index) &&
            Objects.equals(name, that.name) &&
            Objects.equals(categoryPath, that.categoryPath) &&
            Objects.equals(status, that.status) &&
            Objects.equals(categoryVoList, that.categoryVoList);
    }

    @Override
    public int hashCode() {
        return Objects.hash(index, name, categoryPath, status, categoryVoList);
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setCategoryVoList(List<SaasCategoryVo> categoryVoList) {
        this.categoryVoList = categoryVoList;
    }
}
