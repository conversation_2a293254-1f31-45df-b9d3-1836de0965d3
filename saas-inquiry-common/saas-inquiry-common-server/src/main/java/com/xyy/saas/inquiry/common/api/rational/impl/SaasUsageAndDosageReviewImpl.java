package com.xyy.saas.inquiry.common.api.rational.impl;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.common.api.rational.SaasUsageAndDosageReviewApi;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageDrugRuleDto;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageRuleDrugQueryDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasUsageAndDosageReviewDrugDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.UsageAndDosageReviewDto;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.SaasUsageAndDosageReviewPo;
import com.xyy.saas.inquiry.common.service.rational.SaasUsageAndDosageReviewDrugService;
import com.xyy.saas.inquiry.common.service.rational.SaasUsageAndDosageReviewService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.List;

/**
 * 用法用量审查api实现
 *
 * <AUTHOR>
 * @Date 9/2/24 4:56 PM
 */
@Service
public class SaasUsageAndDosageReviewImpl implements SaasUsageAndDosageReviewApi {

    @Resource
    private SaasUsageAndDosageReviewService saasUsageAndDosageReviewService;

    @Resource
    private SaasUsageAndDosageReviewDrugService saasUsageAndDosageReviewDrugService;

    @Override
    public CommonResult<Boolean> save(UsageAndDosageReviewDto usageAndDosageReviewDto) {

        UsageAndDosageReviewDto queryParam = UsageAndDosageReviewDto.builder()
                .commonName(usageAndDosageReviewDto.getCommonName())
                .ingredientDosage(usageAndDosageReviewDto.getIngredientDosage())
                .singleUnit(usageAndDosageReviewDto.getSingleUnit())
                .directions(usageAndDosageReviewDto.getDirections()).build();
        List<SaasUsageAndDosageReviewPo> saasUsageAndDosageReviewPos = saasUsageAndDosageReviewService.queryByCondition(queryParam);

        if (CollectionUtils.isNotEmpty(saasUsageAndDosageReviewPos)) {
            return CommonResult.error("【通用名 + 成份剂量 + 给药单位 + 给药途径】不能重复");
        }

        SaasUsageAndDosageReviewPo saasUsageAndDosageReviewPo = SaasUsageAndDosageReviewPo.builder()
                .commonName(usageAndDosageReviewDto.getCommonName())
                .ingredientDosage(usageAndDosageReviewDto.getIngredientDosage())
                .singleUnit(usageAndDosageReviewDto.getSingleUnit())
                .directions(usageAndDosageReviewDto.getDirections())
                .relationRuleCount(0)
                .relationDrugCount(0)
                .yn(CommonStatusEnum.ENABLE.getStatus())
                .enableStatus(CommonStatusEnum.ENABLE.getStatus()).build();

        saasUsageAndDosageReviewService.insert(saasUsageAndDosageReviewPo);

        return CommonResult.success(saasUsageAndDosageReviewPo.getId() != null && saasUsageAndDosageReviewPo.getId() > 0);
    }

    @Override
    public CommonResult<PageResult<UsageAndDosageReviewDto>> pageQuery(UsageAndDosageReviewDto usageAndDosageReviewDto) {

        return CommonResult.success(saasUsageAndDosageReviewService.pageQuery(usageAndDosageReviewDto));
    }

    @Override
    public CommonResult<Boolean> updateEnableStatus(UsageAndDosageReviewDto usageAndDosageReviewDto) {

        if (usageAndDosageReviewDto == null || usageAndDosageReviewDto.getId() == null) {
            return CommonResult.error("参数异常");
        }

        if (!CommonStatusEnum.ENABLE.getStatus().equals(usageAndDosageReviewDto.getEnableStatus()) && !CommonStatusEnum.DISABLE.getStatus().equals(usageAndDosageReviewDto.getEnableStatus())) {
            return CommonResult.error("开启禁用状态错误");
        }

        SaasUsageAndDosageReviewPo saasUsageAndDosageReviewPo = SaasUsageAndDosageReviewPo.builder()
                .id(usageAndDosageReviewDto.getId())
                .enableStatus(usageAndDosageReviewDto.getEnableStatus()).build();

        return CommonResult.success(saasUsageAndDosageReviewService.update(saasUsageAndDosageReviewPo));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Boolean> deleteById(UsageAndDosageReviewDto usageAndDosageReviewDto) {

        if (usageAndDosageReviewDto == null || usageAndDosageReviewDto.getId() == null) {
            return CommonResult.error("参数异常");
        }

        SaasUsageAndDosageReviewPo saasUsageAndDosageReviewPo = SaasUsageAndDosageReviewPo.builder()
                .id(usageAndDosageReviewDto.getId())
                .yn(CommonStatusEnum.DISABLE.getStatus()).build();
        saasUsageAndDosageReviewService.update(saasUsageAndDosageReviewPo);

        SaasUsageAndDosageReviewDrugDto saasUsageAndDosageReviewDrugDto = SaasUsageAndDosageReviewDrugDto.builder()
                .usageAndDosageReviewId(usageAndDosageReviewDto.getId()).build();
        saasUsageAndDosageReviewDrugService.deleteById(saasUsageAndDosageReviewDrugDto);

        return CommonResult.success(true);
    }

    @Override
    public CommonResult<UsageAndDosageDrugRuleDto> queryRuleByDrug(UsageAndDosageRuleDrugQueryDto usageAndDosageRuleDrugQueryDto) {

        return CommonResult.success(saasUsageAndDosageReviewService.queryRuleByDrug(usageAndDosageRuleDrugQueryDto));
    }
}
