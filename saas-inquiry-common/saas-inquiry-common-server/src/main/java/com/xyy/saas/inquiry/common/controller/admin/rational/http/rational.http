###  1.门店登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "15172440267",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}


### 2.1限制类药品查询
POST {{baseAdminKernelUrl}}/kernel/rational/product/queryList
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "pageNo": 1,
  "pageSize": 50,
  "commonName": "",
  "categoryLv6Id": "",
  "ageLimits": null,
  "ageRangeLimit": null,
  "healthLimit": null,
  "womenLimit": null,
  "age": "0",
  "day1": 0,
  "day2": "天",
  "day3": 1,
  "day4": "岁",
  "ingredientList": [],
  "diagnosisList": [],
  "compatibilityCommonList": []
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}





### 2.诊断禁忌列表查询
POST {{baseAdminKernelUrl}}/kernel/rational/diagnosis/queryList
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "diagnosisPref": "",
  "diagnosisName": "",
  "categoryName": "",
  "commonName": "",
  "caution": null,
  "status": null,
  "pageNo": 1,
  "pageSize": 50
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}


### 3.配伍禁忌列表查询
POST {{baseAdminKernelUrl}}/kernel/rational/compatibility/queryList
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "pref": "",
  "categoryName": "",
  "commonName": "",
  "caution": null,
  "status": null,
  "pageNum": 1,
  "pageSize": 50
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 4.限制类药品列表查询
POST {{baseAdminKernelUrl}}/kernel/rational/product/queryList
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "pageNo": 1,
  "pageSize": 50,
  "commonName": "",
  "categoryLv6Id": "",
  "ageLimits": null,
  "ageRangeLimit": null,
  "healthLimit": null,
  "womenLimit": null,
  "age": "0",
  "day1": 0,
  "day2": "天",
  "day3": 1,
  "day4": "岁",
  "ingredientList": [],
  "diagnosisList": [],
  "compatibilityCommonList": []
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 5.限制类药品列表查询
POST {{baseAdminKernelUrl}}/kernel/usageAndDosage/review/deleteById
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "id": 1958107168524861442
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 6.用法用量审查规则查询
POST {{baseAdminKernelUrl}}/kernel/usageAndDosage/review/rule/queryByCondition
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{"usageAndDosageReviewId":"1958401213681442817"}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 7.诊断禁忌查询
POST {{baseAdminKernelUrl}}/kernel/rational/diagnosis/queryList
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{"categoryName":"","caution":null,"commonName":"","diagnosisName":"","diagnosisPref":"","pageNo":1,"pageSize":50,"status":null}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 8.配伍禁忌查询
POST {{baseAdminKernelUrl}}/kernel/rational/compatibility/queryList
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{"categoryName":"","caution":null,"commonName":"","pageNo":1,"pageSize":50,"pref":"","status":null}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  console.log(response.body)
%}

### 9.限制类药品列表查询
POST {{baseAdminKernelUrl}}/kernel/dosageLimit/pageQuery
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "pageNum": 1,
  "pageSize": 50,
  "generalName": "",
  "standardId": "",
  "whetherLongPrescription": ""
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  console.log(response.body)
%}

### 10.查询关联药品
POST {{baseAdminKernelUrl}}/kernel/usageAndDosage/review/drug/pageSearchCenterDrug
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{"commonName":"感冒灵","specification":"10g","pageNo":1,"pageSize":10}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  console.log(response.body)
%}

### 11.新增诊断禁忌
POST {{baseAdminKernelUrl}}/kernel/rational/diagnosis/createOrUpdate
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{"diagnosisPref":"","caution":2,"status":0,"description":"测试3","diagnosisCodes":["异物入目"],"categories":[15156],"commonProducts":["634325321131"]}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  console.log(response.body)
%}


### 12.药品推荐诊断
POST {{baseAppKernelUrl}}/kernel/hospital/inquiry-diagnosis/recommend-diagnosis
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{"productSearchList":[{"medicineType":0,"pref":"10787","productName":"阿奇霉素胶囊"}],"prescriptionType":"","tenantId":"1928093937057771521"}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  console.log(response.body)
%}

### 13.合理用药审核
POST {{baseAppKernelUrl}}/kernel/hospital/rational/limitDrugAudit
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "age": "31",
  "sex": 1,
  "healthSituation": 0,
  "womenSituation": 0,
  "allergic": [],
  "diagnosisNames": [
    "急性支气管炎"
  ],
  "diagnosisCodes": [
    "CA42.Z"
  ],
  "drugDtoList": [
    {
      "pref": "10787",
      "standardId": "772",
      "productName": "三线品种",
      "commonName": "阿奇霉素胶囊",
      "quantity": 1,
      "attributeSpecification": "0.125g*6s",
      "manufacturer": "北京太洋药业股份有限公司",
      "useFrequency": "1次/天",
      "useFrequencyValue": "1次/天",
      "singleDose": "4",
      "singleUnit": "粒",
      "directions": "口服",
      "approvalNumber": "国药准字H10950066",
      "unitName": "盒",
      "dosageForm": "胶囊剂",
      "isNewAdd": true
    }
  ],
  "drugs": [
    "阿奇霉素胶囊"
  ],
  "medicineType": 0,
  "inquiryWayType": 1
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  console.log(response.body)
%}