package com.xyy.saas.inquiry.common.util;

import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.saas.inquiry.common.annotation.excel.ExcelName;
import com.xyy.saas.inquiry.common.convert.type.ConvertRegistry;
import com.xyy.saas.inquiry.common.convert.type.ValueStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.simpleframework.xml.convert.ConvertException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ExcelUtils {

    private static Map<Class,Map<String, Method>> FIELD_SETMETHOD_MAP = Maps.newConcurrentMap();

    private static Map<Class,Map<String, Class>> FIELD_TYPE_MAP = Maps.newConcurrentMap();

    private static Map<Class,Map<String, Method>> FIELD_GETMETHOD_MAP = Maps.newConcurrentMap();

    private static Map<Class,LinkedHashMap<String, String>> HEAD_FIELD_MAP = Maps.newConcurrentMap();

    private static Map<Class,BiMap<String, String>> HEAD_FIELD_INVERSE_MAP = Maps.newConcurrentMap();

    public static LinkedHashMap<String, String> getHeadFieldMap(Class clazz){
        LinkedHashMap<String, String> headFieldMap = HEAD_FIELD_MAP.get(clazz);
        if(MapUtils.isEmpty(headFieldMap)){
            Field[] declaredFields = clazz.getDeclaredFields();
            TreeMap<Integer, Map<String,String>> treeMap = Maps.newTreeMap();
            for (Field field : declaredFields) {
                ExcelName annotation = field.getAnnotation(ExcelName.class);
                if(annotation != null){
                    String order = annotation.order();
                    Integer key = Integer.parseInt(order);
                    String value = annotation.value();
                    String filter = annotation.filter();
                    String name = field.getName();
                    if(StringUtils.isNotEmpty(filter)){
                        name = name + "_" + filter;
                    }
                    Map<String,String> tmp = Maps.newHashMap();
                    tmp.put(value, name);
                    treeMap.put(key, tmp);
                }
            }
            Set<Integer> keySet = treeMap.keySet();
            for (Integer key : keySet) {
                Map<String, String> tmp = treeMap.get(key);
                HEAD_FIELD_MAP.compute(clazz, (k,v) -> {
                    if(v == null){
                        v = Maps.newLinkedHashMap();
                    }
                    for (String rk : tmp.keySet()) {
                        v.put(rk, tmp.get(rk));
                    }
                    return v;
                });
            }
            headFieldMap = HEAD_FIELD_MAP.get(clazz);
        }
        return headFieldMap;
    }

    public static BiMap<String,String> getHeadFieldInverseMap(Class clazz){
        BiMap<String, String> biMap = HEAD_FIELD_INVERSE_MAP.get(clazz);
        if(MapUtils.isEmpty(biMap)){
            LinkedHashMap<String, String> headFieldMap = getHeadFieldMap(clazz);
            BiMap<String, String> inverse = HashBiMap.create(headFieldMap).inverse();
            HEAD_FIELD_INVERSE_MAP.put(clazz, inverse);
            biMap = HEAD_FIELD_INVERSE_MAP.get(clazz);
        }
        return biMap;
    }

    private static Map<String, Method> getSetMethodMapByClazz(Class clazz){
        if(MapUtils.isEmpty(FIELD_SETMETHOD_MAP.get(clazz))){
            Field[] declaredFields = clazz.getDeclaredFields();
            Method[] methods = clazz.getMethods();
            Map<String, Method> methodMap = Stream.of(methods).collect(Collectors.toMap(m -> m.getName(), m -> m, (k1,k2) -> k1));

            for (Field field : declaredFields) {
                String name = field.getName();
                String substring = name.substring(1);
                String methodName = "set" + String.valueOf(name.charAt(0)).toUpperCase() + substring;
                Method method = methodMap.get(methodName);
                Class genericType = field.getType();
                if(Objects.nonNull(method)){

                    FIELD_SETMETHOD_MAP.compute(clazz, (k,v) ->{
                        if(v == null){
                            v = Maps.newHashMap();
                        }
                        v.put(name, method);
                        return v;
                    });
                }
            }
        }
        return FIELD_SETMETHOD_MAP.get(clazz);
    }


    public static <T> T createObj(Class<T> clazz, List data, List<String> fields) throws Exception{

        Map<String, Method> setMethodMapByClazz = getSetMethodMapByClazz(clazz);
        Map<String, Class> filedTypeMapByClazz = getFiledTypeMapByClazz(clazz);
        T t = clazz.newInstance();

        String fieldName = null;
        for (int i = 0; i < fields.size(); i++) {
            fieldName = fields.get(i);
            String fieldName2HeadName = getHeadFieldInverseMap(clazz).get(fieldName);
            Object obj = data.get(i);
            try {
                if (fieldName.contains("_")) {
                    ValueStrategy valueStrategyByKey = ConvertRegistry.getValueStrategyByKey(fieldName);
                    if (valueStrategyByKey != null) {
                        fieldName = fieldName.split("_")[0];
                        obj = valueStrategyByKey.convert(obj);
                    }
                }
                Class type = filedTypeMapByClazz.get(fieldName);
                Method method = setMethodMapByClazz.get(fieldName);
                method.invoke(t, type.cast(obj));
            }catch (Exception e){
                String msg = "[" + fieldName2HeadName + "]字段:";
                if(e instanceof ConvertException){
                    msg = msg + ((ConvertException)e).getMessage();
                }else{
                    msg = msg + "转换错误!";
                }
                log.info(msg, e);
                throw new ConvertException(msg);
            }
        }
        return t;
    }


    private static Map<String, Class> getFiledTypeMapByClazz(Class clazz){
        if(MapUtils.isEmpty(FIELD_TYPE_MAP.get(clazz))){
            Field[] declaredFields = clazz.getDeclaredFields();
            Method[] methods = clazz.getMethods();
            Map<String, Method> methodMap = Stream.of(methods).collect(Collectors.toMap(m -> m.getName(), m -> m, (k1,k2) -> k1));

            for (Field field : declaredFields) {
                String name = field.getName();
                Class genericType = field.getType();
                FIELD_TYPE_MAP.compute(clazz, (k,v) -> {

                    if(v == null){
                        v = Maps.newHashMap();
                    }
                    v.put(name, genericType);
                    return v;
                });
            }
        }
        return FIELD_TYPE_MAP.get(clazz);
    }

    private static Map<String, Method> getGetMethodMapByClazz(Class clazz){
        if(MapUtils.isEmpty(FIELD_GETMETHOD_MAP.get(clazz))){
            Field[] declaredFields = clazz.getDeclaredFields();
            Method[] methods = clazz.getMethods();
            Map<String, Method> methodMap = Stream.of(methods).collect(Collectors.toMap(m -> m.getName(), m -> m, (k1,k2) -> k1));

            for (Field field : declaredFields) {
                String name = field.getName();
                String substring = name.substring(1);
                String methodName = "get" + String.valueOf(name.charAt(0)).toUpperCase() + substring;
                Method method = methodMap.get(methodName);
                Class genericType = field.getType();
                if(Objects.nonNull(method)){

                    FIELD_GETMETHOD_MAP.compute(clazz, (k,v) -> {

                        if(v == null){
                            v = Maps.newHashMap();
                        }
                        v.put(name, method);
                        return v;
                    });

                }
            }
        }
        return FIELD_GETMETHOD_MAP.get(clazz);
    }


    public static <T> SXSSFWorkbook createExcelByList(List<T> data,List<List<Object>> errorConvertData, Class clazz, String sheetName){

        Map<String, Method> getMethodMapByClazz = getGetMethodMapByClazz(clazz);
        LinkedHashMap<String, String> headMap = getHeadFieldMap(clazz);
        Set<String> keySet = headMap.keySet();

        SXSSFWorkbook workbook = new SXSSFWorkbook(1000000);
        SXSSFSheet indexSheet = workbook.createSheet(sheetName);

        SXSSFRow firstErrorRow = indexSheet.createRow(0);

        List<String> keyList = Lists.newArrayList(keySet);
        for (int i = 0; i < keySet.size(); i++) {
            firstErrorRow.createCell(i).setCellValue(keyList.get(i));
        }

        int rownum=1;
        for (int i = 0; i < data.size(); i++) {
            T errorObj = data.get(i);
            SXSSFRow row = indexSheet.createRow(rownum++);
            for (int j = 0; j < keySet.size(); j++) {
                String k = keyList.get(j);
                String fieldName = headMap.get(k);
                String beforeFieldName = fieldName;
                if(fieldName.contains("_")){
                    fieldName = fieldName.split("_")[0];
                }
                Method method = getMethodMapByClazz.get(fieldName);
                if(method == null){
                    continue;
                }

                Object invoke = null;
                try {
                    invoke = method.invoke(errorObj);
                } catch (Exception e) {
                    log.info("写入excel异常", e);
                }
                if(invoke == null){
                    invoke = "";
                }

                if(invoke instanceof Date){
                    invoke = DateUtil.parseDateToStr(((Date) invoke),"yyyy-MM-dd");
                }

                row.createCell(j).setCellValue(invoke.toString());
            }
        }

        for (int i = 0; i < errorConvertData.size(); i++) {
            List<Object> errorObj = errorConvertData.get(i);
            SXSSFRow row = indexSheet.createRow(rownum++);
            for (int j = 0; j < keySet.size(); j++) {
                Object obj=errorObj.get(j);
                if(obj==null){
                    obj="";
                }
                row.createCell(j).setCellValue(obj+"");
            }
        }
        return workbook;
    }
}
