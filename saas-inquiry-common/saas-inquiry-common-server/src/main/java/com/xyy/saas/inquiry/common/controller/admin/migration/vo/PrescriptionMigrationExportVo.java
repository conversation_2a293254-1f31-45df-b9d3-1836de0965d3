package com.xyy.saas.inquiry.common.controller.admin.migration.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import java.io.Serializable;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2019/09/11
 * @Describe
 */
@Data
@Slf4j
@ExcelIgnoreUnannotated
public class PrescriptionMigrationExportVo implements Serializable {

    @ExcelProperty(value = "处方号")
    private String pref;

    @ExcelProperty(value = "处方医生")
    private String physicianName;

    @ExcelProperty(value = "患者姓名")
    private String patientName;

    @ExcelProperty(value = "开方时间")
    private String outPrescriptionTime;

    @ExcelProperty(value = "医院审核药师")
    private String hosPharmacistName;

    @ExcelProperty(value = "医院药师审核时间")
    private String hosApprovalTime;

    @ExcelProperty(value = "审核状态")
    private String auditStatusStr;

    @ExcelProperty(value = "处方来源")
    private String sourceInfo;

    @ExcelProperty(value = "处方类型")
    private String typeStr;

    @ExcelProperty(value = "药品类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.PRESCRIPTION_MEDICINE_TYPE)
    private Integer medicineType;

    @ExcelProperty(value = "提交门店")
    private String drugstoreName;

    @ExcelProperty(value = "商品名")
    private String medicinesName;

    @ExcelProperty(value = "通用名")
    private String productName;

    @ExcelProperty(value = "规格")
    private String attributeSpecification;

    @ExcelProperty(value = "厂家")
    private String manufacturer;

    @ExcelProperty(value = "数量")
    private String quantity;

    @ExcelProperty(value = "单次剂量")
    private String singleDose;

    @ExcelProperty(value = "计量单位")
    private String singleUnit;

    @ExcelProperty(value = "频次")
    private String useFrequency;

    @ExcelProperty(value = "用法")
    private String directions;

    @ExcelProperty(value = "副数(中药)")
    private String tcmTotal;

    @ExcelProperty(value = "用法(中药)")
    private String tcmUsage;

    @ExcelProperty(value = "加工方式(中药)")
    private String tcmProcessingMethod;

    /**
     * 门店导出字段
     */
    private String patientAge;

    private String patientSex;

    private String telephone;

    private String pharmacistName;

    private String statusStr;
}