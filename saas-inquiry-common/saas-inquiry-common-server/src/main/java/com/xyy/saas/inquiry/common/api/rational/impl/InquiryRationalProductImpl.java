package com.xyy.saas.inquiry.common.api.rational.impl;

import static java.util.stream.Collectors.toList;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils;
import com.alibaba.fastjson2.JSON;
import com.xyy.saas.inquiry.common.api.rational.InquiryCompatibilitySearchApi;
import com.xyy.saas.inquiry.common.api.rational.InquiryDiagnosiscSearchApi;
import com.xyy.saas.inquiry.common.api.rational.InquiryProductRuleSearchApi;
import com.xyy.saas.inquiry.common.api.rational.InquiryRationalCompatibilityApi;
import com.xyy.saas.inquiry.common.api.rational.InquiryRationalDiagnosisApi;
import com.xyy.saas.inquiry.common.api.rational.InquiryRationalProductApi;
import com.xyy.saas.inquiry.common.api.rational.dto.DrugDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryCompatibilityDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryCompatibilitySearchParam;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryDiagnosiscDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryDiagnosiscDtoSearchParam;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryLimitAuditDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryProductResponseDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryProductRuleDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalCategoryDetailDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalDictConfigDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalProductCommonDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalProductDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalProductRuleDto;
import com.xyy.saas.inquiry.common.api.rational.dto.QueryParamDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryProductDetailVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryProductRuleDtoSearchParam;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRationalAgeRangeVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRationalCompatibilityVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRationalDiagnosisVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRationalProductImportVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRationalProductQueryVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.InquiryRationalProductVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.RationalCompatibilityVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.RationalDiagnosisVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.RationalProductVO;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasCategoryTreeVo;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasCategoryVo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalCategoryDetail;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalDictConfig;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalProduct;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalProductRule;
import com.xyy.saas.inquiry.common.handler.rational.DrugInfo;
import com.xyy.saas.inquiry.common.handler.rational.PatientInfo;
import com.xyy.saas.inquiry.common.handler.rational.RationalService;
import com.xyy.saas.inquiry.pojo.RationalTipsVO;
import com.xyy.saas.inquiry.common.handler.rational.strategy.paramAnalysis.ParamAnalysisStrategy;
import com.xyy.saas.inquiry.common.service.productmid.InquiryMidCategoryApiImpl;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalCategoryDetailService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalCompatibilityDetailService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalDiagnosisService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalDictConfigService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalProductRuleService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalProductService;
import com.xyy.saas.inquiry.common.util.DateUtil;
import com.xyy.saas.inquiry.common.util.RationalAgeRangeUtil;
import com.xyy.saas.inquiry.common.util.StringUtil;
import com.xyy.saas.inquiry.enums.rational.CautionLevel;
import com.xyy.saas.inquiry.enums.rational.InquiryProductRuleConstant;
import com.xyy.saas.inquiry.enums.rational.LimitTypeEnum;
import com.xyy.saas.inquiry.enums.rational.RationalDictType;
import com.xyy.saas.inquiry.pojo.excel.ImportReqDto;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import com.xyy.saas.inquiry.util.excel.EasyExcelUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;


@Slf4j
@Service
public class InquiryRationalProductImpl implements InquiryRationalProductApi {

    @Resource
    private List<ParamAnalysisStrategy> strategies;

    @Resource
    private InquiryRationalProductService productService;

    @Resource
    private InquiryRationalProductRuleService productRuleService;

    @Resource
    private InquiryRationalDictConfigService dictConfigService;

    @Resource
    private InquiryRationalDiagnosisService inquiryRationalDiagnosisService;

    @Resource
    private RationalService rationalService;

    @Resource
    private InquiryRationalCompatibilityDetailService inquiryRationalCompatibilityDetailService;

    @Resource
    private InquiryMidCategoryApiImpl inquiryMidCategoryApi;

    @Resource
    private InquiryRationalDiagnosisApi inquiryRationalDiagnosisApi;

    @Resource
    private InquiryRationalCompatibilityApi inquiryRationalCompatibilityApi;

    @Resource(name = "inquiryProductRuleSearchImpl")
    private InquiryProductRuleSearchApi inquiryProductRuleSearchApi;

    @Resource
    private InquiryDiagnosiscSearchApi inquiryDiagnosiscSearchApi;

    @Resource
    private InquiryCompatibilitySearchApi inquiryCompatibilitySearchApi;

    @Resource
    private InquiryMidCategoryApiImpl midCategoryApi;

    @Resource
    private InquiryRationalCategoryDetailService inquiryRationalCategoryDetailService;

    @Resource
    private InquiryRationalDictConfigService inquiryRationalDictConfigService;

    @Value("${limit.audit.organSigns:}")
    private String auditOrganSign;

    @Value("${limit.notAudit.organSigns:}")
    private String notAuditOrganSign;

    @Autowired
    private EasyExcelUtil easyExcelUtil;

    /**
     * 限制类药品 - 新增或者编辑
     *
     * @param productDto 限制类药品
     * @return void
     */
    @Override
    public CommonResult<?> limitDrugCreateOrUpdate(RationalProductVO productDto) {
        CommonResult checkReult = checkParam(productDto);
        if (!checkReult.isSuccess()) {
            return checkReult;
        }

        // 校验分类 必须四级以下
        if (!midCategoryApi.checkCategoryLevelLt(StringUtil.convertStr(productDto.getCategoryLv6Id()), 4)) {
            return CommonResult.error("操作失败,分类选择异常(分类无效或不在四级以下!)");
        }

        InquiryRationalProductCommonDto queryParam = InquiryRationalProductCommonDto.builder().commonNames(Collections.singletonList(productDto.getCommonName())).build();
        List<InquiryRationalProduct> products = productService.queryByCondition(queryParam);
        List<InquiryRationalProductRuleDto> ruleDtos;
        InquiryRationalProductDto dto = BeanUtil.copyProperties(productDto, InquiryRationalProductDto.class);

        // 新增
        if (productDto.getId() == null) {
            if (CollectionUtils.isNotEmpty(products)) {
                return CommonResult.error("通用名" + productDto.getCommonName() + "已存在，请勿重复设置");
            }

            ruleDtos = new ArrayList<>(execute(dto));
            productDto.setCreateUser(LoginUserContextUtils.getLoginUserId().toString());
            return productService.saveProduct(dto, ruleDtos);
        }

        // 修改 校验重复
        if (!Objects.equals(products.get(0).getId(), productDto.getId())) {
            return CommonResult.error("编辑失败,该商品已存在!");
        }

        ruleDtos = new ArrayList<>(execute(dto));

        productService.updateProduct(dto, ruleDtos);
        return CommonResult.success(null);
    }


    CommonResult checkParam(RationalProductVO param) {
        if (StringUtils.isBlank(param.getCommonName())) {
            CommonResult.error("限制类药品通用名不能为空");
        }
        if (StringUtils.isNotBlank(param.getCategoryLv6Id()) && !StringUtils.isNumeric(param.getCategoryLv6Id())) {
            CommonResult.error("限制类药品六级分类有误,请检查");
        }
        if (param.getMedicareRemark() != null && StringUtils.isBlank(param.getMedicareRemark())) {
            param.setMedicareRemark("");
        }
        return CommonResult.success(null);
    }

    // 执行所有策略
    public List<InquiryRationalProductRuleDto> execute(InquiryRationalProductDto productDto) {
        List<InquiryRationalProductRuleDto> result = new ArrayList<>();
        for (ParamAnalysisStrategy strategy : strategies) {
            List<InquiryRationalProductRuleDto> res = strategy.execute(productDto);
            if (CollectionUtils.isNotEmpty(res)) {
                result.addAll(res);
            }
        }
        return result;
    }


    @Override
    public PageResult<InquiryRationalProductVo> querySelector(InquiryRationalProductCommonDto commonDto) {
        PageResult<InquiryRationalProduct> result = productService.pageQueryByCondition(commonDto);
        return new PageResult<>(BeanUtil.copyToList(result.getList(), InquiryRationalProductVo.class), result.getTotal());
    }

    /**
     * 合理用药审核接口
     *
     * @param productDto
     * @return
     */
    @Override
    public CommonResult<List<RationalTipsVO>> limitDrugAudit(InquiryLimitAuditDto productDto) {
       log.info("合理用药审核参数："+ JSON.toJSONString(productDto));
        try {
            PatientInfo patientInfo = new PatientInfo();
            BeanUtils.copyProperties(productDto,patientInfo);
            List<DrugDto> drugDtoList = productDto.getDrugDtoList();
            List<String> drugs = productDto.getDrugs();
            List<DrugInfo> drugInfos = new ArrayList<>();
            List<String> diagnosisCodes = productDto.getDiagnosisCodes();
            List<String> diagnosisNames = productDto.getDiagnosisNames();
            if(CollectionUtils.isNotEmpty(drugDtoList)){
                drugInfos = com.xyy.saas.inquiry.common.util.BeanUtil.copyListProperties(drugDtoList, DrugInfo.class);
            }
            if (CollectionUtils.isEmpty(drugInfos) && CollectionUtils.isNotEmpty(productDto.getDrugs())) {
                drugInfos = drugs.stream().map(item -> {
                    DrugInfo drugInfo = new DrugInfo();
                    drugInfo.setCommonName(item);
                    return drugInfo;
                }).collect(toList());
            }
            if (CollectionUtils.isEmpty(drugInfos)) {
                return CommonResult.success(null);
            }

            if(StringUtils.isBlank(productDto.getOrganSign())){
                return CommonResult.success(null);
            }

            List<DrugInfo> finalDrugInfos = drugInfos;
            List<RationalTipsVO> tipsVos = rationalService.auditingDrug(patientInfo, finalDrugInfos,diagnosisCodes,diagnosisNames,productDto.getOrganSign());
            if(CollectionUtils.isEmpty(tipsVos)){
                //适配前端空数据时返回null
                tipsVos = null;
            }
            return CommonResult.success(tipsVos);
        }catch (Exception e){
            log.error("合理用药审核接口异常,入参:{}", JSON.toJSONString(productDto),e);
        }
        return CommonResult.success(null);

    }


    /**
     * 是否需要合理用药审核
     *
     * @param productDto
     * @return true 需要   false  不需要
     */
    private Boolean auditChek(InquiryLimitAuditDto productDto) {
        // 机构号为空，不审核
        if (StringUtils.isBlank(productDto.getOrganSign())) {
            log.info("获取到机构号为空,不走合理用药审核");
            return false;
        }
        // 不审核机构池包含此机构号，不审核
        if (StringUtils.isNotBlank(notAuditOrganSign) && notAuditOrganSign.contains(productDto.getOrganSign())) {
            log.info("当前机构号：{}在不审核池,不走合理用药审核" + productDto.getOrganSign());
            return false;
        }
        // 审核机构池包含此机构号，走审核逻辑
        if (StringUtils.isNotBlank(auditOrganSign) && auditOrganSign.contains(productDto.getOrganSign())) {
            log.info("当前机构号：{}在审核池,进行合理用药审核" + productDto.getOrganSign());
            return true;
        }
        log.info("当前机构号：{}不满足灰度条件,不走合理用药审核" + productDto.getOrganSign());
        return false;
    }

    @Override
    public CommonResult<InquiryProductDetailVo> queryLimitDrugDetail(InquiryRationalProductQueryVO productDto) {
        InquiryProductDetailVo result = new InquiryProductDetailVo();
        InquiryRationalProduct product = productService.queryById(productDto.getProductCommonId());
        if (product == null) {
            return CommonResult.error("未查询到相关信息");
        }
        result.setCommonName(product.getCommonName());
        result.setMedicareRemark(product.getMedicareRemark());
        if (StringUtils.isNotBlank(product.getCategoryLv6Id())) {
            Integer lv6Id = StringUtil.convertStr(product.getCategoryLv6Id());
            Map<Integer, List<SaasCategoryVo>> map = midCategoryApi.queryCategoryParentPathByIds(Collections.singletonList(lv6Id));
            Optional.ofNullable(map).ifPresent(p -> result.setCategoryLv6Id(map.get(lv6Id)));

            // 查详情或者编辑的时候 填充六级分类树结构
            if (CollectionUtils.isNotEmpty(map.get(lv6Id)) && (productDto.getQueryType() == null || productDto.getQueryType() == 0)) {
                SaasCategoryTreeVo categoryTreeVo = midCategoryApi.queryCategoryBuildTree(map.get(lv6Id).stream().sorted(Comparator.comparing(SaasCategoryVo::getLevel)).map(SaasCategoryVo::getId).collect(toList()));
                result.setCategoryIds(categoryTreeVo.getCategoryIds());
                result.setOptions(categoryTreeVo.getOptions());
            }
        }
        result.setStatus(product.getStatus());
        // 查询商品规则信息
        List<InquiryRationalProductRule> rules = productRuleService.queryByCommonId(productDto.getProductCommonId());
        // 解析并设置参数
        rationalService.setDetailParam(result, rules);

        // 查询诊断禁忌分页信息
        if (productDto.getQueryType() == 0 || productDto.getQueryType() == 1) {
            RationalDiagnosisVO diagnosisDto = RationalDiagnosisVO.builder().commonName(product.getCommonName()).build();
            diagnosisDto.setPageNo(productDto.getPageNo());
            diagnosisDto.setPageSize(productDto.getPageSize());
            diagnosisDto.setQueryParentCategory(true);
            PageResult<InquiryRationalDiagnosisVo> diagnosisVoPageInfo = inquiryRationalDiagnosisApi.queryRationalDiagnosis(diagnosisDto);
            result.setDiagnosisPageInfo(diagnosisVoPageInfo);
        }
        // 查询配伍禁忌分页信息
        if (productDto.getQueryType() == 0 || productDto.getQueryType() == 2) {
            RationalCompatibilityVO compatibilityDto = RationalCompatibilityVO.builder().commonName(product.getCommonName()).searchType(1).build();
            compatibilityDto.setPageNo(productDto.getPageNo());
            compatibilityDto.setPageSize(productDto.getPageSize());
            compatibilityDto.setQueryParentCategory(true);
            PageResult<InquiryRationalCompatibilityVo> compatibility = inquiryRationalCompatibilityApi.queryRationalCompatibility(compatibilityDto);
            result.setCompatibilityPageInfo(compatibility);
        }
        return CommonResult.success(result);
    }

//    @PostConstruct
//    public void init(){
//        String a = "{\n" +
//                "  \"day1\": 0,\n" +
//                "  \"day2\": \"天\",\n" +
//                "  \"day3\": 1,\n" +
//                "  \"day4\": \"岁\",\n" +
//                "  \"commonName\": \"官桂\",\n" +
//                "  \"categoryLv6Id\": \"\",\n" +
//                "  \"sexLimit\": null,\n" +
//                "  \"status\": null,\n" +
//                "  \"ageLimits\": null,\n" +
//                "  \"ageRangeLimit\": null,\n" +
//                "  \"healthLimit\": null,\n" +
//                "  \"womenLimit\": null,\n" +
//                "  \"age\": \"0\",\n" +
//                "  \"ingredientQueryType\": null,\n" +
//                "  \"diagnosisQueryType\": null,\n" +
//                "  \"compatibilityQueryType\": null,\n" +
//                "  \"medicareRemarkQueryType\": null,\n" +
//                "  \"ingredientList\": [],\n" +
//                "  \"diagnosisList\": [],\n" +
//                "  \"compatibilityCommonList\": [],\n" +
//                "  \"pageNum\": 1,\n" +
//                "  \"pageSize\": 50\n" +
//                "}";
//        final InquiryRationalProductQueryDto queryDto = JSON.parseObject(a, InquiryRationalProductQueryDto.class);
//        pageQueryLimitDrug(queryDto);
//
//    }

    /**
     * 分页查询限制类药品
     *
     * @param queryDto
     * @return
     */
    @Override
    public CommonResult<?> pageQueryLimitDrug(InquiryRationalProductQueryVO queryDto) {
        // 如果诊断禁忌 或 配伍禁忌不为空，则优先查询
        QueryParamDto resultDto = null;
        // 校验查询输入参数
        CommonResult checkResult = checkQueryParam(queryDto);
        if (!ObjectUtils.isEmpty(checkResult)) {
            return checkResult;
        }
        if (!CollectionUtils.isEmpty(queryDto.getDiagnosisList()) || !CollectionUtils.isEmpty(queryDto.getCompatibilityCommonList())) {
            resultDto = getQueryParam(queryDto);
        }
        queryDto.setQueryParams(resultDto);
        // 查询ES
        CommonResult<PageResult<InquiryProductResponseDto>> apiResult = inquiryProductRuleSearchApi.pageSearchInquiryProductRuleAgg(paramHandle(queryDto));
        if (ObjectUtils.isEmpty(apiResult) || ObjectUtils.isEmpty(apiResult.getData())) {
            return null;
        }
        List<InquiryProductResponseDto> recordList = apiResult.getData().getList();
        // 组装数据
        assembly(recordList);
        PageResult pageInfo = new PageResult();
        pageInfo.setTotal(apiResult.getData().getTotal());
        pageInfo.setList(recordList);
        return CommonResult.success(pageInfo);
    }

    private CommonResult checkQueryParam(InquiryRationalProductQueryVO queryDto) {
        // 年龄区间不为空
        if (StringUtils.isNotBlank(queryDto.getAgeRangeLimit())) {
            InquiryRationalAgeRangeVo ageRangeVo = RationalAgeRangeUtil.convertStr(queryDto.getAgeRangeLimit());
            if (!ObjectUtils.isEmpty(ageRangeVo) && ageRangeVo.getEndDay() < ageRangeVo.getStartDay()) {
                return CommonResult.error("年龄区间设置异常");
            }
        }
        return null;
    }

    private void assembly(List<InquiryProductResponseDto> recordList) {
        // 根据商品id查询所有商品信息
        InquiryProductRuleDtoSearchParam productQueryParam = new InquiryProductRuleDtoSearchParam();
        // 获取所有商品id
        List<Integer> prodIds = recordList.stream().map(InquiryProductResponseDto::getProductCommonId).collect(Collectors.toList());
        InquiryRationalProductCommonDto quer = new InquiryRationalProductCommonDto();
        quer.setIds(prodIds);
        List<InquiryRationalProduct> products = productService.queryByCondition(quer);
        //<商品id,商品信息>
        Map<Integer, InquiryRationalProduct> prodMap = products.stream().collect(Collectors.toMap(InquiryRationalProduct::getId, obj -> obj, (k1, k2) -> k2));
        // 商品通用名集合
        List<String> commonNames = products.stream().map(InquiryRationalProduct::getCommonName).collect(toList());
        // 查询商品基本规则信息
        productQueryParam.setProductCommonIds(prodIds);
        productQueryParam.setPageSize(10000);
        CommonResult<PageResult<InquiryProductRuleDto>> apiResult = inquiryProductRuleSearchApi.pageSearchInquiryProductRuleCommon(productQueryParam);
        if (ObjectUtils.isEmpty(apiResult) || ObjectUtils.isEmpty(apiResult.getData())) {
            return;
        }
        // 商品基础规则信息
        List<InquiryProductRuleDto> ruleDtos = apiResult.getData().getList();
        // 查询所有商品所有六级分类链路信息
        List<Integer> lv6IdList = products.stream().filter(p -> StringUtils.isNotBlank(p.getCategoryLv6Id())).map(InquiryRationalProduct::getCategoryLv6Id).distinct().map(Integer::parseInt).collect(toList());
        // 获取六级分类信息 所有下级6级分类节点集合
//        Map<Integer, List<SaasCategoryVo>> lv6Map = midCategoryApi.queryCategoryParentPathByIds(lv6IdList);
        // 获取当前所有商品的所有6级分类的链路信息
        Map<Integer, List<SaasCategoryVo>> lv6ParentMap = midCategoryApi.queryCategoryParentPathByIds(lv6IdList);
        // 先填充商品基本信息
        recordList.forEach(obj -> {
            InquiryRationalProduct product = prodMap.get(obj.getProductCommonId());
            Optional.ofNullable(product).ifPresent(p -> {
                obj.setCommonName(product.getCommonName());
                obj.setStatus(product.getStatus().byteValue());
                obj.setUpdateUser(product.getCreator());
                obj.setUpdateTime(DateUtil.parseDateToStr(product.getUpdateTime(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS, ""));
                obj.setCategoryLv6Id(product.getCategoryLv6Id());
                obj.setMedicareRemark(product.getMedicareRemark());
                if (StringUtils.isNotBlank(product.getCategoryLv6Id())) {
                    List<SaasCategoryVo> saasCategoryVos = lv6ParentMap.get(StringUtil.convertStr(product.getCategoryLv6Id()));
                    Optional.ofNullable(saasCategoryVos).ifPresent(list -> obj.setCategoryLv6Str(list.stream().map(SaasCategoryVo::getTitle).collect(Collectors.joining(">"))));
                }
                obj.setCreateTime(DateUtil.parseDateToStr(product.getCreateTime(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS, ""));
                obj.setCategoryLv6Id(product.getCategoryLv6Id());
                obj.setYn(product.getYn().byteValue());
                obj.setCreateUser(product.getCreator());
            });
        });
        // 所有六级分类
        Set<Integer> lV6Ids = new HashSet<>();
        lv6ParentMap.forEach((key, value) -> {
            lV6Ids.add(key);
            value.forEach(vo -> lV6Ids.add(vo.getId()));
            List<SaasCategoryVo> parentValue = lv6ParentMap.get(key);
            parentValue.forEach(vo -> lV6Ids.add(vo.getId()));
        });
        //<productCommonName,"3,12,79,143,241,336">
        Map<String, String> prodLv6Map = new HashMap<>();
        products.forEach(product -> {
            List<SaasCategoryVo> categoryVos = StringUtils.isBlank(product.getCategoryLv6Id()) ? new ArrayList<>() : lv6ParentMap.get(Integer.valueOf(product.getCategoryLv6Id()));
            if (CollectionUtils.isEmpty(categoryVos)) {
                return;
            }
            String ids = categoryVos.stream().map(SaasCategoryVo::getId).map(String::valueOf).collect(Collectors.joining(","));
            prodLv6Map.put(product.getCommonName(), ids);
        });
        // 查询所有关联的分类集合
        List<InquiryRationalCategoryDetail> categoryDetails = inquiryRationalCategoryDetailService.queryByCondition(InquiryRationalCategoryDetailDto.builder().classifyIds(new ArrayList<>(lV6Ids)).build());
        Map<Integer, List<InquiryRationalCategoryDetail>> categoryDetailMap = categoryDetails.stream().collect(Collectors.groupingBy(InquiryRationalCategoryDetail::getCategoryId));
        List<Integer> categorys = categoryDetails.stream().map(InquiryRationalCategoryDetail::getCategoryId).distinct().collect(toList());
        List<InquiryRationalDictConfig> cateDictList = inquiryRationalDictConfigService.queryByCondition(InquiryRationalDictConfigDto.builder().ids(categorys).build());
        //<类别id,类别名称>
        Map<Integer, String> cateMapping = cateDictList.stream().collect(Collectors.toMap(InquiryRationalDictConfig::getId, InquiryRationalDictConfig::getName, (k1, k2) -> k2));
        //<类别id,关联6级分类集合>
        Map<Integer, String> cateMap = new HashMap<>();
        categoryDetailMap.forEach((k, v) -> {
            cateMap.put(k, v.stream().map(InquiryRationalCategoryDetail::getClassifyId).map(String::valueOf).collect(Collectors.joining(",")));
        });
        InquiryDiagnosiscDtoSearchParam diagnosiscDtoSearchParam = new InquiryDiagnosiscDtoSearchParam();
        diagnosiscDtoSearchParam.setCategories(categorys);
        diagnosiscDtoSearchParam.setCommonProducts(commonNames);
        CommonResult<PageResult<InquiryDiagnosiscDto>> diagRes = inquiryDiagnosiscSearchApi.pageSearchInquiryDiagnosisc(diagnosiscDtoSearchParam);
        // 商品诊断禁忌信息
        List<InquiryDiagnosiscDto> diagnosiscDtos = new ArrayList<>();
        if (!ObjectUtils.isEmpty(diagRes) && !ObjectUtils.isEmpty(diagRes.getData()) && !CollectionUtils.isEmpty(diagRes.getData().getList())) {
            diagnosiscDtos = diagRes.getData().getList();
        }
        InquiryCompatibilitySearchParam compatibilitySearchParam = new InquiryCompatibilitySearchParam();
        compatibilitySearchParam.setSearchType(1);
        compatibilitySearchParam.setCategorys(categorys);
        compatibilitySearchParam.setCommonNames(commonNames);
        CommonResult<PageResult<InquiryCompatibilityDto>> compRes = inquiryCompatibilitySearchApi.pageSearchInquiryCompatibility(compatibilitySearchParam);
        // 商品配伍禁忌信息
        List<InquiryCompatibilityDto> compaDtos = new ArrayList<>();
        if (!ObjectUtils.isEmpty(compRes) && !ObjectUtils.isEmpty(compRes.getData()) && !CollectionUtils.isEmpty(compRes.getData().getList())) {
            compaDtos = compRes.getData().getList();
        }
        fillParam(recordList, ruleDtos, diagnosiscDtos, compaDtos, prodLv6Map, cateMap, cateMapping);
    }

    /**
     * 填充返回结果
     *
     * @param recordList
     * @param ruleDtos
     * @param diagnosiscDtos
     * @param compaDtos
     */
    private void fillParam(List<InquiryProductResponseDto> recordList, List<InquiryProductRuleDto> ruleDtos, List<InquiryDiagnosiscDto> diagnosiscDtos,
        List<InquiryCompatibilityDto> compaDtos, Map<String, String> lv6Map, Map<Integer, String> cateMap, Map<Integer, String> cateMapping) {
        // 查询字典信息
        InquiryRationalDictConfigDto dictConfigDto = new InquiryRationalDictConfigDto();
        
        List<InquiryRationalDictConfig> ageDict = new ArrayList<>();
        List<InquiryRationalDictConfig> idtegDict = new ArrayList<>();
        
        // 检查ruleDtos是否为null或空
        if (CollectionUtils.isNotEmpty(ruleDtos)) {
            // 获取所有年龄字典
            List<Integer> ageDictIds = ruleDtos.stream().filter(rule -> LimitTypeEnum.AGE_LIMIT.getType() == rule.getLimitDrugType()).map(InquiryProductRuleDto::getParam).collect(toList()).stream().map(Integer::parseInt).collect(toList());
            dictConfigDto.setIds(ageDictIds);
            dictConfigDto.setType(RationalDictType.AGE_GROUP.getType());
            ageDict = dictConfigService.queryByCondition(dictConfigDto);

            // 获取所有成分字典
            List<Integer> idtegDictIds = ruleDtos.stream().filter(rule -> LimitTypeEnum.INGREDIENT_LIMIT.getType() == rule.getLimitDrugType()).map(InquiryProductRuleDto::getParam).collect(toList()).stream().map(Integer::parseInt)
                .collect(toList());
            dictConfigDto.setIds(idtegDictIds);
            dictConfigDto.setType(RationalDictType.COMPONENT.getType());
            idtegDict = dictConfigService.queryByCondition(dictConfigDto);
        }

        if (CollectionUtils.isNotEmpty(compaDtos)) {
            // 查询配伍禁忌中所有的类别id
            List<Integer> cas = compaDtos.stream().filter(a -> NumberUtils.toInt(a.getFirstParam()) != 0).map(a -> NumberUtils.toInt(a.getFirstParam())).collect(toList());
            List<Integer> cbs = compaDtos.stream().filter(a -> NumberUtils.toInt(a.getSecondParam()) != 0).map(a -> NumberUtils.toInt(a.getSecondParam())).collect(toList());
            cas.addAll(cbs);
            if (CollectionUtils.isNotEmpty(cas)) {
                List<InquiryRationalDictConfig> cateDictList = inquiryRationalDictConfigService.queryByCondition(InquiryRationalDictConfigDto.builder().ids(cas.stream().distinct().collect(toList())).build());
                //<类别id,类别名称>
                Map<Integer, String> cateMapping1 = cateDictList.stream().collect(Collectors.toMap(InquiryRationalDictConfig::getId, InquiryRationalDictConfig::getName, (k1, k2) -> k2));
                cateMapping.putAll(cateMapping1);
            }
        }

        Map<Integer, List<InquiryProductRuleDto>> ruleMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ruleDtos)) {
            ruleMap = ruleDtos.stream().collect(Collectors.groupingBy(InquiryProductRuleDto::getProductCommonId));
        }
        for(InquiryProductResponseDto record : recordList){
            // 填充基础规则信息
            List<InquiryProductRuleDto> ruleDtoList = ruleMap.get(record.getProductCommonId());
            if (CollectionUtils.isEmpty(ruleDtoList)) {
                return;
            }
            fillBaseRule(record, ruleDtoList, ageDict, idtegDict);
            // 填充诊断禁忌
            fillDiagnosisInfo(record, diagnosiscDtos, lv6Map, cateMap);
            // 填充配伍禁忌
            fillCompatibilityInfo(record, compaDtos, lv6Map, cateMap, cateMapping);
        }
    }

    private void fillCompatibilityInfo(InquiryProductResponseDto record, List<InquiryCompatibilityDto> compaDtos, Map<String, String> lv6Map, Map<Integer, String> cateMap, Map<Integer, String> cateMapping) {
        Set<Integer> cateSet = getCurrProductCateSetByLv6(lv6Map.get(record.getCommonName()), cateMap);
        StringBuilder builder = new StringBuilder();
        compaDtos.forEach(obj -> {
            // 一级类别匹配
            if (InquiryProductRuleConstant.FIRST_TYPE_CATEGORY.equals(obj.getFirstType()) && cateSet.contains(NumberUtils.toInt(obj.getFirstParam()))) {
                if (InquiryProductRuleConstant.FIRST_TYPE_CATEGORY.equals(obj.getSecondType())) { // 二级是分类
                    builder.append(cateMapping.getOrDefault(Integer.valueOf(obj.getSecondParam()), "")).append(",").append(CautionLevel.getTipsNameByType(obj.getCaution())).append(";");
                }
                if (InquiryProductRuleConstant.FIRST_TYPE_COMMON_NAME.equals(obj.getSecondType())) { // 二级是商品
                    builder.append(obj.getSecondParam()).append(",").append(CautionLevel.getTipsNameByType(obj.getCaution())).append(";");
                }
            }
            // 一级通用名匹配
            if (InquiryProductRuleConstant.FIRST_TYPE_COMMON_NAME.equals(obj.getFirstType()) && StringUtils.equals(record.getCommonName(), obj.getFirstParam())) {
                if (InquiryProductRuleConstant.FIRST_TYPE_CATEGORY.equals(obj.getSecondType()) && cateSet.contains(NumberUtils.toInt(obj.getSecondParam()))) { // 二级是分类
                    builder.append(cateMapping.getOrDefault(Integer.valueOf(obj.getSecondParam()), "")).append(",").append(CautionLevel.getTipsNameByType(obj.getCaution())).append(";");
                } else { // 二级是商品
                    builder.append(obj.getSecondParam()).append(",").append(CautionLevel.getTipsNameByType(obj.getCaution())).append(";");
                }
            }
        });
        record.setCompatibilityLimitStr(builder.toString());
    }

    public Set<Integer> getCurrProductCateSetByLv6(String lv6Str, Map<Integer, String> cateMap) {
        Set<Integer> result = new HashSet<>();
        if (StringUtils.isNotBlank(lv6Str)) {
            // 查找当前药品六级分类关联的类别
            Arrays.asList(lv6Str.split(",")).forEach(str -> {
                cateMap.forEach((k, v) -> {
                    Arrays.asList(v.split(",")).stream().forEach(cate -> {
                        if (StringUtils.equals(str, cate)) {
                            result.add(k);
                        }
                    });
                });
            });
        }
        return result;
    }

    private void fillDiagnosisInfo(InquiryProductResponseDto record, List<InquiryDiagnosiscDto> diagnosiscDtos, Map<String, String> lv6Map, Map<Integer, String> cateMap) {
        Set<Integer> cateSet = getCurrProductCateSetByLv6(lv6Map.get(record.getCommonName()), cateMap);
        StringBuilder builder = new StringBuilder();
        diagnosiscDtos.forEach(obj -> {
            String commonNames = obj.getCommonProducts();
            if (StringUtils.isNotBlank(commonNames)) {
                if (Arrays.asList(commonNames.split(",")).stream().anyMatch(str -> str.equals(record.getCommonName()))) {
                    // 名称匹配
                    builder.append(obj.getDiagnosisCodes()).append(",").append(CautionLevel.getTipsNameByType(obj.getCaution())).append(";");
                    return;
                }
            }
            String categories = obj.getCategories();
            if (StringUtils.isNotBlank(categories)) {
                if (Arrays.asList(categories.split(",")).stream().anyMatch(cate -> {
                    Integer category = Integer.valueOf(cate);
                    return cateSet.contains(category);
                })) {
                    // 类别匹配
                    builder.append(obj.getDiagnosisCodes()).append(",").append(CautionLevel.getTipsNameByType(obj.getCaution())).append(";");
                    return;
                }
            }
        });
        record.setDiagnosisLimitStr(builder.toString());
    }

    private void fillBaseRule(InquiryProductResponseDto record, List<InquiryProductRuleDto> ruleDtoList, List<InquiryRationalDictConfig> ageDict, List<InquiryRationalDictConfig> idtegDict) {
        for (ParamAnalysisStrategy strategy : strategies) {
            strategy.fillParam(record, ruleDtoList, ageDict, idtegDict);
        }
    }


    private InquiryProductRuleDtoSearchParam paramHandle(InquiryRationalProductQueryVO queryDto) {
        InquiryProductRuleDtoSearchParam searchParam = new InquiryProductRuleDtoSearchParam();
        // 分页信息
        searchParam.setPageNo(queryDto.getPageNo());
        searchParam.setPageSize(queryDto.getPageSize());
        // 通用名
        searchParam.setCommonName(queryDto.getCommonName());
        // 获取当前六级分类id下所有六级分类id节点
        if (StringUtils.isNotBlank(queryDto.getCategoryLv6Id())) {
            searchParam.setCategoryLv6Ids(getLowerLv6IdsById(Arrays.asList(StringUtil.convertStr(queryDto.getCategoryLv6Id()))));
        }
        // 性别用药
        searchParam.setSexLimit(queryDto.getSexLimit());
        // 启用状态
        searchParam.setStatus(queryDto.getStatus());
        // 年龄限制
        searchParam.setAgeLimits(queryDto.getAgeLimits());
        // 年龄区间限制
        if (StringUtils.isNotBlank(queryDto.getAgeRangeLimit())) {
            InquiryRationalAgeRangeVo ageRangeVo = RationalAgeRangeUtil.convertStr(queryDto.getAgeRangeLimit());
            Optional.ofNullable(ageRangeVo).ifPresent(p -> {
                searchParam.setRangeBegin(ageRangeVo.getStartDay());
                searchParam.setRangeEnd(ageRangeVo.getEndDay());
            });
        }
        // 肝肾功能异常限制
        searchParam.setHealthLimit(queryDto.getHealthLimit());
        // 哺乳期异常限制
        searchParam.setWomenLimit(queryDto.getWomenLimit());
        // 成分查询类型
        searchParam.setIngredientQueryType(queryDto.getIngredientQueryType());
        // 成分列表
        searchParam.setIngredientList(queryDto.getIngredientList());
        // 医保备注查询类型
        searchParam.setMedicareRemarkQueryType(queryDto.getMedicareRemarkQueryType());
        // 通用名集合
        Optional.ofNullable(queryDto)
            .map(InquiryRationalProductQueryVO::getQueryParams)
            .map(QueryParamDto::getProductCommons)
            .ifPresent(searchParam::setProductCommonNames);
        // 六级分类集合
        Optional.ofNullable(queryDto)
            .map(InquiryRationalProductQueryVO::getQueryParams)
            .map(QueryParamDto::getCategoryLv6Ids)
            .ifPresent(p -> {
                List<String> list = CollectionUtils.isEmpty(searchParam.getCategoryLv6Ids()) ? new ArrayList<>() : searchParam.getCategoryLv6Ids();
                list.addAll(p);
            });

        return searchParam;
    }

    /**
     * 调用中台服务，获取当前六级分类id下所有六级分类id节点
     *
     * @return
     */
    private List<String> getLowerLv6IdsById(List<Integer> ids) {
        Map<Integer, List<SaasCategoryVo>> map = inquiryMidCategoryApi.queryCategoryChildPathByIds(ids);

        List<String> lowerLv6Ids = map.values().stream().flatMap(v -> v.stream().map(SaasCategoryVo::getId)).map(String::valueOf).collect(Collectors.toList());

        return lowerLv6Ids;
    }

    private QueryParamDto getQueryParam(InquiryRationalProductQueryVO queryDto) {
        List<QueryParamDto> resultDtos = new ArrayList<>();
        // 获取诊断禁忌相关信息
        if (!CollectionUtils.isEmpty(queryDto.getDiagnosisList())) {
            QueryParamDto diagnosisResult = inquiryRationalDiagnosisService.queryLimitDrugsByDiagnosis(queryDto.getDiagnosisList());
            if (!ObjectUtils.isEmpty(diagnosisResult)) {
                resultDtos.add(diagnosisResult);
            }
        }
        // 获取配伍禁忌相关信息
        if (!CollectionUtils.isEmpty(queryDto.getCompatibilityCommonList())) {
            QueryParamDto diagnosisResult = inquiryRationalCompatibilityDetailService.queryLimitDrugsByCommonNames(queryDto.getCompatibilityCommonList());
            if (!ObjectUtils.isEmpty(diagnosisResult)) {
                resultDtos.add(diagnosisResult);
            }
        }
        Set<String> productSet = resultDtos.stream().filter(p -> CollectionUtils.isNotEmpty(p.getProductCommons()))
            .flatMap(obj -> obj.getProductCommons().stream())
            .collect(Collectors.toSet());

        Set<String> lv6Set = resultDtos.stream().filter(p -> CollectionUtils.isNotEmpty(p.getCategoryLv6Ids()))
            .flatMap(obj -> obj.getCategoryLv6Ids().stream())
            .collect(Collectors.toSet());
        return QueryParamDto.builder().productCommons(new ArrayList<>(productSet)).categoryLv6Ids(new ArrayList<>(lv6Set)).build();
    }


    @Override
    public ImportResultDto importByExcel(ImportReqDto importReqDto) {
        importReqDto.setExcelName("限制类商品导入").setLimitCount(5000);

        return easyExcelUtil.importData(importReqDto, InquiryRationalProductImportVo.class, productService::productImportByExcel);
    }
}
