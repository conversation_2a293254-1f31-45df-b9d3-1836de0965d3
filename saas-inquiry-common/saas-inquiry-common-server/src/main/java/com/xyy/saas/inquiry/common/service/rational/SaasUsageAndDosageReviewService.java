package com.xyy.saas.inquiry.common.service.rational;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageDrugRuleDto;
import com.xyy.saas.inquiry.common.api.rational.dto.UsageAndDosageRuleDrugQueryDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.UsageAndDosageReviewDto;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.SaasUsageAndDosageReviewPo;
import java.util.List;


/**
 * 用法用量审查(SaasUsageAndDosageReview)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-02 16:15:00
 */
public interface SaasUsageAndDosageReviewService {

    /**
     * 新增数据
     *
     * @param saasUsageAndDosageReviewPo 实例对象
     * @return 实例对象
     */
    SaasUsageAndDosageReviewPo insert(SaasUsageAndDosageReviewPo saasUsageAndDosageReviewPo);

    /**
     * 修改数据
     *
     * @param saasUsageAndDosageReviewPo 实例对象
     * @return 实例对象
     */
    Boolean update(SaasUsageAndDosageReviewPo saasUsageAndDosageReviewPo);

    /**
     * 分页查询
     *
     * @param usageAndDosageReviewDto
     * @return com.github.pagehelper.PageInfo<com.xyy.saas.remote.web.core.dto.UsageAndDosageReviewDto>
     * <AUTHOR> 9/2/24 5:31 PM
     */
    PageResult<UsageAndDosageReviewDto> pageQuery(UsageAndDosageReviewDto usageAndDosageReviewDto);

    /**
     * 条件查询
     *
     * @param usageAndDosageReviewDto
     * @return java.util.List<com.xyy.saas.remote.web.po.SaasUsageAndDosageReviewPo>
     * <AUTHOR> 9/12/24 5:16 PM
     */
    List<SaasUsageAndDosageReviewPo> queryByCondition(UsageAndDosageReviewDto usageAndDosageReviewDto);

    /**
     * 修改用法用量审查规则数量
     *
     * @param idList
     * @param count
     * @return boolean
     * <AUTHOR> 9/13/24 2:47 PM
     */
    boolean updateRelationRuleCount(List<Long> idList, Integer count);

    /**
     * 修改用法用量审查药品数量
     *
     * @param idList
     * @param count
     * @return boolean
     * <AUTHOR> 9/13/24 2:47 PM
     */
    boolean updateRelationDrugCount(List<Long> idList, Integer count);

    /**
     * 根据药品查询用法用量审查规则
     *
     * @param usageAndDosageRuleDrugQueryDto
     * @return com.xyy.saas.remote.web.core.dto.UsageAndDosageDrugRuleDto
     * <AUTHOR> 9/18/24 3:22 PM
     */
    UsageAndDosageDrugRuleDto queryRuleByDrug(UsageAndDosageRuleDrugQueryDto usageAndDosageRuleDrugQueryDto);
}
