package com.xyy.saas.inquiry.common.api.rational.impl;


import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.common.api.rational.InquiryRationalProductApi;
import com.xyy.saas.inquiry.common.api.rational.ProductDiagnosisRelationApi;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryLimitAuditDto;
import com.xyy.saas.inquiry.common.api.rational.dto.ProductDiagnosisRelationSearchDto;
import com.xyy.saas.inquiry.common.service.rational.ProductDiagnosisRelationService;
import com.xyy.saas.inquiry.pojo.RationalTipsVO;
import com.xyy.saas.inquiry.pojo.diagnosis.InquiryDiagnosisSearchRespDto;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/19 17:42
 */
@DubboService
@Slf4j
public class ProductDiagnosisRelationApiImpl implements ProductDiagnosisRelationApi {

    @Resource
    private ProductDiagnosisRelationService productDiagnosisRelationService;

    @Resource
    private InquiryRationalProductApi inquiryRationalProductApi;

    @Override
    public List<InquiryDiagnosisSearchRespDto> productDiagnostics(ProductDiagnosisRelationSearchDto searchDto) {
        return productDiagnosisRelationService.productDiagnostics(searchDto);
    }

    @Override
    public CommonResult<List<RationalTipsVO>> limitDrugAudit(InquiryLimitAuditDto productDto) {
        return inquiryRationalProductApi.limitDrugAudit(productDto);
    }
}
