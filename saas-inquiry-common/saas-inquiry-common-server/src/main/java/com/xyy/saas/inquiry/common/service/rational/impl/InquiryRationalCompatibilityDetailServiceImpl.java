package com.xyy.saas.inquiry.common.service.rational.impl;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.common.api.rational.InquiryCompatibilitySearchApi;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryCompatibilityDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryCompatibilitySearchParam;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalCategoryDetailDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalCompatibilityDetailDto;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryRationalProductCommonDto;
import com.xyy.saas.inquiry.common.api.rational.dto.QueryParamDto;
import com.xyy.saas.inquiry.common.controller.admin.rational.vo.SaasCategoryVo;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalCategoryDetail;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalCompatibilityDetail;
import com.xyy.saas.inquiry.common.dal.dataobject.rational.InquiryRationalProduct;
import com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryRationalCompatibilityDetailMapper;
import com.xyy.saas.inquiry.common.dal.mysql.rational.InquiryRationalProductMapper;
import com.xyy.saas.inquiry.common.service.productmid.InquiryMidCategoryApiImpl;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalCategoryDetailService;
import com.xyy.saas.inquiry.common.service.rational.InquiryRationalCompatibilityDetailService;
import com.xyy.saas.inquiry.enums.rational.RationalFieldType;
import com.xyy.saas.inquiry.enums.rational.RationalStatusEnum;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 问诊配伍禁忌明细表(InquiryRationalCompatibilityDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-07 16:49:30
 */
@Service
public class InquiryRationalCompatibilityDetailServiceImpl implements InquiryRationalCompatibilityDetailService {
    @Resource
    private InquiryRationalCompatibilityDetailMapper inquiryRationalCompatibilityDetailMapper;

    @Resource
    private InquiryMidCategoryApiImpl midCategoryApi;

    @Resource
    private InquiryRationalCategoryDetailService categoryDetailService;

    @Resource
    private InquiryRationalProductMapper productDao;

    @Resource
    private InquiryCompatibilitySearchApi inquiryCompatibilitySearchApi;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public InquiryRationalCompatibilityDetail queryById(Integer id) {
        return this.inquiryRationalCompatibilityDetailMapper.queryById(id);
    }


    /**
     * 新增数据
     *
     * @param inquiryRationalCompatibilityDetail 实例对象
     * @return 实例对象
     */
    @Override
    public InquiryRationalCompatibilityDetail insertSelective(InquiryRationalCompatibilityDetail inquiryRationalCompatibilityDetail) {
        this.inquiryRationalCompatibilityDetailMapper.insert(inquiryRationalCompatibilityDetail);
        return inquiryRationalCompatibilityDetail;
    }

    /**
     * 修改数据
     *
     * @param inquiryRationalCompatibilityDetail 实例对象
     * @return 实例对象
     */
    @Override
    public InquiryRationalCompatibilityDetail updateSelective(InquiryRationalCompatibilityDetail inquiryRationalCompatibilityDetail) {
        this.inquiryRationalCompatibilityDetailMapper.updateById(inquiryRationalCompatibilityDetail);
        return this.queryById(inquiryRationalCompatibilityDetail.getId());
    }

    /**
     * 批量写入
     * @param entities
     * @return
     */
    @Override
    public int insertBatch(List<InquiryRationalCompatibilityDetail> entities){
        if(CollectionUtils.isEmpty(entities)){
            return 0;
        }
        inquiryRationalCompatibilityDetailMapper.insertBatch(entities);
        return entities.size();
    }

    @Override
    public List<InquiryRationalCompatibilityDetail> queryByCondition(InquiryRationalCompatibilityDetailDto detailDto) {
        return inquiryRationalCompatibilityDetailMapper.queryByCondition(detailDto);
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Integer id) {
        return this.inquiryRationalCompatibilityDetailMapper.deleteById(id) > 0;
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主表主键
     * @return 是否成功
     */
    @Override
    public boolean deleteByCompId(Integer id) {
        return this.inquiryRationalCompatibilityDetailMapper.deleteByCompId(id) > 0;
    }

    public List<InquiryCompatibilityDto> searchEs(List<String> compatibilityCommonList,Integer status){
        //先根据通用名查分类（需要获取药品全链路分类节点）
        InquiryRationalProductCommonDto product = InquiryRationalProductCommonDto.builder().commonNames(compatibilityCommonList).build();
        List<InquiryRationalProduct> products = productDao.queryByCondition(product);
        //拿到所有商品的六级分类的id集合
        List<Integer> lv6Ids = products.stream().filter(product1 -> StringUtils.isNotBlank(product1.getCategoryLv6Id())).map(InquiryRationalProduct::getCategoryLv6Id).map(Integer::parseInt).collect(Collectors.toList());
        Map<Integer, List<SaasCategoryVo>>  childMap =  midCategoryApi.queryCategoryChildPathByIds(lv6Ids);
        Set<Integer> lv6Set = new HashSet<>();
        childMap.forEach((key, value) -> {
            lv6Set.add(key);
            value.forEach(vo -> lv6Set.add(vo.getId()));
        });
        List<InquiryRationalCategoryDetail>  categoryDetails = categoryDetailService.queryByCondition(InquiryRationalCategoryDetailDto.builder().classifyIds(new ArrayList<>(lv6Set)).build());
        Set<Integer> caregorySet = categoryDetails.stream().map(InquiryRationalCategoryDetail::getCategoryId).collect(Collectors.toSet());
        //拿通用名集合  与 分类集合  去ES查询  任一命中
        CommonResult<PageResult<InquiryCompatibilityDto>> apiResult =
            inquiryCompatibilitySearchApi.pageSearchInquiryCompatibility(InquiryCompatibilitySearchParam.builder().status(status).commonNames(compatibilityCommonList).categorys(new ArrayList<>(caregorySet)).searchType(1).build());
        if(ObjectUtils.isEmpty(apiResult) || ObjectUtils.isEmpty(apiResult.getData())){
            return Collections.emptyList();
        }
        return apiResult.getData().getList();
    }

    @Override
    public QueryParamDto queryLimitDrugsByCommonNames(List<String> compatibilityCommonList) {
        List<InquiryCompatibilityDto> dtos = searchEs(compatibilityCommonList,null);
        Set<String> prodSet = new HashSet<>();
        Set<Integer> caregorySet = new HashSet<>();

        dtos.forEach(obj ->{
            String value = obj.getSecondParam();
            if(RationalFieldType.CATEGORY.getType().equals(obj.getSecondType())){
                caregorySet.add(Integer.valueOf(value));
            }
            if(RationalFieldType.COMMON_NAME.getType().equals(obj.getSecondType())){
                prodSet.add(value);
            }
        });
        List<InquiryRationalCategoryDetail> categoryDetails = categoryDetailService.queryByCondition(InquiryRationalCategoryDetailDto.builder().categoryIds(new ArrayList<>(caregorySet)).build());
        List<String> lv6Res = categoryDetails.stream().map(InquiryRationalCategoryDetail::getClassifyId).map(String::valueOf).distinct().collect(Collectors.toList());
        return QueryParamDto.builder().productCommons(new ArrayList<>(prodSet)).categoryLv6Ids(lv6Res).build();
    }

    @Override
    public List<InquiryCompatibilityDto> queryCompatibilityDetail(List<String> commonNames) {
        return searchEs(commonNames, RationalStatusEnum.ON.getCode());
    }
}
