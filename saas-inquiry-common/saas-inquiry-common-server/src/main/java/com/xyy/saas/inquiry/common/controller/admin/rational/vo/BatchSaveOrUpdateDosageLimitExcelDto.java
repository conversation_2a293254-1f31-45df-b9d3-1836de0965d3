package com.xyy.saas.inquiry.common.controller.admin.rational.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.excel.ImportExcelVoDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 批量新增或修改excel对象
 *
 * <AUTHOR>
 * @Date 4/22/24 10:39 AM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BatchSaveOrUpdateDosageLimitExcelDto extends ImportExcelVoDto {

    private static final long serialVersionUID = 6302938940802062648L;

    @ExcelProperty(value = "中台商品ID", index = 0)
    private String standardId;

    @ExcelProperty(value = "是否长处方药品", index = 1)
    private String whetherLongPrescription;

    @ExcelProperty(value = "最小包装数量", index = 2)
    private String minimumPackageQuantity;

    @ExcelProperty(value = "总使用剂量限制", index = 3)
    private String totalDoseLimit;

    @ExcelProperty(value = "限制等级", index = 4)
    private String cautionLevelDesc;
}
