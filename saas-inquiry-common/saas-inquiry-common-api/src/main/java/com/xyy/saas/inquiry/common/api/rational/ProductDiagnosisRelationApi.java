package com.xyy.saas.inquiry.common.api.rational;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.common.api.rational.dto.InquiryLimitAuditDto;
import com.xyy.saas.inquiry.common.api.rational.dto.ProductDiagnosisRelationSearchDto;
import com.xyy.saas.inquiry.pojo.RationalTipsVO;
import com.xyy.saas.inquiry.pojo.diagnosis.InquiryDiagnosisSearchRespDto;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/19 17:13
 */
public interface ProductDiagnosisRelationApi {


    List<InquiryDiagnosisSearchRespDto> productDiagnostics(ProductDiagnosisRelationSearchDto searchDto);

    CommonResult<List<RationalTipsVO>> limitDrugAudit(InquiryLimitAuditDto productDto);
}
