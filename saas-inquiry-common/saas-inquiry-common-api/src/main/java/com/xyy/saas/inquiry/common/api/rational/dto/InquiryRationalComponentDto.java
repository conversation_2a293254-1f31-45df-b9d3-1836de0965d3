package com.xyy.saas.inquiry.common.api.rational.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.enums.rational.RationalStatusEnum;
import com.xyy.saas.inquiry.pojo.excel.ImportExcelVoDto;
import jakarta.validation.constraints.Size;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2023/11/06 18:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InquiryRationalComponentDto extends ImportExcelVoDto {

    /**
     * 名称 eg:新生儿/抗生素
     */
    @ExcelProperty(value = "成分", index = 0)
    @Size(max = 64, message = "成分超出最大长度64限制")
    private String name;
    /**
     * 是否启用：0启用，1禁用
     */
    @ExcelProperty(value = "是否启用", index = 1)
    @Size(max = 64, message = "成分超出最大长度2限制")
    private String status;


    @Override
    public void valid() {
        if (RationalStatusEnum.valueOfName(status) == null) {
            this.errMsg += "是否启用仅支持填写“启用”/“禁用”";
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        InquiryRationalComponentDto that = (InquiryRationalComponentDto) o;
        return Objects.equals(name, that.name) &&
            Objects.equals(status, that.status);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, status);
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
