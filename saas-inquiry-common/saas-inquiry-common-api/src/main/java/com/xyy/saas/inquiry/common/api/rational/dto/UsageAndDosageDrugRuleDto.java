package com.xyy.saas.inquiry.common.api.rational.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用法用量药品规则
 *
 * <AUTHOR>
 * @Date 9/9/24 4:44 PM
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UsageAndDosageDrugRuleDto implements Serializable {

    private static final long serialVersionUID = -3161607010906012281L;

    private List<UsageAndDosageDrugRuleDetailDto> usageAndDosageDrugRuleDetailDtoList;

    @Data
    @Builder
    public static class UsageAndDosageDrugRuleDetailDto implements Serializable {

        private static final long serialVersionUID = 4563118773611223454L;

        private String commonName;

        private String specification;

        /**
         * 单次剂量最低值
         */
        private BigDecimal minSingleDose;
        /**
         * 单次剂量最高值
         */
        private BigDecimal maxSingleDose;

        /**
         * 用药频次集合json
         */
        private List<String> useFrequencyListList;

        /**
         * 给药单位
         */
        private String singleUnit;

        /**
         * 给药途径
         */
        private String directions;

        private Integer ageRiskTipsSwitch;

    }

}
