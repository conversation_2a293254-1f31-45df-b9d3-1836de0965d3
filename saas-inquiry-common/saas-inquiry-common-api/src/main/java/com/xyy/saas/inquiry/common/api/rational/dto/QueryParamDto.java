package com.xyy.saas.inquiry.common.api.rational.dto;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class QueryParamDto implements Serializable {

    /**
     * 商品通用名集合
     */
    private List<String> productCommons;


    /**
     * 六级分类集合
     */
    private List<String> categoryLv6Ids;
}
