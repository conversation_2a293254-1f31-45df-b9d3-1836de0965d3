package com.xyy.saas.inquiry.common.api.rational.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;

/**
 * 用法用量规则药品查询对象
 *
 * <AUTHOR>
 * @Date 9/9/24 4:20 PM
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UsageAndDosageRuleDrugQueryDto implements Serializable {

    private static final long serialVersionUID = -3206074396860078352L;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 药品和规格集合
     */
    private List<UsageAndDosageRuleDrugDetailQueryDto> usageAndDosageRuleDrugDetailQueryDtoList;

    @Data
    public static class UsageAndDosageRuleDrugDetailQueryDto implements Serializable {

        private static final long serialVersionUID = 6730756380917884939L;

        private String commonName;

        private String specification;
    }
}
