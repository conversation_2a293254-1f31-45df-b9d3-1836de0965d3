package com.xyy.saas.inquiry.im.server.service.message;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_IM_MESSAGE_NOT_EXISTS;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_IM_RECORD_ARCHIVE_ERROR;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.enums.tencent.TencentImMessageReadStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryLastMessageDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryUserImMappingDto;
import com.xyy.saas.inquiry.im.server.config.TencentImConfig;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentImCallBackReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessagePageReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageRespVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageSaveReqVO;
import com.xyy.saas.inquiry.im.server.convert.message.InquiryImMessageConvert;
import com.xyy.saas.inquiry.im.server.dal.dataobject.message.InquiryImArchiveMessageDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.message.InquiryImMessageDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.out.TencentImBaseRespDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.user.InquiryImUserDO;
import com.xyy.saas.inquiry.im.server.dal.mysql.message.InquiryImArchiveMessageMapper;
import com.xyy.saas.inquiry.im.server.dal.mysql.message.InquiryImMessageMapper;
import com.xyy.saas.inquiry.im.server.dal.mysql.user.InquiryImUserMapper;
import com.xyy.saas.inquiry.im.server.mq.message.TencentImCallBackEvent;
import com.xyy.saas.inquiry.im.server.mq.producer.TencentImCallBackProducer;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentImClient;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryQueryDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.FileApiUtil;
import com.xyy.saas.inquiry.util.ListPageUtil;
import jakarta.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2024/11/28 16:08
 * @Description: 必须描述类做什么事情, 实现什么功能
 */
@Service
@Slf4j
public class InquiryImMessageServiceImpl implements InquiryImMessageService {

    @Resource
    private InquiryImMessageMapper inquiryImMessageMapper;

    @Resource
    private InquiryImUserMapper inquiryImUserMapper;

    @Resource
    private TencentImClient tencentImClient;

    @Resource
    private TencentImConfig tencentImConfig;

    @Resource
    private TencentImCallBackProducer tencentImCallBackProducer;

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    @Resource
    private FileApi fileApi;

    @Resource
    private TenantParamConfigApi tenantParamConfigApi;

    @Resource
    private InquiryImArchiveMessageMapper inquiryImArchiveMessageMapper;

    /**
     * 发送常规消息
     *
     * @param messageDto 消息对象
     * @return boolean
     */
    @Override
    public Boolean sendUserMessage(InquiryImMessageDto messageDto) {
        // 常规消息转换
        Map<String, Object> params = InquiryImMessageConvert.INSTANCE.buildCreateImMessageDO(messageDto);
        return sendMessage(params, tencentImConfig.getApi().getSendMessage());
    }

    /**
     * 发送消息
     *
     * @param params  消息内容
     * @param apiPath 接口路径(单发 || 群发)
     * @return
     */
    Boolean sendMessage(Map<String, Object> params, String apiPath) {
        // 调用腾讯云接口发送消息
        log.info("腾讯IM发送消息,入参:{}", params);
        Map<String, Object> result = tencentImClient.callApi(apiPath, params);
        log.info("腾讯IM发送消息,响应:{}", JSON.toJSONString(result));
        TencentImBaseRespDO baseRespDO = BeanUtil.toBean(result, TencentImBaseRespDO.class);
        return baseRespDO.isSuccess();
    }

    /**
     * 发送系统消息
     *
     * @param messageDto
     * @return
     */
    @Override
    public Boolean sendSystemMessage(InquiryImMessageDto messageDto) {
        // 系统消息转换
        Map<String, Object> params = InquiryImMessageConvert.INSTANCE.buildSystemMessageDO(messageDto);
        return sendMessage(params, tencentImConfig.getApi().getSendMessage());
    }

    /**
     * 批量发送系统消息
     *
     * @param messageDto
     * @return
     */
    @Override
    public Boolean batchSendSystemMessage(InquiryImMessageDto messageDto) {
        // 批量系统消息转换
        Map<String, Object> params = InquiryImMessageConvert.INSTANCE.buildSystemBatchMessageDO(messageDto);
        return sendMessage(params, tencentImConfig.getApi().getSendBatchMessage());
    }

    /**
     * 腾讯IM回调接口
     *
     * @param tencentImCallBackReqVO 回调对象
     * @return boolean
     */
    @Override
    public Boolean onCallBackMessage(TencentImCallBackReqVO tencentImCallBackReqVO) {
        // 发送MQ消息
        tencentImCallBackProducer.sendMessage(TencentImCallBackEvent.builder().msg(JSON.toJSONString(tencentImCallBackReqVO)).build());
        return Boolean.TRUE;
    }


    @Override
    public Long createInquiryImMessage(InquiryImMessageSaveReqVO createReqVO) {
        // 插入
        InquiryImMessageDO inquiryImMessage = InquiryImMessageConvert.INSTANCE.convertVO2DOWithUser(createReqVO);
        inquiryImMessageMapper.insert(inquiryImMessage);
        // 返回
        return inquiryImMessage.getId();
    }

    @Override
    public void updateInquiryImMessage(InquiryImMessageSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryImMessageExists(updateReqVO.getId());
        // 更新
        InquiryImMessageDO updateObj = BeanUtils.toBean(updateReqVO, InquiryImMessageDO.class);
        inquiryImMessageMapper.updateById(updateObj);
    }

    @Override
    public void deleteInquiryImMessage(Long id) {
        // 校验存在
        validateInquiryImMessageExists(id);
        // 删除
        inquiryImMessageMapper.deleteById(id);
    }

    private void validateInquiryImMessageExists(Long id) {
        if (inquiryImMessageMapper.selectById(id) == null) {
            throw exception(INQUIRY_IM_MESSAGE_NOT_EXISTS);
        }
    }

    @Override
    public InquiryImMessageDO getInquiryImMessage(Long id) {
        return inquiryImMessageMapper.selectById(id);
    }

    @Override
    public PageResult<InquiryImMessageRespVO> getInquiryImMessagePage(InquiryImMessagePageReqVO pageReqVO) {
        PageResult<InquiryImMessageDO> page = this.selectArchiveImHistoryPageListByInquiryPref(pageReqVO);
        if (page == null || page.getTotal() == null || page.getTotal() == 0) {
            return PageResult.empty();
        }
        List<InquiryImMessageRespVO> voList = InquiryImMessageConvert.INSTANCE.converMsgDOLIST2VOLIST(page.getList());
        // 填充用户名称
        messageListFillUser(voList);
        return new PageResult<>(voList, page.getTotal());
    }

    /**
     * IM回调阅读消息
     *
     * @param callBackReqVO 回调对象
     */
    @Override
    public void readMsg(TencentImCallBackReqVO callBackReqVO) {
        // 查询最后一条未读
        InquiryImMessageDO messageDO = inquiryImMessageMapper.selectLastReadMsg(InquiryImMessageDO.builder().msgTo(callBackReqVO.getReportAccount()).msgFrom(callBackReqVO.getPeerAccount()).msgTime(callBackReqVO.getLastReadTime()).build());
        if (ObjectUtils.isEmpty(messageDO)) {
            return;
        }
        // 设置消息阅读位点
        messageDO.setReadOffset(TencentImMessageReadStatusEnum.READ.getCode());
        inquiryImMessageMapper.updateById(messageDO);
    }

    /**
     * 获得腾讯IM用户消息分页
     *
     * @param pageReqVO 查询条件
     * @return IM消息列表
     */
    @Override
    public List<InquiryImMessageRespVO> getInquiryImMessageList(InquiryImMessagePageReqVO pageReqVO) {
        List<InquiryImMessageDO> messageList = this.selectArchiveImHistoryListByInquiryPref(pageReqVO);
        if (CollectionUtils.isEmpty(messageList)) {
            return List.of();
        }
        List<InquiryImMessageRespVO> result = InquiryImMessageConvert.INSTANCE.converMsgDOLIST2VOLIST(messageList);
        // 填充用户名称
        messageListFillUser(result);
        return result;
    }

    /**
     * 根据问诊单号删除聊天记录
     *
     * @param inquiryPref 问诊单号
     */
    @Override
    public void deleteByInquiryPref(String inquiryPref) {
        inquiryImMessageMapper.deleteByInquiryPref(inquiryPref);
    }

    /**
     * 批量插入消息
     *
     * @param messageDtos
     * @return
     */
    @Override
    public Boolean batchInsertMessage(List<InquiryImMessageDto> messageDtos) {
        List<InquiryImMessageDO> messageList = InquiryImMessageConvert.INSTANCE.convertDTOList2DOList(messageDtos);
        return inquiryImMessageMapper.insertBatch(messageList);
    }

    /**
     * 根据问诊单号批量获取最后一条消息
     *
     * @param inquiryPrefList 问诊单号集合
     * @return 问诊单最后一条消息集合
     */
    @Override
    public List<InquiryLastMessageDto> getLastMessageByInquiryPrefs(List<String> inquiryPrefList) {

        List<InquiryLastMessageDto> resultList = new ArrayList<>();

        // 查询未归档的最后一条im记录
        List<InquiryLastMessageDto> notArchiveInquiryLastMessageDtoList = inquiryImMessageMapper.selectLastMessageByInquiryPrefs(inquiryPrefList);
        if (CollectionUtils.isNotEmpty(notArchiveInquiryLastMessageDtoList)) {
            resultList.addAll(notArchiveInquiryLastMessageDtoList);
        }

        // 查询归档的最后一条im记录的inquiryPref集合
        List<String> notArchiveInquiryPrefList = Optional.ofNullable(notArchiveInquiryLastMessageDtoList)
            .orElse(Collections.emptyList()).stream()
            .map(InquiryLastMessageDto::getInquiryPref)
            .filter(Objects::nonNull)
            .distinct().toList();
        List<String> archiveInquiryPrefList = inquiryPrefList.stream().filter(inquiryPref ->
            CollectionUtils.isEmpty(notArchiveInquiryPrefList) || !notArchiveInquiryPrefList.contains(inquiryPref)).toList();

        // 查询归档的最后一条im记录
        if (CollectionUtils.isNotEmpty(archiveInquiryPrefList)) {
            List<InquiryImArchiveMessageDO> inquiryImArchiveMessageDOList = inquiryImArchiveMessageMapper.selectList(InquiryImArchiveMessageDO::getInquiryPref, archiveInquiryPrefList);
            if (CollectionUtils.isNotEmpty(inquiryImArchiveMessageDOList)) {
                List<InquiryLastMessageDto> archiveInquiryLastMessageDtoList = inquiryImArchiveMessageDOList.stream()
                    .map(item ->
                        InquiryLastMessageDto.builder().inquiryPref(item.getInquiryPref()).lastMsg(item.getMsgBody()).msgExt(item.getMsgExt()).build()).toList();
                resultList.addAll(archiveInquiryLastMessageDtoList);
            }
        }

        return resultList.stream().map(InquiryImMessageConvert.INSTANCE::convertDTO).toList();
    }


    /**
     * 填充问诊用户名称
     *
     * @param messageList
     */
    private void messageListFillUser(List<InquiryImMessageRespVO> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        // 收集所有用户名
        List<String> userAccountList = new ArrayList<>();
        userAccountList.addAll(messageList.stream().map(InquiryImMessageRespVO::getMsgFrom).toList());
        userAccountList.addAll(messageList.stream().map(InquiryImMessageRespVO::getMsgTo).toList());
        if (CollectionUtils.isEmpty(userAccountList)) {
            return;
        }
        // 获取IM账号涉及的所有IM用户
        List<InquiryImUserDO> userList = inquiryImUserMapper.selectList(InquiryImMessagePageReqVO.builder().accountList(userAccountList.stream().distinct().toList()).build());
        // accountId -> userId
        Map<String, Long> userMap = userList.stream().collect(Collectors.toMap(InquiryImUserDO::getAccountId, InquiryImUserDO::getUserId, (k1, k2) -> k2));

        // 收集所有问诊单号
        List<String> inquiryPrefList = messageList.stream().map(InquiryImMessageRespVO::getInquiryPref).distinct().toList();
        // 查询问诊单信息
        List<InquiryRecordDto> inquiryRecordList = inquiryApi.getInquiryRecordList(InquiryQueryDto.builder().prefs(inquiryPrefList).build());
        if (CollectionUtils.isEmpty(inquiryRecordList)) {
            return;
        }
        // 问诊对应用户信息
        Map<String, InquiryUserImMappingDto> inquiryMap = new HashMap<>();
        // doctorPref -> doctorUserId
        Map<String, Long> doctorDtos = new HashMap<>();
        // 查询医生信息
        List<String> doctorPrefList = inquiryRecordList.stream().map(InquiryRecordDto::getDoctorPref).distinct().toList();
        if (!CollectionUtils.isEmpty(doctorPrefList)) {
            doctorDtos = inquiryDoctorApi.getInquiryDoctorByPrefList(doctorPrefList).stream().collect(Collectors.toMap(InquiryDoctorDto::getPref, InquiryDoctorDto::getUserId, (k1, k2) -> k2));
        }
        Map<String, Long> finalDoctorDtos = doctorDtos;
        // 循环问诊单填充map
        inquiryRecordList.forEach(inquiryRecord -> {
            Long doctorUserId = finalDoctorDtos.get(inquiryRecord.getDoctorPref());
            inquiryMap.put(inquiryRecord.getPref(),
                InquiryUserImMappingDto.builder().inquiryPref(inquiryRecord.getPref()).patientName(inquiryRecord.getPatientName()).patientUserId(Long.parseLong(inquiryRecord.getCreator())).doctorName(inquiryRecord.getDoctorName())
                    .doctorUserId(doctorUserId).build());
        });

        Integer dateType = tenantParamConfigApi.getTenantPresDateType(inquiryRecordList.getFirst().getTenantId());

        messageList.forEach(message -> {
            InquiryUserImMappingDto mappingDto = inquiryMap.get(message.getInquiryPref());
            if (ObjectUtil.isEmpty(mappingDto)) {
                return;
            }
            // 填充用户名称
            message.setFromUserName(getUserNameByAccountId(message.getMsgFrom(), userMap, mappingDto));
            message.setToUserName(getUserNameByAccountId(message.getMsgTo(), userMap, mappingDto));

            InquiryImMessageConvert.INSTANCE.convertMsgTime(message, dateType);
        });

    }

    /**
     * 通过accountId获取用户名称
     *
     * @param accountId
     * @param userMap
     * @param mappingDto
     * @return
     */
    private String getUserNameByAccountId(String accountId, Map<String, Long> userMap, InquiryUserImMappingDto mappingDto) {
        // 先获取userId
        Long userId = userMap.get(accountId);
        if (ObjectUtil.equals(mappingDto.getDoctorUserId(), userId)) {
            return mappingDto.getDoctorName();
        }
        if (ObjectUtil.equals(mappingDto.getPatientUserId(), userId)) {
            return mappingDto.getPatientName();
        }
        return "";
    }

    @Override
    public Boolean batchUpdateInquiryImHistory(List<InquiryRecordDto> inquiryRecordDtoList) {

        if (CollectionUtils.isEmpty(inquiryRecordDtoList)) {
            return false;
        }

        log.info("batchUpdateInquiryImHistory#inquiryRecordDtoList:{}", JSON.toJSONString(inquiryRecordDtoList));

        // 过滤掉pref为空的问诊单
        List<InquiryRecordDto> filterBeforeInquiryRecordDtoList = inquiryRecordDtoList.stream().filter(item -> StringUtils.isNotBlank(item.getPref())).toList();

        if (CollectionUtils.isEmpty(filterBeforeInquiryRecordDtoList)) {
            return false;
        }

        // im归档最后一条记录集合
        List<InquiryImArchiveMessageDO> inquiryImArchiveMessageDOList = new ArrayList<>();
        // 批量修改im聊天记录对象集合
        List<InquiryRecordDto> batchUpdateInquiryImHistoryDataList = new ArrayList<>();

        // 分批处理
        for (List<InquiryRecordDto> partitionList : Lists.partition(filterBeforeInquiryRecordDtoList, 50)) {

            Map<String, Long> prefAndIdMap = partitionList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getPref()) && item.getId() != null && item.getId() > 0)
                .collect(Collectors.toMap(InquiryRecordDto::getPref, InquiryRecordDto::getId, (v1, v2) -> v2));

            if (MapUtils.isEmpty(prefAndIdMap)) {
                continue;
            }

            // 根据问诊单号查询im聊天记录
            List<InquiryImMessageDO> inquiryImMessageDOList = inquiryImMessageMapper.selectList(new LambdaQueryWrapperX<InquiryImMessageDO>()
                .in(InquiryImMessageDO::getInquiryPref, prefAndIdMap.keySet())
                .orderByDesc(InquiryImMessageDO::getMsgTime)
                .orderByDesc(InquiryImMessageDO::getMsgSeq));

            if (CollectionUtils.isEmpty(inquiryImMessageDOList)) {
                continue;
            }

            // 根据问诊单号进行im聊天记录分组, 并保证顺序
            Map<String, List<InquiryImMessageDO>> inquiryImMessageDOListMap = inquiryImMessageDOList.stream()
                .collect(Collectors.groupingBy(InquiryImMessageDO::getInquiryPref, LinkedHashMap::new, Collectors.toList()));

            for (Entry<String, List<InquiryImMessageDO>> mapItem : inquiryImMessageDOListMap.entrySet()) {

                // 生成im聊天记录文件
                String imHistoryFileUrl = this.generateImChatFile(mapItem.getValue());

                if (StringUtils.isBlank(imHistoryFileUrl)) {
                    continue;
                }

                inquiryImArchiveMessageDOList.add(InquiryImMessageConvert.INSTANCE.convertDO2ArchiveDO(mapItem.getValue().getFirst()));
                batchUpdateInquiryImHistoryDataList.add(InquiryRecordDto.builder().id(prefAndIdMap.get(mapItem.getKey())).imHistory(imHistoryFileUrl).build());
            }
        }

        if (CollectionUtils.isEmpty(batchUpdateInquiryImHistoryDataList)) {
            return false;
        }

        // step 1 批量更新im历史聊天记录文件
        inquiryApi.batchUpdateInquiryImHistory(batchUpdateInquiryImHistoryDataList);

        if (CollectionUtils.isNotEmpty(inquiryImArchiveMessageDOList)) {
            for (List<InquiryImArchiveMessageDO> item : Lists.partition(inquiryImArchiveMessageDOList, 500)) {
                // step 2 插入im归档表
                inquiryImArchiveMessageMapper.batchInsertByXml(item);
                // step 3 物理删除im已经归档的记录
                inquiryImMessageMapper.physicalDeleteByInquiryPrefList(item.stream().map(InquiryImArchiveMessageDO::getInquiryPref).toList());
            }
        }

        return true;
    }

    /**
     * 生成im聊天记录文件
     *
     * @param inquiryImMessageDOList
     * @return
     */
    public String generateImChatFile(List<InquiryImMessageDO> inquiryImMessageDOList) {
        if (CollectionUtils.isEmpty(inquiryImMessageDOList)) {
            return "";
        }

        try {
            ObjectMapper mapper = new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

            byte[] bytes = mapper.writeValueAsString(inquiryImMessageDOList).getBytes(StandardCharsets.UTF_8);

            try (ByteArrayOutputStream output = new ByteArrayOutputStream();
                ZipOutputStream zos = new ZipOutputStream(output)) {

                String jsonEntryName = inquiryImMessageDOList.get(0).getInquiryPref() + ".json";
                ZipEntry entry = new ZipEntry(jsonEntryName);
                zos.putNextEntry(entry);
                zos.write(bytes);
                zos.closeEntry();

                // 必须先 finish 再获取字节数组
                zos.finish(); // ⚠️ 关键步骤，确保 ZIP 结构完整

                byte[] zipBytes = output.toByteArray();

                String fileUrl = FileApiUtil.createFileCore("imChat" + UUID.randomUUID().toString().replace("-", "") + ".zip", null, false, zipBytes);
                if (StringUtils.isBlank(fileUrl)) {
                    log.error("文件写入Zip异常,未生成im聊天归档文件:{}", JSON.toJSONString(inquiryImMessageDOList));
                    return "";
                }

                return fileUrl;
            }
        } catch (Exception e) {
            log.error("文件写入Zip异常,未生成im聊天归档文件:{}", JSON.toJSONString(inquiryImMessageDOList), e);
            throw exception(INQUIRY_IM_RECORD_ARCHIVE_ERROR);
        }
    }

    /**
     * 查询归档的im聊天记录列表
     *
     * @param inquiryImMessagePageReqVO
     * @return
     */
    public List<InquiryImMessageDO> selectArchiveImHistoryListByInquiryPref(InquiryImMessagePageReqVO inquiryImMessagePageReqVO) {

        if (StringUtils.isBlank(inquiryImMessagePageReqVO.getInquiryPref())) {
            return Lists.newArrayList();
        }

        List<InquiryImMessageDO> messageList = inquiryImMessageMapper.selectList(inquiryImMessagePageReqVO);

        if (CollectionUtils.isEmpty(messageList)) {
            InquiryRecordDto inquiryRecord = inquiryApi.getInquiryRecord(inquiryImMessagePageReqVO.getInquiryPref());
            if (inquiryRecord == null || StringUtils.isBlank(inquiryRecord.getImHistory())) {
                return Lists.newArrayList();
            }
            messageList = this.parseImZipToList(inquiryImMessagePageReqVO.getInquiryPref(), inquiryRecord.getImHistory(), InquiryImMessageDO.class);
        }

        return messageList;
    }

    /**
     * 查询归档的im聊天记录列表
     *
     * @param inquiryImMessagePageReqVO
     * @return
     */
    public PageResult<InquiryImMessageDO> selectArchiveImHistoryPageListByInquiryPref(InquiryImMessagePageReqVO inquiryImMessagePageReqVO) {

        if (StringUtils.isBlank(inquiryImMessagePageReqVO.getInquiryPref())) {
            return new PageResult<>();
        }

        PageResult<InquiryImMessageDO> page = inquiryImMessageMapper.selectPage(inquiryImMessagePageReqVO);

        if (page == null || CollectionUtils.isEmpty(page.getList())) {

            InquiryRecordDto inquiryRecord = inquiryApi.getInquiryRecord(inquiryImMessagePageReqVO.getInquiryPref());
            if (inquiryRecord == null || StringUtils.isBlank(inquiryRecord.getImHistory())) {
                return new PageResult<>();
            }

            List<InquiryImMessageDO> inquiryImMessageDOList = this.parseImZipToList(inquiryImMessagePageReqVO.getInquiryPref(), inquiryRecord.getImHistory(), InquiryImMessageDO.class);
            if (CollectionUtils.isEmpty(inquiryImMessageDOList)) {
                return new PageResult<>();
            }

            List<InquiryImMessageDO> pageInquiryImMessageDOList = ListPageUtil.startPageList(inquiryImMessageDOList, inquiryImMessagePageReqVO.getPageNo(), inquiryImMessagePageReqVO.getPageSize());
            page = new PageResult<>(pageInquiryImMessageDOList, (long) inquiryImMessageDOList.size());
        }

        return page;
    }

    /**
     * 解析ZIP文件成im聊天记录
     *
     * @param zipUrl
     * @param elementType
     * @param <T>
     * @return
     */
    public <T> List<T> parseImZipToList(String inquiryPref, String zipUrl, Class<T> elementType) {
        if (StringUtils.isBlank(zipUrl)) {
            log.info("ZIP文件地址为空:{}", zipUrl);
            return Lists.newArrayList();
        }

        // 使用 try-with-resources 自动关闭资源
        try (
            HttpResponse httpResponse = HttpUtil.createGet(zipUrl).execute();
            ZipInputStream zis = new ZipInputStream(new ByteArrayInputStream(httpResponse.bodyBytes()))) {

            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                if (entry.getName().equals(inquiryPref + ".json")) {
                    ObjectMapper mapper = new ObjectMapper().registerModule(new JavaTimeModule()).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    return mapper.readValue(zis, mapper.getTypeFactory().constructCollectionType(List.class, elementType));
                }
                // ⚠️ 跳过当前非目标条目内容
                byte[] buffer = new byte[1024];
                while (zis.read(buffer) > 0)
                    ; // 清空当前条目数据

                // 可选：关闭当前条目（可提高兼容性）
                zis.closeEntry();
            }
            log.warn("ZIP中未找到指定文件:{}", inquiryPref);
            return Lists.newArrayList();

        } catch (Exception e) {
            log.error("ZIP解析失败", e);
            return Lists.newArrayList();
        }
    }


}
