package com.xyy.saas.inquiry.im.server.controller.app.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Schema(description = "管理后台 - 腾讯IM用户 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryImUserRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "30810")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "用户id & trtc用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30000")
    @ExcelProperty("用户id & trtc用户id")
    private Long userId;

    @Schema(description = "im用户名,唯一", requiredMode = Schema.RequiredMode.REQUIRED, example = "12053")
    @ExcelProperty("im用户名,唯一")
    private String accountId;

    @Schema(description = "im应用sdkAppId", requiredMode = Schema.RequiredMode.REQUIRED, example = "12053")
    @ExcelProperty("sdkAppId")
    private Long sdkAppId;

    @Schema(description = "im用户签名", requiredMode = Schema.RequiredMode.REQUIRED, example = "12053")
    @ExcelProperty("im用户签名")
    private String userSign;

    @Schema(description = "用户昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("用户昵称")
    private String nickName;

    @Schema(description = "trtc应用ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("trtc应用ID")
    private Long trtcAppId;

    @Schema(description = "trtc用户签名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("trtc用户签名")
    private String trtcUserSign;

    @Schema(description = "用户类型 0-门店  1-医生", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("用户类型 0-门店  1-医生")
    private Integer userType;

    @Schema(description = "用户状态 0-正常 1-禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("用户状态 0-正常 1-禁用")
    private Integer userStatus;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}