package com.xyy.saas.inquiry.im.server.controller.app.message.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 腾讯IM用户消息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquiryImMessageRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "10081")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "消息唯一key", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("消息唯一key")
    private String msgKey;

    @Schema(description = "问诊单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问诊单号")
    private String inquiryPref;

    @Schema(description = "发送方IM用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发送方IM用户名")
    private String msgFrom;

    @Schema(description = "发送方用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发送方用户名")
    private String fromUserName;

    @Schema(description = "接收方IM用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("接收方IM用户名")
    private String msgTo;

    @Schema(description = "接收方用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("接收方用户名")
    private String toUserName;

    @Schema(description = "消息发送时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("消息发送时间")
    private LocalDateTime msgTime;

    @Schema(description = "消息已读状态 0-未读 1-已读", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("消息已读状态 0-未读 1-已读")
    private Integer readStatus;

    @Schema(description = "消息体内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("消息体内容")
    private String msgBody;

    @Schema(description = "消息扩展内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("消息扩展内容")
    private String msgExt;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}