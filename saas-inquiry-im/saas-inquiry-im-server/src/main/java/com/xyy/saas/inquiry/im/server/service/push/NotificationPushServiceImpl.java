package com.xyy.saas.inquiry.im.server.service.push;


import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.alibaba.fastjson.JSON;
import com.aliyun.credentials.utils.ParameterHelper;
import com.aliyun.push20160801.Client;
import com.aliyun.push20160801.models.PushRequest;
import com.aliyun.push20160801.models.PushResponse;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushConfigDto;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto;
import com.xyy.saas.inquiry.im.enums.DeviceTypeEnum;
import com.xyy.saas.inquiry.im.enums.PushTypeEnum;
import com.xyy.saas.inquiry.im.server.config.AliOpenApiConfig;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessagePageReqVO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.user.InquiryImUserDO;
import com.xyy.saas.inquiry.im.server.dal.mysql.user.InquiryImUserMapper;
import com.xyy.saas.inquiry.util.AssertUtils;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Service
public class NotificationPushServiceImpl implements NotificationPushService {

    @Resource
    private InquiryImUserMapper inquiryImUserMapper;
    @Resource
    private AliOpenApiConfig aliOpenApiConfig;

    /**
     * 获取配置
     */
    @Override
    public NotificationPushConfigDto getConfig() {
        return new NotificationPushConfigDto()
            .setAndroidChannelProperties(Optional.ofNullable(aliOpenApiConfig).map(AliOpenApiConfig::getAndroidChannelProperties).orElse(null));
    }

    /**
     * 发送通知到手机
     * @param pushReq
     */
    @Override
    public void sendNoticeToMobile(@Nonnull NotificationPushDto pushReq) {
        log.info("发送通知到手机, 请求参数: {}", pushReq);
        // 查询用户的设备类型
        InquiryImMessagePageReqVO imUserQuery = InquiryImMessagePageReqVO.builder()
            .userIdList(pushReq.getUserIdList())
            .clientChannelType(ClientChannelTypeEnum.APP.getCode())
            .build();

        // 根据设备类型分组 & 推送通知
        inquiryImUserMapper.selectList(imUserQuery)
            .stream().collect(Collectors.groupingBy(InquiryImUserDO::getLastDeviceType))
            .forEach((deviceType, imUserList) -> {
                push(pushReq, deviceType, imUserList);
            });
        log.info("发送通知到手机完成, 请求参数: {}", pushReq);
    }

    /**
     * 推送
     * @param pushReq
     * @param deviceType
     * @param imUserList
     */
    private void push(@NotNull NotificationPushDto pushReq, Integer deviceType, List<InquiryImUserDO> imUserList) {
        DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.fromCode(deviceType);
        if (deviceTypeEnum == null) {
            return;
        }
        NotificationPushDto devicePushReq = BeanUtils.toBean(pushReq, NotificationPushDto.class);
        devicePushReq.setUserIdList(imUserList.stream().map(InquiryImUserDO::getUserId).collect(Collectors.toList()));

        try {
            log.info("阿里云EMAS推送【{}】开始: {}", deviceTypeEnum.name(), JSON.toJSONString(devicePushReq));
            // 1 创建Config实例并初始化。
            Client client = new Client(aliOpenApiConfig.toConfig());

            // 2 创建RuntimeObject实例并设置运行参数
            // RuntimeOptions runtime = new RuntimeOptions();

            // 3 创建API请求并设置参数。
            PushRequest request = toPushRequest(deviceTypeEnum, devicePushReq);

            // 4 发起请求并处理应答或异常。
            PushResponse response = client.push(request);

            log.info("阿里云EMAS推送【{}】完成: {}", deviceTypeEnum.name(), JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("阿里云EMAS推送【{}】失败: {}", deviceTypeEnum.name(), e.getMessage(), e);
        }
    }

    /**
     * 转换为阿里云推送请求
     * @param deviceTypeEnum
     * @param pushReq
     * @return
     */
    public PushRequest toPushRequest(DeviceTypeEnum deviceTypeEnum, NotificationPushDto pushReq) {
        AssertUtils.notNull(pushReq, "推送请求不能为空");
        AssertUtils.notNull(deviceTypeEnum, "设备类型不能为空");
        Long appKey = aliOpenApiConfig.getAppKeyByDeviceType(deviceTypeEnum);
        AssertUtils.notNull(appKey, "阿里云EMAS推送AppKey不能为空");
        PushRequest request = new PushRequest();

        // 推送目标
        request.setAppKey(appKey);
        //推送目标: DEVICE:推送给设备; ACCOUNT:推送给指定账号,TAG:推送给自定义标签; ALIAS: 按别名推送; ALL: 全推
        request.setTarget("ACCOUNT");
        //根据 Target 来设定，如 Target=DEVICE, 则对应的值为 设备 id1,设备 id2. 多个值使用逗号分隔.(账号与设备有一次最多 100 个的限制)
        request.setTargetValue(StringUtils.join(pushReq.getUserIdList(), ","));
        // 设备类型 deviceType, iOS 设备: "iOS"; Android 设备: "ANDROID"; 全部: "ALL", 这是默认值.
        request.setDeviceType("ALL");

        // 推送配置
        // MESSAGE:表示消息(默认), NOTICE:表示通知
        request.setPushType(PushTypeEnum.NOTICE.name());
        // 消息的标题
        request.setTitle(pushReq.getTitle());
        // 消息的内容
        request.setBody(pushReq.getBody());
        // 自动对过长标题、内容进行截断。
        // 仅支持明确限制标题、内容的厂商通道，对 APNs、华为、荣耀通道等不限制标题、内容，只限制总请求体大小的不适用。
        request.setTrim(true);

        // //用于定时发送。不设置缺省是立即发送。时间格式按照 ISO8601 标准表示，并需要使用 UTC 时间，格式为`YYYY-MM-DDThh:mm:ssZ`。
        // final Date pushDate = new Date(System.currentTimeMillis() + 3600 * 1000);
        // final String pushTime = ParameterHelper.getISO8601Time(pushDate);
        // // 延后推送。可选，如果不设置表示立即推送
        // request.setPushTime(pushTime);

        if (deviceTypeEnum == DeviceTypeEnum.iOS) {
            // // iOS 应用图标右上角角标
            // request.setIOSBadge(this.iOSBadge);
            // // iOS 通知声音
            // request.setIOSMusic(this.iOSMusic);
            // iOS 的通知是通过 APNs 中心来发送的，需要填写对应的环境信息。'DEV': 表示开发环境 'PRODUCT': 表示生产环境
            request.setIOSApnsEnv(aliOpenApiConfig.getIos().getApnsEnv());
            // // 消息推送时设备不在线（既与移动推送的服务端的长连接通道不通），则这条推送会做为通知，通过苹果的 APNs 通道送达一次。注意：**离线消息转通知仅适用于`生产环境`**
            // request.setIOSRemind(this.iOSRemind);
            // // iOS 消息转通知时使用的 iOS 通知内容，仅当 iOSApnsEnv=`PRODUCT` && iOSRemind 为 true 时有效
            // request.setIOSRemindBody(this.iOSRemindBody);
            // 通知的扩展属性(注意 : 该参数要以 json map 的格式传入,否则会解析出错)
            request.setIOSExtParameters(JSON.toJSONString(pushReq.getExt()));
            // 离线消息是否保存,若保存, 在推送时候，用户即使不在线，下一次上线则会收到，安卓中若为 false 则只走阿里云自有在线通道
            request.setStoreOffline(aliOpenApiConfig.getIos().getStoreOffline());
            // 离线ios走厂商推送，可以保证通知送达

        } else if (deviceTypeEnum == DeviceTypeEnum.ANDROID) {
            // // 通知的提醒方式 ‘VIBRATE': 振动  'SOUND': 声音 'DEFAULT': 声音和振动 'NONE': 不做处理，用户自定义
            // // request.setAndroidNotifyType(this.androidNotifyType);
            // request.setAndroidOpenUrl(this.androidOpenUrl);
            // // 华为厂商通道通知声音
            // request.setAndroidMusic(this.androidMusic);
            // // Android 收到推送后打开对应的 ACTIVITY,仅当`AndroidOpenType="ACTIVITY"`有效
            // request.setAndroidActivity(this.androidActivity);

            // // Android 自定义通知栏样式，取值：1-100
            // request.setAndroidNotificationBarType(this.androidNotificationBarType);
            // // Android 通知在通知栏展示时排列位置的优先级 -2 -1 0 1 2
            // request.setAndroidNotificationBarPriority(this.androidNotificationBarPriority);
            // 设定通知的扩展属性。(注意 : 该参数要以 json map 的格式传入,否则会解析出错)
            request.setAndroidExtParameters(JSON.toJSONString(pushReq.getExt()));

            // 推送控制
            // // 指定下发的推送通道，若不填可从任何可行的通道下发
            // request.setSendChannels(this.sendChannels);

            // // 设置通知覆盖参数，避免重试等场景用户显示多条相同的通知
            // request.setAndroidNotificationNotifyId(this.androidNotificationNotifyId);

            // // 代表本次推送如果推送到华为、荣耀、vivo 通道，是一个正式通知，但本代码示例中这个值被厂商通道特有参数所覆盖
            // request.setAndroidTargetUserType(this.androidTargetUserType);
            // // 代表本次推送如果推送到华为通道，是一个测试性质的通知
            // request.setAndroidHuaweiTargetUserType(this.androidHuaweiTargetUserType);
            // // 代表本次推送如果推送到荣耀通道，是一个测试性质的通知
            // request.setAndroidHonorTargetUserType(this.androidHonorTargetUserType);
            // // 代表本次推送如果推送到华为通道，是一个测试性质的通知，请在推送前把 vivo 设备 regId 加入 vivo 推送平台的测试设备列表中
            // request.setAndroidVivoPushMode(this.androidVivoPushMode);
            // // 华为厂商通道
            // request.setAndroidHuaweiReceiptId(this.androidHuaweiReceiptId);

            // 辅助弹窗（离线）
            // 设置辅助弹窗通知的标题
            request.setAndroidPopupTitle(pushReq.getTitle());
            // 设置辅助弹窗通知的内容
            request.setAndroidPopupBody(pushReq.getBody());


            Optional.ofNullable(aliOpenApiConfig.getAndroid()).ifPresent(config -> {
                // 离线消息是否保存,若保存, 在推送时候，用户即使不在线，下一次上线则会收到，安卓中若为 false 则只走阿里云自有在线通道
                request.setStoreOffline(config.getStoreOffline());
                // 点击通知后动作 'APPLICATION': 打开应用 'ACTIVITY': 打开应用 AndroidActivity 'URL': 打开 URL 'NONE': 无跳转
                request.setAndroidOpenType(config.getAndroidOpenType());
                // 设置该参数后启动辅助弹窗功能, 此处指定通知点击后跳转的 Activity（辅助弹窗的前提条件：1. 集成第三方辅助通道；2. StoreOffline 参数设为 true）
                request.setAndroidPopupActivity(config.getAndroidPopupActivity());
                // // 离线消息失效, 不会再发送
                if (config.getStoreOfflineSeconds() != null && config.getStoreOfflineSeconds() >= 0) {
                    final String expireTime = ParameterHelper.getISO8601Time(new Date(System.currentTimeMillis() + config.getStoreOfflineSeconds() * 3600 * 1000));
                    request.setExpireTime(expireTime);
                }

                // 通知提醒方式：声音+振动
                request.setAndroidNotifyType(config.getAndroidNotifyType());
            });

            // 厂商通道通知分类
            Optional.ofNullable(aliOpenApiConfig).map(AliOpenApiConfig::getAndroidChannelProperties).ifPresent(properties -> {
                // OPPO 通道私信通道 channel_id，以及通用安卓 channel_id
                request.setAndroidNotificationChannel(properties.getAndroidNotificationChannel());
                // 华为
                // request.setAndroidNotificationHuaweiChannel(properties.getAndroidNotificationHuaweiChannel());
                // 荣耀通道消息分类参数，对应荣耀通道 importance 参数
                request.setAndroidNotificationHonorChannel(properties.getAndroidNotificationHonorChannel());
                // 小米通道通知类型 channel_id
                request.setAndroidNotificationXiaomiChannel(properties.getAndroidNotificationXiaomiChannel());
                // vivo
                // request.setAndroidNotificationVivoChannel(properties.getAndroidNotificationVivoChannel());

                // 消息分组
                request.setAndroidNotificationGroup(properties.getAndroidNotificationGroup());
                // 华为通道的通知分类参数，服务与通讯类通知需向华为通道申请权限
                request.setAndroidMessageHuaweiCategory(properties.getAndroidMessageHuaweiCategory());
                // oppo 通道的通知分类参数，安装渠道非vivo官方，系统消息接收权限开启、运营消息默认关闭
                request.setAndroidMessageOppoCategory(properties.getAndroidMessageOppoCategory());
                // vivo 通道的通知分类参数，系统消息需向 vivo 通道申请权限
                request.setAndroidMessageVivoCategory(properties.getAndroidMessageVivoCategory());
            });
        }

        return request;
    }



}
