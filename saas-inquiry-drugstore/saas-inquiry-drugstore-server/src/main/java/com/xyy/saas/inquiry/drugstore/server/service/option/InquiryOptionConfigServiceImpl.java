package com.xyy.saas.inquiry.drugstore.server.service.option;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_NOT_EXISTS;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.INQUIRY_OPTION_CONFIG_NOT_EXISTS;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.INQUIRY_OPTION_CONFIG_NOT_FOR_TARGET;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.dict.core.DictFrameworkUtils;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantRespDto;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.drugstore.api.option.dto.AreaInquiryDoctorDeptDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.AreaInquiryHospitalTypeDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryCommonPrefAndNameDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionAreaConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionStoreConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.PresAllRealPeopleInquiryTimeConfigDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionAreaConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionConfigPageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionGlobalConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionStoreConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.convert.option.InquiryOptionConfigConvert;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.option.InquiryOptionConfigDO;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.option.InquiryOptionConfigMapper;
import com.xyy.saas.inquiry.hospital.api.dept.InquiryDeptApi;
import com.xyy.saas.inquiry.hospital.api.dept.dto.InquiryDeptDto;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.UserUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 问诊配置选项 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class InquiryOptionConfigServiceImpl implements InquiryOptionConfigService {


    @Resource
    private InquiryOptionConfigMapper inquiryOptionConfigMapper;
    @Resource
    private TenantApi tenantApi;

    @Resource
    private AdminUserApi adminUserApi;

    @DubboReference
    private InquiryHospitalApi inquiryHospitalApi;


    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    @Resource
    private InquiryDeptApi inquiryDeptApi;

    @Resource
    private DictDataApi dictDataApi;

    private int insertOrUpdateBatch(List<InquiryOptionConfigDO> saveDoList) {
        if (CollectionUtils.isEmpty(saveDoList)) {
            return 0;
        }

        saveDoList.forEach(s -> {
            s.setCreator(WebFrameworkUtils.getLoginUserId() == null ? "" : WebFrameworkUtils.getLoginUserId().toString());
        });

        return inquiryOptionConfigMapper.insertOrUpdateBatch(saveDoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveInquiryOptionGlobalConfig(InquiryOptionGlobalConfigReqVO reqVO) {
        InquiryOptionConfigQueryDto queryDto = new InquiryOptionConfigQueryDto();
        // 全局-全部配置
        queryDto.setTargetType(InquiryTargetTypeEnum.GLOBAL.getType())
            .setTargetId(TenantConstant.DEFAULT_TENANT_ID);
        // 查询出当前配置
        List<InquiryOptionConfigDO> doList = inquiryOptionConfigMapper.queryList(queryDto);
        List<InquiryOptionConfigDO> saveDoList = InquiryOptionConfigConvert.INSTANCE.globalOptions2DOList(reqVO, doList);
        // 批量保存
        return insertOrUpdateBatch(saveDoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveInquiryOptionAreaConfig(InquiryOptionAreaConfigReqVO reqVO) {
        InquiryOptionConfigQueryDto queryDto = new InquiryOptionConfigQueryDto();
        // Area areaVO = AreaUtils.getArea(reqVO.getArea());
        queryDto.setTargetType(InquiryTargetTypeEnum.AREA.getType())
            .setTargetIds(reqVO.getAreas())
            // 配置类型
            .setOptionTypeList(List.of(reqVO.getOptionType()));
        // 查询出当前配置
        List<InquiryOptionConfigDO> doList = inquiryOptionConfigMapper.queryList(queryDto);
        List<InquiryOptionConfigDO> saveDoList = InquiryOptionConfigConvert.INSTANCE.areaOptions2DOList(reqVO, doList);
        // 批量保存
        return insertOrUpdateBatch(saveDoList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveInquiryOptionStoreConfig(InquiryOptionStoreConfigReqVO reqVO) {
        if (CollUtil.isEmpty(reqVO.getTenantIdList())) {
            return 0;
        }

        return reqVO.getTenantIdList().stream().distinct().mapToInt(tenantId -> {
            // 查询租户信息
            TenantDto tenantDto = tenantApi.getTenant(tenantId);
            if (tenantDto == null) {
                throw exception(TENANT_NOT_EXISTS);
            }
            InquiryOptionConfigQueryDto queryDto = new InquiryOptionConfigQueryDto();
            queryDto.setTargetType(InquiryTargetTypeEnum.STORE.getType())
                .setTargetId(tenantId)
                // 配置类型
                .setOptionTypeList(List.of(reqVO.getOptionType()));
            // 查询出当前配置
            List<InquiryOptionConfigDO> doList = inquiryOptionConfigMapper.queryList(queryDto);
            List<InquiryOptionConfigDO> saveDoList = InquiryOptionConfigConvert.INSTANCE.storeOptions2DOList(reqVO, tenantDto, doList);
            // 批量保存
            return insertOrUpdateBatch(saveDoList);
        }).sum();
    }

    @Override
    @Transactional(readOnly = true)
    public InquiryOptionGlobalConfigRespDto getInquiryOptionGlobalConfig(InquiryOptionConfigQueryDto dto) {
        dto.setTargetType(InquiryTargetTypeEnum.GLOBAL.type)
            .setTargetId(TenantConstant.DEFAULT_TENANT_ID);
        List<InquiryOptionConfigDO> optionList = inquiryOptionConfigMapper.queryList(dto);
        // 转换返回
        return InquiryOptionConfigConvert.INSTANCE.convertDO2GlobalOptions(optionList);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<InquiryOptionAreaConfigRespDto> getInquiryOptionAreaConfigPage(
        InquiryOptionConfigPageReqVO reqVO) {
        reqVO.setTargetType(InquiryTargetTypeEnum.AREA.type);
        PageResult<InquiryOptionConfigDO> pageResult = inquiryOptionConfigMapper.selectPage(reqVO);
        // 转换返回
        List<InquiryOptionAreaConfigRespDto> list = pageResult.getList().stream().map(InquiryOptionConfigConvert.INSTANCE::convertDO2AreaOptions).toList();
        UserUtil.fillUserInfo(list, adminUserApi::getUserNameMap);
        // 填充医院信息
        fillExtInfo(reqVO.getOptionType(), list);
        return new PageResult<>(list, pageResult.getTotal());
    }

    /**
     * 根据选项类型填充扩展信息
     * <p>
     * 参考 queryInquiryOptionAreaConfig 方法的模式，使用 switch 分发到具体的处理方法
     *
     * @param optionType 选项类型
     * @param list       响应DTO列表，同一批次的数据具有相同的选项类型
     */
    private void fillExtInfo(Integer optionType, List<InquiryOptionAreaConfigRespDto> list) {
        if (CollUtil.isEmpty(list) || optionType == null) {
            return;
        }
        try {
            // 首先处理兼容性
            list.forEach(respDto -> {
                handleHospitalTypeCompatibility(respDto);
                handleDoctorDeptCompatibility(respDto);
            });

            // 填充通用的医院信息（适用于所有包含医院字段的选项类型）
            fillCommonHospitalInfo(list, null);

            // 根据选项类型分发到具体的处理方法
            switch (InquiryOptionTypeEnum.fromType(optionType)) {
                case PROC_INQUIRY_PRODUCT_DOCTOR -> fillProcInquiryProductDoctorExt(list);
                default -> log.debug("选项类型 {} 无需特殊处理", optionType);
            }

        } catch (Exception e) {
            log.error("填充扩展信息时发生异常，选项类型: {}", optionType, e);
            // 不抛出异常，避免影响主流程
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<InquiryOptionAreaConfigRespDto> getInquiryOptionAreaAllConfig(InquiryOptionConfigPageReqVO reqVO) {
        InquiryOptionConfigQueryDto queryDto = InquiryOptionConfigQueryDto.builder().optionTypeList(Collections.singletonList(reqVO.getOptionType())).targetType(InquiryTargetTypeEnum.AREA.type).build();
        List<InquiryOptionConfigDO> doList = inquiryOptionConfigMapper.queryList(queryDto);
        return InquiryOptionConfigConvert.INSTANCE.convertDtos(doList);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<InquiryOptionStoreConfigRespDto> getInquiryOptionStoreConfigPage(
        InquiryOptionConfigPageReqVO reqVO) {
        if (StringUtils.isNotBlank(reqVO.getTargetName())) {
            List<TenantDto> tenantList = tenantApi.getTenantList(reqVO.getTargetName());
            if (CollectionUtils.isEmpty(tenantList)) {
                return PageResult.empty();
            }
            reqVO.setTargetIds(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(tenantList, TenantDto::getId));
            reqVO.setTargetName(null);
        }
        reqVO.setTargetType(InquiryTargetTypeEnum.STORE.type);
        PageResult<InquiryOptionConfigDO> pageResult = inquiryOptionConfigMapper.selectPage(reqVO);
        // 转换返回
        List<InquiryOptionStoreConfigRespDto> list = pageResult.getList().stream().map(InquiryOptionConfigConvert.INSTANCE::convertDO2StoreOptions).toList();
        if (CollUtil.isEmpty(list)) {
            return new PageResult<>();
        }
        Map<Long, TenantDto> tenantDtoMap = tenantApi.getTenantListMap(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(list, InquiryOptionStoreConfigRespDto::getTenantId));
        for (InquiryOptionStoreConfigRespDto dto : list) {
            Optional.ofNullable(tenantDtoMap.get(dto.getTenantId())).ifPresent(t -> dto.setTenantPref(t.getPref()));
        }
        UserUtil.fillUserInfo(list, adminUserApi::getUserNameMap);
        fillCommonHospitalInfo(null, list);

        return new PageResult<>(list, pageResult.getTotal());
    }

    @Override
    @Transactional(readOnly = true)
    public InquiryOptionAreaConfigRespDto getInquiryOptionAreaConfig(Long id) {
        InquiryOptionConfigDO optionConfigDO = inquiryOptionConfigMapper.selectById(id);
        if (optionConfigDO == null) {
            throw exception(INQUIRY_OPTION_CONFIG_NOT_EXISTS);
        }
        if (!Objects.equals(optionConfigDO.getTargetType(), InquiryTargetTypeEnum.AREA.type)) {
            throw exception(INQUIRY_OPTION_CONFIG_NOT_FOR_TARGET, InquiryTargetTypeEnum.AREA.name);
        }
        return InquiryOptionConfigConvert.INSTANCE.convertDO2AreaOptions(optionConfigDO);
    }

    @Override
    @Transactional(readOnly = true)
    public InquiryOptionStoreConfigRespDto getInquiryOptionStoreConfig(Long id) {
        InquiryOptionConfigDO optionConfigDO = inquiryOptionConfigMapper.selectById(id);
        if (optionConfigDO == null) {
            throw exception(INQUIRY_OPTION_CONFIG_NOT_EXISTS);
        }
        if (!Objects.equals(optionConfigDO.getTargetType(), InquiryTargetTypeEnum.STORE.type)) {
            throw exception(INQUIRY_OPTION_CONFIG_NOT_FOR_TARGET, InquiryTargetTypeEnum.STORE.name);
        }
        return InquiryOptionConfigConvert.INSTANCE.convertDO2StoreOptions(optionConfigDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteInquiryOptionConfig(Long id) {
        // 校验存在
        validateInquiryOptionConfigExists(id);
        // 删除
        return inquiryOptionConfigMapper.deleteById(id);
    }

    private void validateInquiryOptionConfigExists(Long id) {
        if (inquiryOptionConfigMapper.selectById(id) == null) {
            throw exception(INQUIRY_OPTION_CONFIG_NOT_EXISTS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDeleteInquiryOptionConfig(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        return inquiryOptionConfigMapper.deleteByIds(ids);
    }

    @Override
    @Transactional(readOnly = true)
    public InquiryOptionConfigRespDto getInquiryOptionConfig(TenantDto tenantDto,
        InquiryOptionTypeEnum... optionTypeEnums) {
        Assert.notNull(tenantDto, "租户信息不能为空");
        // 查询全局 & 区域 & 门店配置, optionType 不传默认查全部类型
        List<Integer> optionTypeList = Arrays.stream(optionTypeEnums).map(InquiryOptionTypeEnum::getType).toList();
        Map<Integer, List<InquiryOptionConfigDO>> optionTypeMap = inquiryOptionConfigMapper.queryAllByTenantAndOptionType(tenantDto, optionTypeList)
            .stream().collect(Collectors.groupingBy(InquiryOptionConfigDO::getOptionType));

        // 获取优先级最高的配置
        List<InquiryOptionConfigDO> optionList = new ArrayList<>();
        for (Entry<Integer, List<InquiryOptionConfigDO>> entry : optionTypeMap.entrySet()) {
            InquiryOptionTypeEnum optionTypeEnum = InquiryOptionTypeEnum.fromType(entry.getKey());
            if (optionTypeEnum == null) {
                continue;
            }
            // 优先级最高的配置（配置不覆盖传递）
            InquiryOptionConfigDO option = null;
            int priority = Integer.MAX_VALUE;
            for (InquiryOptionConfigDO optionConfigDO : entry.getValue()) {
                int priority1 = optionConfigDO.priority(optionTypeEnum);
                if (priority > priority1) {
                    priority = priority1;
                    option = optionConfigDO;
                }
            }
            if (option != null) {
                optionList.add(option);
            }
        }
        // 转换返回
        return InquiryOptionConfigConvert.INSTANCE.convertDO2GenericOptions(optionList);
    }


    @Override
    public PageResult<TenantRespDto> getTenantPage(TenantReqDto tenantReqDto) {

        PageResult<TenantRespDto> pageResult = tenantApi.pageTenant(tenantReqDto);
        if (CollectionUtils.isEmpty(pageResult.getList())) {
            return pageResult;
        }
        if (tenantReqDto.getOptionType() != null) {
            List<Long> tenantIds = cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(pageResult.getList(), TenantRespDto::getId);

            List<Long> existsTenantIds = inquiryOptionConfigMapper.queryList(
                    InquiryOptionConfigQueryDto.builder().targetIds(tenantIds).optionTypeList(Collections.singletonList(tenantReqDto.getOptionType()))
                        .targetType(InquiryTargetTypeEnum.STORE.type).build())
                .stream().map(InquiryOptionConfigDO::getTargetId).filter(Objects::nonNull).toList();

            for (TenantRespDto dto : pageResult.getList()) {
                dto.setExistsOptionType(existsTenantIds.contains(dto.getId()));
            }
        }

        return pageResult;
    }

    @Override
    public InquiryOptionConfigDO getInquiryOptionConfig(Long targetId, InquiryOptionTypeEnum optionTypeEnum) {
        return inquiryOptionConfigMapper.queryOne(InquiryOptionConfigQueryDto.builder().targetId(targetId).optionTypeList(Collections.singletonList(optionTypeEnum.getType())).build());
    }

    @Override
    public InquiryOptionAreaConfigRespDto queryInquiryOptionAreaConfig(InquiryOptionConfigPageReqVO reqVO) {
        reqVO.setTargetType(InquiryTargetTypeEnum.AREA.type);
        List<InquiryOptionConfigDO> list = inquiryOptionConfigMapper.queryList(reqVO);
        if (CollectionUtils.isEmpty(list)) {
            return InquiryOptionAreaConfigRespDto.builder().build();
        }

        List<InquiryOptionAreaConfigRespDto> inquiryOptionAreaConfigRespDtoList = list.stream().map(InquiryOptionConfigConvert.INSTANCE::convertDO2AreaOptions).toList();

        // 构建通用拓展字段
        InquiryOptionAreaConfigRespDto result = this.buildCommonExt(inquiryOptionAreaConfigRespDtoList);

        // 补充字段赋值
        switch (InquiryOptionTypeEnum.fromType(reqVO.getOptionType())) {
            case PROC_INQUIRY_PRODUCT_DOCTOR -> this.buildProcInquiryProductDoctorExt(result, inquiryOptionAreaConfigRespDtoList);
            case PRES_ALL_REAL_PEOPLE_INQUIRY -> this.buildPresAllRealPeopleInquiryExt(result, inquiryOptionAreaConfigRespDtoList);
            case PROC_VIDEO_INQUIRY -> this.buildProcVideoInquiryExt(result, inquiryOptionAreaConfigRespDtoList);
            case PROC_IDENTITY_REQUIRED -> this.buildProcIdentityRequiredExt(result, inquiryOptionAreaConfigRespDtoList);
            case PROC_GUARDIAN_INPUT -> this.buildProcGuardianInputExt(result, inquiryOptionAreaConfigRespDtoList);
        }

        return result;
    }

    /**
     * 构建监护人填写拓展字段
     *
     * @param result
     * @param inquiryOptionAreaConfigRespDtoList
     */
    private void buildProcGuardianInputExt(InquiryOptionAreaConfigRespDto result, List<InquiryOptionAreaConfigRespDto> inquiryOptionAreaConfigRespDtoList) {

        Set<Integer> procGuardianAgeSet = new HashSet<>();

        for (InquiryOptionAreaConfigRespDto item : inquiryOptionAreaConfigRespDtoList) {
            if (item.getProcGuardianAge() == null) {
                procGuardianAgeSet.clear();
                break;
            }
            procGuardianAgeSet.add(item.getProcGuardianAge());
        }

        if (CollUtil.isNotEmpty(procGuardianAgeSet) && procGuardianAgeSet.size() == 1) {
            result.setProcGuardianAge(procGuardianAgeSet.iterator().next());
        }

    }

    /**
     * 构建通用拓展字段
     *
     * @param inquiryOptionAreaConfigRespDtoList
     * @return
     */
    private InquiryOptionAreaConfigRespDto buildCommonExt(List<InquiryOptionAreaConfigRespDto> inquiryOptionAreaConfigRespDtoList) {

        InquiryOptionAreaConfigRespDto result = InquiryOptionAreaConfigRespDto.builder().build();

        Set<String> optionValueSet = new HashSet<>();
        for (InquiryOptionAreaConfigRespDto item : inquiryOptionAreaConfigRespDtoList) {
            if (StringUtils.isBlank(item.getOptionValue())) {
                optionValueSet.clear();
                break;
            }
            optionValueSet.add(item.getOptionValue());
        }

        if (CollUtil.isNotEmpty(optionValueSet) && optionValueSet.size() == 1) {
            result.setOptionValue(optionValueSet.iterator().next());
        }

        return result;
    }

    /**
     * 构建身份证必填拓展字段
     *
     * @param inquiryOptionAreaConfigRespDtoList
     * @return
     */
    private void buildProcIdentityRequiredExt(InquiryOptionAreaConfigRespDto result, List<InquiryOptionAreaConfigRespDto> inquiryOptionAreaConfigRespDtoList) {

        Set<AreaInquiryHospitalTypeDto> hospitalTypeDtos = inquiryOptionAreaConfigRespDtoList.stream()
            .flatMap(i -> Stream.of(i.getAreaInquiryHospitalTypes()).filter(CollUtil::isNotEmpty).flatMap(Collection::stream))
            .collect(Collectors.toSet());

        // 设置医院集合
        if (CollUtil.isEmpty(hospitalTypeDtos)) {
            return;
        }

        Map<String, InquiryHospitalRespDto> hospitalRespDtoMap = inquiryHospitalApi.getInquiryHospitals(
                InquiryHospitalReqDto.builder().inquiryHospitalPrefs(hospitalTypeDtos.stream().map(AreaInquiryHospitalTypeDto::getPref).toList()).disable(null).build())
            .stream().collect(Collectors.toMap(InquiryHospitalRespDto::getPref, Function.identity(), (a, b) -> b));

        // hospitalTypeDtos 按照 pref 合并，将types去重合并到一起 返回最终的一个  List<AreaInquiryHospitalTypeDto>
        List<AreaInquiryHospitalTypeDto> mergedHospitalTypeDtos = hospitalTypeDtos.stream()
            .collect(Collectors.groupingBy(AreaInquiryHospitalTypeDto::getPref,
                Collectors.collectingAndThen(
                    Collectors.flatMapping(dto -> dto.getTypes() != null ? dto.getTypes().stream() : Stream.empty(), Collectors.toSet()), ArrayList::new)))
            .entrySet().stream().map(entry -> new AreaInquiryHospitalTypeDto(entry.getKey(), hospitalRespDtoMap.getOrDefault(entry.getKey(), new InquiryHospitalRespDto()).getName(), entry.getValue()))
            .toList();

        result.setAreaInquiryHospitalTypes(mergedHospitalTypeDtos);
    }

    /**
     * 构建视频问诊拓展字段
     *
     * @param inquiryOptionAreaConfigRespDtoList
     * @return
     */
    private void buildProcVideoInquiryExt(InquiryOptionAreaConfigRespDto result, List<InquiryOptionAreaConfigRespDto> inquiryOptionAreaConfigRespDtoList) {

        Set<Integer> procVideoInquiryRatioSet = new HashSet<>();
        for (InquiryOptionAreaConfigRespDto item : inquiryOptionAreaConfigRespDtoList) {
            if (item.getProcVideoInquiryRatio() == null) {
                procVideoInquiryRatioSet.clear();
                break;
            }
            procVideoInquiryRatioSet.add(item.getProcVideoInquiryRatio());
        }

        if (CollUtil.isNotEmpty(procVideoInquiryRatioSet) && procVideoInquiryRatioSet.size() == 1) {
            result.setProcVideoInquiryRatio(procVideoInquiryRatioSet.iterator().next());
        }

    }

    /**
     * 构建图文全真人问诊拓展字段
     *
     * @param inquiryOptionAreaConfigRespDtoList
     * @return
     */
    private void buildPresAllRealPeopleInquiryExt(InquiryOptionAreaConfigRespDto result, List<InquiryOptionAreaConfigRespDto> inquiryOptionAreaConfigRespDtoList) {

        Set<String> hospitalPrefSet = new HashSet<>();
        Set<Integer> effectiveTimeTypeSet = new HashSet<>();
        Set<Integer> presAllRealPeopleInquiryRatioSet = new HashSet<>();
        for (InquiryOptionAreaConfigRespDto item : inquiryOptionAreaConfigRespDtoList) {

            if (CollUtil.isNotEmpty(item.getPresAllRealPeopleInquiryHospitalPrefs())) {
                hospitalPrefSet.addAll(item.getPresAllRealPeopleInquiryHospitalPrefs());
            }

            if (item.getPresAllRealPeopleInquiryTimeConfig() != null && item.getPresAllRealPeopleInquiryTimeConfig().getEffectiveTimeType() != null) {
                effectiveTimeTypeSet.add(item.getPresAllRealPeopleInquiryTimeConfig().getEffectiveTimeType());
            }

            presAllRealPeopleInquiryRatioSet.add(item.getPresAllRealPeopleInquiryRatio() != null ? item.getPresAllRealPeopleInquiryRatio() : -1);

        }

        // 设置医院集合
        if (CollUtil.isNotEmpty(hospitalPrefSet)) {
            List<InquiryHospitalRespDto> inquiryHospitals = inquiryHospitalApi.getInquiryHospitals(InquiryHospitalReqDto.builder().inquiryHospitalPrefs(Lists.newArrayList(hospitalPrefSet)).disable(null).build());
            List<InquiryCommonPrefAndNameDto> inquiryCommonPrefAndNameDtoList = inquiryHospitals.stream().map(item -> InquiryCommonPrefAndNameDto.builder().pref(item.getPref()).name(item.getName()).build()).toList();
            result.setPresAllRealPeopleInquiryHospitals(inquiryCommonPrefAndNameDtoList);
        }

        // 设置比例
        if (CollUtil.isNotEmpty(presAllRealPeopleInquiryRatioSet) && presAllRealPeopleInquiryRatioSet.size() == 1 && !presAllRealPeopleInquiryRatioSet.contains(-1)) {
            result.setPresAllRealPeopleInquiryRatio(presAllRealPeopleInquiryRatioSet.iterator().next());
        }

        // 设置时间配置
        // 比较inquiryOptionAreaConfigRespDtoList中每一个presAllRealPeopleInquiryTimeConfig对象内容是否一致(需要考虑顺序)
        boolean isTimeConfigConsistent = isPresAllRealPeopleInquiryTimeConfigConsistent(inquiryOptionAreaConfigRespDtoList);
        if (isTimeConfigConsistent) {
            // 如果所有配置一致，则使用第一个配置作为统一配置
            result.setPresAllRealPeopleInquiryTimeConfig(inquiryOptionAreaConfigRespDtoList.getFirst().getPresAllRealPeopleInquiryTimeConfig());
        } else if (CollUtil.isNotEmpty(effectiveTimeTypeSet) && effectiveTimeTypeSet.size() == 1) {
            result.setPresAllRealPeopleInquiryTimeConfig(PresAllRealPeopleInquiryTimeConfigDto.builder().effectiveTimeType(effectiveTimeTypeSet.iterator().next()).build());
        }

    }

    /**
     * 构建药品指定医生拓展字段
     *
     * @param inquiryOptionAreaConfigRespDtoList
     * @return
     */
    private void buildProcInquiryProductDoctorExt(InquiryOptionAreaConfigRespDto result, List<InquiryOptionAreaConfigRespDto> inquiryOptionAreaConfigRespDtoList) {

        Set<String> productNames = inquiryOptionAreaConfigRespDtoList.stream().flatMap(i -> Stream.of(i.getAreaInquiryProductNames()).filter(CollUtil::isNotEmpty).flatMap(Collection::stream))
            .collect(Collectors.toSet());
        result.setAreaInquiryProductNames(new ArrayList<>(productNames));

        List<AreaInquiryDoctorDeptDto> doctorDeptDtos = inquiryOptionAreaConfigRespDtoList.stream().flatMap(i -> Stream.of(i.getAreaInquiryDoctorDeptPrefs()).filter(CollUtil::isNotEmpty).flatMap(Collection::stream))
            .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(doctorDeptDtos)) {

            Map<String, InquiryDoctorDto> hospitalRespDtoMap = inquiryDoctorApi.getInquiryDoctorByPrefList(
                    doctorDeptDtos.stream().map(AreaInquiryDoctorDeptDto::getPref).toList())
                .stream().collect(Collectors.toMap(InquiryDoctorDto::getPref, Function.identity(), (a, b) -> b));

            // doctorDeptDtos 根据医生pref分组，如果医生pref相同，但deptPref存在多个，则置空，否则取第一个
            List<AreaInquiryDoctorDeptDto> values = doctorDeptDtos.stream()
                .collect(Collectors.groupingBy(AreaInquiryDoctorDeptDto::getPref))
                .values().stream().map(list -> {
                    long distinctDeptCount = list.stream()
                        .map(AreaInquiryDoctorDeptDto::getDeptPref)
                        .distinct()
                        .count();
                    if (distinctDeptCount <= 0) {
                        return null;
                    }
                    AreaInquiryDoctorDeptDto first = list.getFirst();
                    if (distinctDeptCount <= 1) {
                        return first;
                    }
                    // 检查同一医生pref下是否有多个不同的deptPref 如果只有一个deptPref或者多个但都相同，则取第一个；否则返回null表示置空
                    return new AreaInquiryDoctorDeptDto().setPref(first.getPref()).setName(hospitalRespDtoMap.get(first.getPref()).getName());
                }).filter(Objects::nonNull).collect(Collectors.toList());
            result.setAreaInquiryDoctorDeptPrefs(values);
        }
    }

    /**
     * 判断列表中所有presAllRealPeopleInquiryTimeConfig对象内容是否一致 使用JSON序列化比较，先对对象进行排序确保一致性
     *
     * @param inquiryOptionAreaConfigRespDtoList 配置列表
     * @return true-一致，false-不一致
     */
    private boolean isPresAllRealPeopleInquiryTimeConfigConsistent(List<InquiryOptionAreaConfigRespDto> inquiryOptionAreaConfigRespDtoList) {
        if (CollUtil.isEmpty(inquiryOptionAreaConfigRespDtoList)) {
            return true;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        String baseConfigJson = null;

        try {
            // 获取第一个配置作为基准，进行排序后序列化
            PresAllRealPeopleInquiryTimeConfigDto baseConfig = inquiryOptionAreaConfigRespDtoList.get(0).getPresAllRealPeopleInquiryTimeConfig();
            baseConfigJson = serializeTimeConfigWithSort(baseConfig, objectMapper);

            // 比较其他配置是否与基准一致
            for (int i = 1; i < inquiryOptionAreaConfigRespDtoList.size(); i++) {
                PresAllRealPeopleInquiryTimeConfigDto currentConfig = inquiryOptionAreaConfigRespDtoList.get(i).getPresAllRealPeopleInquiryTimeConfig();
                String currentConfigJson = serializeTimeConfigWithSort(currentConfig, objectMapper);

                if (!Objects.equals(baseConfigJson, currentConfigJson)) {
                    return false;
                }
            }
        } catch (JsonProcessingException e) {
            // 序列化失败时，降级使用原有的逐字段比较方式
            return isPresAllRealPeopleInquiryTimeConfigConsistentFallback(inquiryOptionAreaConfigRespDtoList);
        }

        return true;
    }

    /**
     * 对时间配置进行排序后序列化为JSON字符串
     *
     * @param config       时间配置
     * @param objectMapper JSON序列化器
     * @return 排序后的JSON字符串
     * @throws JsonProcessingException 序列化异常
     */
    private String serializeTimeConfigWithSort(PresAllRealPeopleInquiryTimeConfigDto config, ObjectMapper objectMapper) throws JsonProcessingException {
        if (config == null) {
            return "null";
        }

        // 创建配置副本进行排序，避免修改原对象
        PresAllRealPeopleInquiryTimeConfigDto sortedConfig = new PresAllRealPeopleInquiryTimeConfigDto();
        sortedConfig.setEffectiveTimeType(config.getEffectiveTimeType());

        if (config.getTimeRanges() != null) {
            List<PresAllRealPeopleInquiryTimeConfigDto.TimeRangeConfig> sortedTimeRanges = config.getTimeRanges().stream()
                .map(this::sortTimeRangeConfig)
                .sorted(this::compareTimeRangeConfig)
                .collect(Collectors.toList());
            sortedConfig.setTimeRanges(sortedTimeRanges);
        }

        return objectMapper.writeValueAsString(sortedConfig);
    }

    /**
     * 对单个时间范围配置进行排序
     *
     * @param timeRange 时间范围配置
     * @return 排序后的时间范围配置
     */
    private PresAllRealPeopleInquiryTimeConfigDto.TimeRangeConfig sortTimeRangeConfig(PresAllRealPeopleInquiryTimeConfigDto.TimeRangeConfig timeRange) {
        if (timeRange == null) {
            return null;
        }

        PresAllRealPeopleInquiryTimeConfigDto.TimeRangeConfig sortedTimeRange = new PresAllRealPeopleInquiryTimeConfigDto.TimeRangeConfig();

        // 对weekDays进行排序
        if (timeRange.getWeekDays() != null) {
            List<Integer> sortedWeekDays = timeRange.getWeekDays().stream()
                .sorted()
                .collect(Collectors.toList());
            sortedTimeRange.setWeekDays(sortedWeekDays);
        }

        // 对timeSlots进行排序（按开始时间排序）
        if (timeRange.getTimeSlots() != null) {
            List<PresAllRealPeopleInquiryTimeConfigDto.TimeSlot> sortedTimeSlots = timeRange.getTimeSlots().stream()
                .sorted(Comparator.comparing(PresAllRealPeopleInquiryTimeConfigDto.TimeSlot::getStartTime, Comparator.nullsLast(String::compareTo))
                    .thenComparing(PresAllRealPeopleInquiryTimeConfigDto.TimeSlot::getEndTime, Comparator.nullsLast(String::compareTo)))
                .collect(Collectors.toList());
            sortedTimeRange.setTimeSlots(sortedTimeSlots);
        }

        return sortedTimeRange;
    }

    /**
     * 比较两个时间范围配置的排序顺序
     *
     * @param tr1 时间范围配置1
     * @param tr2 时间范围配置2
     * @return 比较结果
     */
    private int compareTimeRangeConfig(PresAllRealPeopleInquiryTimeConfigDto.TimeRangeConfig tr1, PresAllRealPeopleInquiryTimeConfigDto.TimeRangeConfig tr2) {
        if (tr1 == tr2) {
            return 0;
        }
        if (tr1 == null) {
            return -1;
        }
        if (tr2 == null) {
            return 1;
        }

        // 先比较weekDays的第一个元素（如果存在）
        Integer firstWeekDay1 = (tr1.getWeekDays() != null && !tr1.getWeekDays().isEmpty()) ? tr1.getWeekDays().get(0) : null;
        Integer firstWeekDay2 = (tr2.getWeekDays() != null && !tr2.getWeekDays().isEmpty()) ? tr2.getWeekDays().get(0) : null;

        int weekDayCompare = Comparator.nullsLast(Integer::compareTo).compare(firstWeekDay1, firstWeekDay2);
        if (weekDayCompare != 0) {
            return weekDayCompare;
        }

        // 再比较timeSlots的第一个元素的开始时间（如果存在）
        String firstStartTime1 = (tr1.getTimeSlots() != null && !tr1.getTimeSlots().isEmpty()) ? tr1.getTimeSlots().get(0).getStartTime() : null;
        String firstStartTime2 = (tr2.getTimeSlots() != null && !tr2.getTimeSlots().isEmpty()) ? tr2.getTimeSlots().get(0).getStartTime() : null;

        return Comparator.nullsLast(String::compareTo).compare(firstStartTime1, firstStartTime2);
    }

    /**
     * 降级方案：使用原有的逐字段比较方式
     *
     * @param inquiryOptionAreaConfigRespDtoList 配置列表
     * @return true-一致，false-不一致
     */
    private boolean isPresAllRealPeopleInquiryTimeConfigConsistentFallback(List<InquiryOptionAreaConfigRespDto> inquiryOptionAreaConfigRespDtoList) {
        if (CollUtil.isEmpty(inquiryOptionAreaConfigRespDtoList)) {
            return true;
        }

        // 获取第一个配置作为基准
        PresAllRealPeopleInquiryTimeConfigDto baseConfig = inquiryOptionAreaConfigRespDtoList.get(0).getPresAllRealPeopleInquiryTimeConfig();

        // 比较其他配置是否与基准一致
        for (int i = 1; i < inquiryOptionAreaConfigRespDtoList.size(); i++) {
            PresAllRealPeopleInquiryTimeConfigDto currentConfig = inquiryOptionAreaConfigRespDtoList.get(i).getPresAllRealPeopleInquiryTimeConfig();
            if (!isTimeConfigEqual(baseConfig, currentConfig)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 比较两个时间配置是否相等（降级方案使用）
     *
     * @param config1 配置1
     * @param config2 配置2
     * @return true-相等，false-不相等
     */
    private boolean isTimeConfigEqual(PresAllRealPeopleInquiryTimeConfigDto config1, PresAllRealPeopleInquiryTimeConfigDto config2) {
        if (config1 == config2) {
            return true;
        }
        if (config1 == null || config2 == null) {
            return false;
        }

        // 比较effectiveTimeType
        if (!Objects.equals(config1.getEffectiveTimeType(), config2.getEffectiveTimeType())) {
            return false;
        }

        // 比较timeRanges
        return isTimeRangeConfigListEqual(config1.getTimeRanges(), config2.getTimeRanges());
    }

    /**
     * 比较两个时间范围配置列表是否相等（考虑顺序）
     *
     * @param list1 列表1
     * @param list2 列表2
     * @return true-相等，false-不相等
     */
    private boolean isTimeRangeConfigListEqual(List<PresAllRealPeopleInquiryTimeConfigDto.TimeRangeConfig> list1,
        List<PresAllRealPeopleInquiryTimeConfigDto.TimeRangeConfig> list2) {
        if (list1 == list2) {
            return true;
        }
        if (list1 == null || list2 == null) {
            return false;
        }
        if (list1.size() != list2.size()) {
            return false;
        }

        for (int i = 0; i < list1.size(); i++) {
            if (!isTimeRangeConfigEqual(list1.get(i), list2.get(i))) {
                return false;
            }
        }

        return true;
    }

    /**
     * 比较两个时间范围配置是否相等
     *
     * @param config1 配置1
     * @param config2 配置2
     * @return true-相等，false-不相等
     */
    private boolean isTimeRangeConfigEqual(PresAllRealPeopleInquiryTimeConfigDto.TimeRangeConfig config1,
        PresAllRealPeopleInquiryTimeConfigDto.TimeRangeConfig config2) {
        if (config1 == config2) {
            return true;
        }
        if (config1 == null || config2 == null) {
            return false;
        }

        // 比较weekDays
        if (!isIntegerListEqual(config1.getWeekDays(), config2.getWeekDays())) {
            return false;
        }

        // 比较timeSlots
        return isTimeSlotListEqual(config1.getTimeSlots(), config2.getTimeSlots());
    }

    /**
     * 比较两个时间段列表是否相等（考虑顺序）
     *
     * @param list1 列表1
     * @param list2 列表2
     * @return true-相等，false-不相等
     */
    private boolean isTimeSlotListEqual(List<PresAllRealPeopleInquiryTimeConfigDto.TimeSlot> list1,
        List<PresAllRealPeopleInquiryTimeConfigDto.TimeSlot> list2) {
        if (list1 == list2) {
            return true;
        }
        if (list1 == null || list2 == null) {
            return false;
        }
        if (list1.size() != list2.size()) {
            return false;
        }

        for (int i = 0; i < list1.size(); i++) {
            if (!isTimeSlotEqual(list1.get(i), list2.get(i))) {
                return false;
            }
        }

        return true;
    }

    /**
     * 比较两个时间段是否相等
     *
     * @param slot1 时间段1
     * @param slot2 时间段2
     * @return true-相等，false-不相等
     */
    private boolean isTimeSlotEqual(PresAllRealPeopleInquiryTimeConfigDto.TimeSlot slot1,
        PresAllRealPeopleInquiryTimeConfigDto.TimeSlot slot2) {
        if (slot1 == slot2) {
            return true;
        }
        if (slot1 == null || slot2 == null) {
            return false;
        }

        return Objects.equals(slot1.getStartTime(), slot2.getStartTime()) &&
            Objects.equals(slot1.getEndTime(), slot2.getEndTime());
    }

    /**
     * 比较两个整数列表是否相等（考虑顺序）
     *
     * @param list1 列表1
     * @param list2 列表2
     * @return true-相等，false-不相等
     */
    private boolean isIntegerListEqual(List<Integer> list1, List<Integer> list2) {
        if (list1 == list2) {
            return true;
        }
        if (list1 == null || list2 == null) {
            return false;
        }
        if (list1.size() != list2.size()) {
            return false;
        }

        for (int i = 0; i < list1.size(); i++) {
            if (!Objects.equals(list1.get(i), list2.get(i))) {
                return false;
            }
        }

        return true;
    }

    /**
     * 填充医院相关的名称字段
     *
     * @param respDto          响应DTO
     * @param inquiryHospitals 医院信息列表
     */
    private void fillHospitalNames(InquiryOptionAreaConfigRespDto respDto, List<InquiryHospitalRespDto> inquiryHospitals) {

        // 处理新格式的医院类型字段，生成拼接展示文案
        if (CollUtil.isNotEmpty(respDto.getAreaInquiryHospitalTypes())) {
            List<String> hospitalTypeNames = respDto.getAreaInquiryHospitalTypes().stream()
                .map(hospitalType -> {
                    String hospitalName = inquiryHospitals.stream()
                        .filter(hospital -> hospital.getPref().equals(hospitalType.getPref()))
                        .map(InquiryHospitalRespDto::getName)
                        .findFirst()
                        .orElse(hospitalType.getPref()); // 如果找不到名称，使用编码

                    return formatHospitalNameWithTypes(hospitalName, hospitalType.getTypes());
                })
                .toList();
            respDto.setAreaInquiryHospitalNames(hospitalTypeNames);
            return;
        }

        // 处理旧格式的医院名称
        if (CollUtil.isNotEmpty(respDto.getAreaInquiryHospitalPrefs())) {
            respDto.setAreaInquiryHospitalNames(inquiryHospitals.stream()
                .filter(a -> respDto.getAreaInquiryHospitalPrefs().contains(a.getPref()))
                .map(InquiryHospitalRespDto::getName).toList());
        }
    }

    /**
     * 处理医院类型字段的兼容性 优先使用新格式，如果没有则从旧格式转换
     *
     * @param respDto 响应DTO
     */
    private void handleHospitalTypeCompatibility(InquiryOptionAreaConfigRespDto respDto) {
        // 如果新格式为空但旧格式有值，则从旧格式转换
        if (CollUtil.isEmpty(respDto.getAreaInquiryHospitalTypes()) &&
            CollUtil.isNotEmpty(respDto.getAreaInquiryHospitalPrefs())) {

            List<com.xyy.saas.inquiry.drugstore.api.option.dto.AreaInquiryHospitalTypeDto> hospitalTypes =
                respDto.getAreaInquiryHospitalPrefs().stream()
                    .map(pref -> new com.xyy.saas.inquiry.drugstore.api.option.dto.AreaInquiryHospitalTypeDto(pref, null))
                    .toList();
            respDto.setAreaInquiryHospitalTypes(hospitalTypes);
        }
    }

    /**
     * 处理医生科室字段的兼容性 优先使用新格式，如果没有则从旧格式转换
     *
     * @param respDto 响应DTO
     */
    private void handleDoctorDeptCompatibility(InquiryOptionAreaConfigRespDto respDto) {
        // 如果新格式为空但旧格式有值，则从旧格式转换
        if (CollUtil.isEmpty(respDto.getAreaInquiryDoctorDeptPrefs()) &&
            CollUtil.isNotEmpty(respDto.getAreaInquiryDoctorPrefs())) {

            List<com.xyy.saas.inquiry.drugstore.api.option.dto.AreaInquiryDoctorDeptDto> doctorDepts =
                respDto.getAreaInquiryDoctorPrefs().stream()
                    .map(pref -> new com.xyy.saas.inquiry.drugstore.api.option.dto.AreaInquiryDoctorDeptDto(pref, null))
                    .toList();
            respDto.setAreaInquiryDoctorDeptPrefs(doctorDepts);
        }
    }

    /**
     * 生成带类型的医院名称显示 格式：医院名称(类型1,类型2)
     *
     * @param hospitalName 医院名称
     * @param types        类型列表
     * @return 格式化后的名称
     */
    private String formatHospitalNameWithTypes(String hospitalName, List<Integer> types) {
        if (CollUtil.isEmpty(types)) {
            return hospitalName;
        }
        // dictDataApi.getDictDataList(DictTypeConstants.PRESCRIPTION_TYPE);
        String typeText = types.stream()
            .map(type -> {
                if (Objects.equals(type, -1)) {
                    return "无处方类型";
                }
                return DictFrameworkUtils.getDictDataLabel(DictTypeConstants.PRESCRIPTION_TYPE, String.valueOf(type));
            }).collect(Collectors.joining("、"));

        return hospitalName + "(" + typeText + ")";
    }

    /**
     * 填充通用的医院信息
     * <p>
     * 处理所有包含医院字段的选项类型，包括： - areaInquiryHospitalPrefs/areaInquiryHospitalNames - areaInquiryHospitalTypes/areaInquiryHospitalTypeNames - presAllRealPeopleInquiryHospitalPrefs/presAllRealPeopleInquiryHospitalPrefNames
     *
     * @param list 响应DTO列表
     */
    private void fillCommonHospitalInfo(List<InquiryOptionAreaConfigRespDto> list, List<InquiryOptionStoreConfigRespDto> storeList) {
        if (CollUtil.isEmpty(list) && CollUtil.isEmpty(storeList)) {
            return;
        }
        try {
            // 收集所有医院编码
            Set<String> allHospitalPrefs;
            if (CollUtil.isNotEmpty(list)) {
                allHospitalPrefs = list.stream()
                    .flatMap(respDto -> Stream.of(
                        respDto.getPresAllRealPeopleInquiryHospitalPrefs(),
                        respDto.compatibleAreaInquiryHospitalPrefs()
                    ).filter(CollUtil::isNotEmpty).flatMap(Collection::stream))
                    .collect(Collectors.toSet());

                if (CollUtil.isEmpty(allHospitalPrefs)) {
                    return;
                }
            } else {
                allHospitalPrefs = storeList.stream()
                    .flatMap(respDto -> Stream.of(
                        Optional.ofNullable(respDto.getAreaInquiryHospitalTypes()).orElse(List.of()).stream().map(AreaInquiryHospitalTypeDto::getPref).toList()
                    ).filter(CollUtil::isNotEmpty).flatMap(Collection::stream))
                    .collect(Collectors.toSet());
            }

            // 批量查询医院信息
            List<InquiryHospitalRespDto> inquiryHospitals = inquiryHospitalApi.getInquiryHospitals(
                InquiryHospitalReqDto.builder()
                    .inquiryHospitalPrefs(new ArrayList<>(allHospitalPrefs))
                    .disable(null)
                    .build()
            );

            if (CollUtil.isEmpty(inquiryHospitals)) {
                return;
            }

            if (CollUtil.isNotEmpty(list)) {
                // 为每个响应DTO填充医院相关字段
                for (InquiryOptionAreaConfigRespDto respDto : list) {
                    // 填充图文全真人问诊医院名称
                    if (CollUtil.isNotEmpty(respDto.getPresAllRealPeopleInquiryHospitalPrefs())) {
                        List<String> hospitalNames = inquiryHospitals.stream()
                            .filter(hospital -> respDto.getPresAllRealPeopleInquiryHospitalPrefs().contains(hospital.getPref()))
                            .map(InquiryHospitalRespDto::getName)
                            .toList();
                        respDto.setPresAllRealPeopleInquiryHospitalPrefNames(hospitalNames);
                    }

                    // 填充区域医院相关的名称字段
                    fillHospitalNames(respDto, inquiryHospitals);
                }
            }

            if (CollUtil.isNotEmpty(storeList)) {
                storeList.forEach(respDto -> {
                    if (CollUtil.isNotEmpty(respDto.getAreaInquiryHospitalTypes())) {
                        List<String> hospitalTypeNames = respDto.getAreaInquiryHospitalTypes().stream()
                            .map(hospitalType -> {
                                String hospitalName = inquiryHospitals.stream()
                                    .filter(hospital -> hospital.getPref().equals(hospitalType.getPref()))
                                    .map(InquiryHospitalRespDto::getName)
                                    .findFirst()
                                    .orElse(hospitalType.getPref()); // 如果找不到名称，使用编码

                                return formatHospitalNameWithTypes(hospitalName, hospitalType.getTypes());
                            }).toList();
                        respDto.setAreaInquiryHospitalNames(hospitalTypeNames);
                    }
                });
            }

        } catch (Exception e) {
            log.error("填充通用医院信息时发生异常", e);
        }
    }

    /**
     * 填充药品指定医生扩展信息
     * <p>
     * 对应选项类型：PROC_INQUIRY_PRODUCT_DOCTOR (20019) 相关字段：areaInquiryProductNames, areaInquiryDoctorPrefs, areaInquiryDoctorDeptPrefs, areaInquiryDoctorNamePrefs
     * <p>
     * 该方法采用批量查询策略，避免N+1查询问题： 1. 收集所有医生编码，批量查询医生信息 2. 收集所有科室编码，批量查询科室信息 3. 构建映射表，高效填充每个DTO
     *
     * @param list 响应DTO列表，同一批次的数据具有相同的选项类型
     */
    private void fillProcInquiryProductDoctorExt(List<InquiryOptionAreaConfigRespDto> list) {
        if (CollUtil.isEmpty(list)) {
            log.debug("医生扩展信息填充跳过：列表为空");
            return;
        }

        try {
            // 收集所有医生编码
            Set<String> allDoctorPrefs = list.stream()
                .flatMap(respDto -> Stream.of(respDto.compatibleAreaInquiryDoctorPrefs())
                    .filter(CollUtil::isNotEmpty)
                    .flatMap(Collection::stream))
                .collect(Collectors.toSet());

            if (CollUtil.isEmpty(allDoctorPrefs)) {
                return;
            }

            // 批量查询医生信息
            List<InquiryDoctorDto> doctors = inquiryDoctorApi.getInquiryDoctorByPrefList(new ArrayList<>(allDoctorPrefs));
            if (CollUtil.isEmpty(doctors)) {
                return;
            }
            // 构建医生编码到医生信息的映射
            Map<String, InquiryDoctorDto> doctorMap = doctors.stream()
                .collect(Collectors.toMap(InquiryDoctorDto::getPref, Function.identity()));

            // 批量构建科室编码到名称的映射
            Map<String, String> deptPrefToNameMap = buildDeptPrefToNameMapBatch(list);

            // 为每个响应DTO填充医生名称
            for (InquiryOptionAreaConfigRespDto respDto : list) {
                fillSingleDoctorExtInfo(respDto, doctorMap, deptPrefToNameMap);
            }
        } catch (Exception e) {
            log.error("批量填充医生扩展信息时发生异常", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 批量构建科室编码到名称的映射
     * <p>
     * 从所有DTO中收集科室编码，批量查询科室信息并构建映射表
     *
     * @param list 响应DTO列表
     * @return 科室编码到名称的映射，如果无科室信息则返回空Map
     */
    private Map<String, String> buildDeptPrefToNameMapBatch(List<InquiryOptionAreaConfigRespDto> list) {
        try {
            // 收集所有科室编码
            Set<String> allDeptPrefs = list.stream()
                .filter(respDto -> CollUtil.isNotEmpty(respDto.getAreaInquiryDoctorDeptPrefs()))
                .flatMap(respDto -> respDto.getAreaInquiryDoctorDeptPrefs().stream())
                .map(AreaInquiryDoctorDeptDto::getDeptPref)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

            if (CollUtil.isEmpty(allDeptPrefs)) {
                log.debug("无科室编码需要查询");
                return Collections.emptyMap();
            }

            log.debug("开始批量查询科室信息，科室编码数量: {}", allDeptPrefs.size());

            // 批量查询科室信息
            List<InquiryDeptDto> deptList = inquiryDeptApi.listInquiryDeptByPref(new ArrayList<>(allDeptPrefs));

            if (CollUtil.isEmpty(deptList)) {
                log.warn("批量查询科室信息返回空结果，科室编码: {}", allDeptPrefs);
                return Collections.emptyMap();
            }

            log.debug("成功查询到科室信息数量: {}", deptList.size());

            return deptList.stream()
                .collect(Collectors.toMap(InquiryDeptDto::getPref, InquiryDeptDto::getDeptName, (existing, replacement) -> existing));

        } catch (Exception e) {
            log.error("批量构建科室编码映射时发生异常", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 为单个响应DTO填充医生扩展信息
     * <p>
     * 使用预构建的映射表快速填充医生名称，支持科室名称拼接
     *
     * @param respDto           响应DTO
     * @param doctorMap         医生编码到医生信息的映射
     * @param deptPrefToNameMap 科室编码到名称的映射
     */
    private void fillSingleDoctorExtInfo(InquiryOptionAreaConfigRespDto respDto, Map<String, InquiryDoctorDto> doctorMap, Map<String, String> deptPrefToNameMap) {
        List<String> compatibleDoctorPrefs = respDto.compatibleAreaInquiryDoctorPrefs();
        if (CollUtil.isEmpty(compatibleDoctorPrefs)) {
            return;
        }

        try {
            // 填充医生名称，如果有科室信息则拼接
            List<InquiryCommonPrefAndNameDto> doctorNamePrefs = compatibleDoctorPrefs.stream()
                .map(doctorMap::get)
                .filter(Objects::nonNull)
                .map(doctor -> InquiryCommonPrefAndNameDto.builder()
                    .pref(doctor.getPref())
                    .name(buildDoctorNameWithDept(doctor, respDto, deptPrefToNameMap))
                    .build())
                .toList();

            respDto.setAreaInquiryDoctorNamePrefs(doctorNamePrefs);
            respDto.setAreaInquiryDoctorPrefs(doctorNamePrefs.stream().map(InquiryCommonPrefAndNameDto::getName).collect(Collectors.toList()));
        } catch (Exception e) {
            log.error("填充单个医生扩展信息时发生异常，医生编码: {}", compatibleDoctorPrefs, e);
            // 设置空列表，避免null值
            respDto.setAreaInquiryDoctorNamePrefs(Collections.emptyList());
        }
    }

    /**
     * 构建带科室信息的医生名称
     *
     * @param doctor            医生信息
     * @param respDto           响应DTO
     * @param deptPrefToNameMap 科室编码到名称的映射
     * @return 医生名称（可能包含科室信息）
     */
    private String buildDoctorNameWithDept(InquiryDoctorDto doctor, InquiryOptionAreaConfigRespDto respDto, Map<String, String> deptPrefToNameMap) {
        String doctorName = doctor.getName() + "(" + doctor.getPref() + ")";

        // 如果使用新格式且有科室信息，则拼接科室名称
        if (CollUtil.isNotEmpty(respDto.getAreaInquiryDoctorDeptPrefs())) {
            String deptName = respDto.getAreaInquiryDoctorDeptPrefs().stream()
                .filter(deptDto -> Objects.equals(deptDto.getPref(), doctor.getPref()))
                .map(AreaInquiryDoctorDeptDto::getDeptPref)
                .filter(StringUtils::isNotBlank)
                .map(deptPrefToNameMap::get)
                .filter(StringUtils::isNotBlank)
                .findFirst()
                .orElse(null);

            if (StringUtils.isNotBlank(deptName)) {
                doctorName = doctorName + "-" + deptName;
            }
        }

        return doctorName;
    }
}