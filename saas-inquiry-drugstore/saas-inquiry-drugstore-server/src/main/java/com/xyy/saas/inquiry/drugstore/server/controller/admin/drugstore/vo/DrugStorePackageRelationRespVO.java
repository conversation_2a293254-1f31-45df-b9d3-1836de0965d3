package com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageEffectiveStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 门店套餐订单 DO
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ExcelIgnoreUnannotated
public class DrugStorePackageRelationRespVO implements Serializable {

    @ExcelProperty(value = "订单编号")
    private String pref;

    @ExcelProperty(value = "门店名称")
    private String tenantName;

    @ExcelProperty(value = "门店编码")
    private String tenantPref;

    @ExcelProperty(value = "省")
    private String province;

    @ExcelProperty(value = "市")
    private String city;

    @ExcelProperty(value = "区")
    private String area;

    @ExcelProperty(value = "系统类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.SYSTEM_BIZ_TYPE)
    private Integer bizType;

    @ExcelProperty(value = "问诊类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.INQUIRY_BIZ_TYPE)
    private Integer inquiryBizType;

    @ExcelProperty(value = "套餐名")
    private String packageName;

    @ExcelProperty(value = "套餐编码")
    private String packagePref;

    @ExcelProperty(value = "套餐额度")
    private String inquiryPackageItemStr;

    @ExcelProperty(value = "剩余额度")
    private String surplusCostStr;

    @ExcelProperty(value = "问诊医院")
    private String hospitalName;

    @ExcelProperty(value = "套餐状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.TENANT_PACKAGE_EFFECTIVE_STATUS)
    private Integer effective;

    @ExcelProperty(value = "审方类型", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.INQUIRY_AUDIT_TYPE)
    private Integer inquiryAuditType;

    @ExcelProperty(value = "套餐时限")
    private String termStr;

    @ExcelProperty(value = "套餐定价")
    private BigDecimal price;

    @ExcelProperty(value = "订单状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.TENANT_PACKAGE_RELATION_STATUS)
    private Integer status;

    @ExcelProperty(value = "套餐性质", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.TENANT_PACKAGE_RELATION_NATURE)
    private Integer packageNature;

    @ExcelProperty(value = "生效时间")
    private LocalDateTime startTime;

    @ExcelProperty(value = "失效时间")
    private LocalDateTime endTime;

    @ExcelProperty(value = "签约日期")
    private LocalDateTime signTime;


    @ExcelProperty(value = "开通时间")
    private LocalDateTime createTime;

    @ExcelProperty(value = "收款方式", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.TENANT_PACKAGE_RELATION_PAYMENT_TYPE)
    private Integer paymentType;

    @ExcelProperty(value = "签约人")
    private String signUser;

    @ExcelProperty(value = "代理人")
    private String proxyUser;

    @ExcelProperty(value = "创建人")
    private String creator;

    @ExcelProperty(value = "签约渠道", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.TENANT_PACKAGE_RELATION_SIGN_CHANNEL)
    private Integer signChannel;

    @ExcelProperty(value = "实收金额")
    private BigDecimal actualAmount;

    @ExcelProperty(value = "收款账户", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.TENANT_PACKAGE_RELATION_COLLECT_ACCOUNT)
    private Integer collectAccount;


    @ExcelProperty(value = "付款流水号")
    private String payNo;

    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "状态变更备注")
    private String statusRemark;


    /**
     * 套餐额度
     */
    private List<InquiryPackageItem> inquiryPackageItems;

    /**
     * 问诊套餐包关联医院pref
     */
    private List<String> hospitalPrefs;

    /**
     * 套餐时限
     */
    private Integer term;

    /**
     * 时限类型 {@link com.xyy.saas.inquiry.enums.tenant.DateTermTypeEnum}
     */
    private Integer termType;


    /**
     * 付款凭证url
     */
    private List<String> payVoucherUrls;


    /**
     * 剩余到期天数
     */
    private Long endDay;

    /**
     * 剩余额度
     */
    private List<InquiryPackageItem> surplusCosts;


    public String getInquiryPackageItemStr() {
        return InquiryPackageItem.getInquiryPackageItemStr(getInquiryBizType(), getInquiryPackageItems());
    }

    public String getSurplusCostStr() {
        return InquiryPackageItem.getInquiryPackageItemStr(getInquiryBizType(), getSurplusCosts());
    }

    public Integer getEffective() {
        return TenantPackageEffectiveStatusEnum.getEffectiveStatus(bizType, startTime, endTime, TenantPackageRelationStatusEnum.fromStatusCode(status), surplusCosts);
    }
}