package com.xyy.saas.inquiry.drugstore.server.convert.menu;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.inquiry.drugstore.api.permission.PermissionMatchContext;
import com.xyy.saas.inquiry.drugstore.enums.AppHomeCoreMeunEnum;
import com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo.DrugStoreInquiryPermissionVO;
import com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo.Meun;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: xucao
 * @DateTime: 2025/4/23 14:10
 * @Description: app菜单相关转换器
 **/
@Mapper
public interface MenuConvert {

    MenuConvert INSTANCE = Mappers.getMapper(MenuConvert.class);

    static final String POSITION_TOP = "top";
    static final String POSITION_BOTTOM = "bottom";

    default PermissionMatchContext convert(List<TenantPackageCostDO> packageCost, AdminUserRespDTO user){
        PermissionMatchContext context = new PermissionMatchContext();
        context.setInquiryBizTypeList(packageCost.stream().map(TenantPackageCostDO::getInquiryBizType).distinct().toList().stream().map(InquiryBizTypeEnum::fromCode).toList());
        context.setRoleCodeList(user.getRoleCodes().stream().map(RoleCodeEnum::forCode).toList());
        context.setTenantId(TenantContextHolder.getTenantId());
        return context;
    }

    default DrugStoreInquiryPermissionVO convert(List<InquiryWayTypeEnum> inquiryWayTypes , List<AppHomeCoreMeunEnum> matchedMenus){
        DrugStoreInquiryPermissionVO respVo = DrugStoreInquiryPermissionVO.builder()
            .text(inquiryWayTypes.stream().anyMatch(i -> Objects.equals(i, InquiryWayTypeEnum.TEXT)))
            .video(inquiryWayTypes.stream().anyMatch(i -> Objects.equals(i, InquiryWayTypeEnum.VIDEO)))
            .appHomeCoreMeunItem(matchedMenus.stream().filter(item -> StringUtils.equals(item.getPosition(), POSITION_TOP)).map(this::convert).collect(Collectors.toList()))
            .appHomeAuxMeunItem(matchedMenus.stream().filter(item -> StringUtils.equals(item.getPosition(), POSITION_BOTTOM)).map(this::convert).collect(Collectors.toList()))
            .build();
        matchedMenus.stream().filter(item -> StringUtils.isBlank(item.getPosition())).forEach(item -> checkPosition(item, respVo));
        return respVo;
    }

    default void checkPosition(AppHomeCoreMeunEnum item, DrugStoreInquiryPermissionVO respVo){
        List<Meun> appHomeCoreMeunItem = respVo.getAppHomeCoreMeunItem();
        Meun meun = convert(item);
        // 核心功能入口菜单为空 或者 核心功能入口菜单数量 > 1  此时，动态变换位置的菜单直接放下面
        if(CollectionUtils.isEmpty(appHomeCoreMeunItem) || appHomeCoreMeunItem.size() > 1){
            respVo.getAppHomeAuxMeunItem().add(meun);
            return;
        }
        // 核心功能入口菜单数量 = 1 且 核心功能入口菜单绑定的菜单为当前菜单，则直接放入核心功能入口菜单中
        if(StringUtils.equals(Objects.requireNonNull(AppHomeCoreMeunEnum.fromCode(appHomeCoreMeunItem.getFirst().getCode())).getBound(), item.getCode())){
            respVo.getAppHomeCoreMeunItem().add(meun);
            return;
        }
        // 其他情况下还是放下面
        respVo.getAppHomeAuxMeunItem().add(meun);
    }

    Meun convert(AppHomeCoreMeunEnum item);
}
