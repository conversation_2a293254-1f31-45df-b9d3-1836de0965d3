package com.xyy.saas.inquiry.drugstore.server.service.tutorial;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TUTORIAL_MAINTENANCE_CREATE_FAIL;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TUTORIAL_MAINTENANCE_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.constant.TutorialMaintenanceConstant;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo.TutorialMaintenancePageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo.TutorialMaintenanceRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo.TutorialMaintenanceSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tutorial.TutorialMaintenanceDO;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.tutorial.TutorialMaintenanceMapper;
import com.xyy.saas.inquiry.drugstore.server.util.HtmlUtil;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tutorial.TutorialTypeEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.FileApiUtil;
import com.xyy.saas.inquiry.util.MathUtil;
import com.xyy.saas.inquiry.util.UserUtil;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 教程维护 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TutorialMaintenanceServiceImpl implements TutorialMaintenanceService {

    @Resource
    private TutorialMaintenanceMapper tutorialMaintenanceMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private ConfigApi configApi;

    @Resource
    private TenantApi tenantApi;

    @Override
    public Integer createTutorialMaintenance(TutorialMaintenanceSaveReqVO createReqVO) {

        // 构建教程的html的url
        this.buildTutorialUrl(createReqVO);
        TutorialMaintenanceDO tutorialMaintenance = BeanUtils.toBean(createReqVO, TutorialMaintenanceDO.class);
        tutorialMaintenanceMapper.insert(tutorialMaintenance);
        return tutorialMaintenance.getId();
    }

    /**
     * 构建教程的html的url
     *
     * @param createReqVO
     */
    private void buildTutorialUrl(TutorialMaintenanceSaveReqVO createReqVO) {

        // 根据教程类型处理不同逻辑
        if (!Objects.equals(TutorialTypeEnum.OWN.getCode(), createReqVO.getTutorialType())) {
            return;
        }

        // 平台教程：生成HTML文件
        File tempFile = null;
        try {
            tempFile = File.createTempFile("tutorial_", ".html");
            FileUtil.writeString(HtmlUtil.concatHTML(createReqVO.getContent(), createReqVO.getTitle()), tempFile, StandardCharsets.UTF_8);
            // 上传文件并获取URL
            createReqVO.setTutorialUrl(FileApiUtil.createFileCore("tutorial_" + UUID.randomUUID().toString().replace("-", "") + ".html", null, false, FileUtil.readBytes(tempFile))); // 更新VO中的URL
        } catch (IOException e) {
            throw exception(TUTORIAL_MAINTENANCE_CREATE_FAIL);
        } finally {
            // 确保删除临时文件
            if (tempFile != null && tempFile.exists()) {
                FileUtil.del(tempFile);
            }
        }
    }

    @Override
    public void updateTutorialMaintenance(TutorialMaintenanceSaveReqVO updateReqVO) {
        // 校验存在
        validateTutorialMaintenanceExists(updateReqVO.getId());
        // 构建教程的html的url
        this.buildTutorialUrl(updateReqVO);
        // 更新
        TutorialMaintenanceDO updateObj = BeanUtils.toBean(updateReqVO, TutorialMaintenanceDO.class);
        tutorialMaintenanceMapper.updateById(updateObj);
    }

    @Override
    public void deleteTutorialMaintenance(Integer id) {
        // 校验存在
        validateTutorialMaintenanceExists(id);
        // 删除
        tutorialMaintenanceMapper.deleteById(id);
    }

    private void validateTutorialMaintenanceExists(Integer id) {
        if (tutorialMaintenanceMapper.selectById(id) == null) {
            throw exception(TUTORIAL_MAINTENANCE_NOT_EXISTS);
        }
    }

    @Override
    public TutorialMaintenanceDO getTutorialMaintenance(Integer id) {
        return tutorialMaintenanceMapper.selectById(id);
    }

    @Override
    public PageResult<TutorialMaintenanceRespVO> getTutorialMaintenancePage(TutorialMaintenancePageReqVO pageReqVO) {

        // 当为首页查询时 , 构建一些固定地查询条件
        if (Boolean.TRUE.equals(pageReqVO.getTenantHomePage())) {
            pageReqVO.setPageNo(1);
            pageReqVO.setPageSize(MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(TutorialMaintenanceConstant.TUTORIAL_MAINTENANCE_MAX_SHOW_COUNT), 6));
            pageReqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        }

        // 非运营门店只查询当前业务线数据
        if (!TenantConstant.isSystemTenant()) {
            TenantDto tenantDto = tenantApi.getTenant();
            List<Integer> bizTypeList = new ArrayList<>();
            if (CommonStatusEnum.ENABLE.getStatus().equals(tenantDto.getWzBizTypeStatus())) {
                bizTypeList.add(BizTypeEnum.HYWZ.getCode());
            }
            if (CommonStatusEnum.ENABLE.getStatus().equals(tenantDto.getZhlBizTypeStatus())) {
                bizTypeList.add(BizTypeEnum.ZHL.getCode());
            }
            pageReqVO.setBizTypeList(bizTypeList);
        }

        PageResult<TutorialMaintenanceDO> tutorialMaintenanceDOPageResult = tutorialMaintenanceMapper.selectPage(pageReqVO);

        if (tutorialMaintenanceDOPageResult == null || CollUtil.isEmpty(tutorialMaintenanceDOPageResult.getList())) {
            return new PageResult<>();
        }

        // 清空内容 , 前端不需要此字段内容
        tutorialMaintenanceDOPageResult.getList().forEach(item -> item.setContent(null));

        PageResult<TutorialMaintenanceRespVO> tutorialMaintenanceRespVOPageResult = BeanUtils.toBean(tutorialMaintenanceDOPageResult, TutorialMaintenanceRespVO.class);

        // 补充用户信息
        UserUtil.fillUserInfo(tutorialMaintenanceRespVOPageResult.getList(), adminUserApi::getUserNameMap);

        return tutorialMaintenanceRespVOPageResult;
    }

    @Override
    public Integer getMaxShowCount() {

        return MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(TutorialMaintenanceConstant.TUTORIAL_MAINTENANCE_MAX_SHOW_COUNT), 2);
    }

}