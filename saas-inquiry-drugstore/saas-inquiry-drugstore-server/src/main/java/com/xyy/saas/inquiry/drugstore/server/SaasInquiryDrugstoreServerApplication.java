package com.xyy.saas.inquiry.drugstore.server;

import cn.iocoder.yudao.framework.security.config.YudaoSecurityAutoConfiguration;
import cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration;
import cn.iocoder.yudao.module.infra.api.logger.ApiAccessLogApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;


@EnableDubbo
@EnableDiscoveryClient
@SpringBootApplication(exclude = {
//        YudaoTenantAutoConfiguration.class,
//        YudaoSecurityAutoConfiguration.class,
//        YudaoSecurityAutoConfiguration.class,
}
)
public class SaasInquiryDrugstoreServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(SaasInquiryDrugstoreServerApplication.class, args);
    }


}
