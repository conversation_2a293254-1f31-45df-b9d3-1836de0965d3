<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-inquiry-drugstore</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <artifactId>saas-inquiry-drugstore-server</artifactId>
  <version>${revision}</version>
  <name>${project.artifactId}</name>

  <properties>
    <java.version>21</java.version>
  </properties>
  <dependencies>


    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>biz-soa-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-drugstore-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-user-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-transmitter-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-hospital-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-test</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-excel</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>wx-java-mp-spring-boot-starter</artifactId> <!-- 微信登录（公众号） -->
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>  <!-- 微信登录（小程序） -->
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
      <!-- <plugin> -->
      <!--   <groupId>org.apache.maven.plugins</groupId> -->
      <!--   <artifactId>maven-compiler-plugin</artifactId> -->
      <!--   <version>3.8.0</version> -->
      <!--   <configuration> -->
      <!--     <source>21</source> -->
      <!--     <target>21</target> -->
      <!--     <encoding>${project.build.sourceEncoding}</encoding> -->
      <!--     <annotationProcessorPaths> -->
      <!--       <path> -->
      <!--         <groupId>org.projectlombok</groupId> -->
      <!--         <artifactId>lombok</artifactId> -->
      <!--         <version>1.18.30</version> -->
      <!--       </path> -->
      <!--       <path> -->
      <!--         <groupId>org.mapstruct</groupId> -->
      <!--         <artifactId>mapstruct-processor</artifactId> -->
      <!--         <version>1.5.5.Final</version> -->
      <!--       </path> -->
      <!--     </annotationProcessorPaths> -->
      <!--     <compilerArgs> -->
      <!--       <arg>-parameters</arg> -->
      <!--       <arg>&#45;&#45;enable-preview</arg> -->
      <!--     </compilerArgs> -->
      <!--   </configuration> -->
      <!-- </plugin> -->
    </plugins>
  </build>

</project>
