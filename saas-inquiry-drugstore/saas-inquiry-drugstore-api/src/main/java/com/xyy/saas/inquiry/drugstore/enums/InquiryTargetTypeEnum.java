package com.xyy.saas.inquiry.drugstore.enums;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Desc 问诊配置选项类型枚举
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum InquiryTargetTypeEnum implements IntArrayValuable {

    GLOBAL(1, "global", "全局", "全局"),
    AREA(2, "area", "区域", "区域"),
    STORE(3, "store", "门店", "门店");

    public final int type;
    public final String field;
    public final String name;
    public final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(InquiryTargetTypeEnum::getType).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static InquiryTargetTypeEnum fromType(int type) {
        return Arrays.stream(values())
            .filter(value -> Objects.equals(value.getType(), type))
            .findFirst()
            .orElse(null);
    }

    public static int toFlags(InquiryTargetTypeEnum... enums) {
        return Arrays.stream(enums)
            .map(i -> 1 << (i.getType() - 1))
            .reduce(0, (a, b) -> a | b);
    }


    public boolean hasFlags(InquiryTargetTypeEnum[] flags) {
        return flags != null && Arrays.stream(flags).anyMatch(i -> i.type == this.type);
    }

}