package com.xyy.saas.inquiry.drugstore.enums;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.drugstore.api.permission.PermissionMatchContext;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import lombok.Getter;
import org.springframework.util.CollectionUtils;
import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Stream;

/**
 * @Author: xucao
 * @DateTime: 2025/4/23 13:34
 * @Description: app 首页上部分菜单 申请处方 、 药师审方 、远程审方
 **/
@Getter
public enum AppHomeCoreMeunEnum {

    APPLY_PRESCRIPTION("applyPrescription","申请处方","top","prescriptionRecord",ctx -> ObjectUtil.notEqual(ctx.getTenantId(), TenantConstant.DEFAULT_TENANT_ID)),
    PHARMACIST_AUDIT("pharmacistAudit","药师审方","top","prescriptionRecord",ctx -> ctx.getRoleCodeList().contains(RoleCodeEnum.PHARMACIST)),
    REMOTE_AUDIT("remoteAudit","远程审方","top","prescriptionAuditRecord",ctx -> ctx.getInquiryBizTypeList().contains(InquiryBizTypeEnum.REMOTE_INQUIRY)),
    PRESCRIPTION_RECORD("prescriptionRecord","处方记录",null,null,ctx -> ObjectUtil.notEqual(ctx.getTenantId(), TenantConstant.DEFAULT_TENANT_ID)),
    PRE_INQUIRY_RECORD("preInquiryRecord","预问诊记录","bottom",null,ctx -> ObjectUtil.notEqual(ctx.getTenantId(), TenantConstant.DEFAULT_TENANT_ID) ),
    PRESCRIPTION_AUDIT_RECORD("prescriptionAuditRecord","审方记录",null,null,ctx -> ctx.getRoleCodeList().contains(RoleCodeEnum.PHARMACIST)),
    PRODUCT_MANAGER("productManager","商品管理","bottom",null,ctx -> ObjectUtil.notEqual(ctx.getTenantId(), TenantConstant.DEFAULT_TENANT_ID));

    private final String code;
    private final String name;
    private final String position;
    private final String bound;
    private final Predicate<PermissionMatchContext> matcher;

    AppHomeCoreMeunEnum(String code, String name, String position , String bound ,Predicate<PermissionMatchContext> matcher) {
        this.code = code;
        this.name = name;
        this.position = position;
        this.bound = bound;
        this.matcher = matcher;
    }

    // 动态匹配方法
    public boolean matches(PermissionMatchContext context) {
        return matcher.test(context);
    }

    // 获取所有匹配的菜单（Java 21新特性：虚拟线程优化）
    public static List<AppHomeCoreMeunEnum> getMatchedMenus(PermissionMatchContext context) {
        return Stream.of(values())
            .filter(menu -> menu.matches(context))
            .toList();
    }

    public static AppHomeCoreMeunEnum fromCode(String code) {
        for (AppHomeCoreMeunEnum menu : values()) {
            if (menu.getCode().equals(code)) {
                return menu;
            }
        }
        return null;
    }
}
