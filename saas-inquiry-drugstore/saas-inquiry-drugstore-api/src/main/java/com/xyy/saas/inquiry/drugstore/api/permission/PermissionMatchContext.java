package com.xyy.saas.inquiry.drugstore.api.permission;

import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import lombok.Data;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/23 13:28
 * @Description: 当前登录用户权限匹配上下文
 **/
@Data
public class PermissionMatchContext {

    /**
     * 当前登录门店开通套餐所拥有的问诊业务类型  1-药店问诊  2-远程审方
     */
    private List<InquiryBizTypeEnum> inquiryBizTypeList;

    /**
     * 当前登录用户拥有的角色编码
     */
    private List<RoleCodeEnum> roleCodeList;

    /**
     * 当前登录用户所属租户id
     */
    private Long tenantId;
}
