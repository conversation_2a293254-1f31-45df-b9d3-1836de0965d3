package com.xyy.saas.inquiry.signature.server.api.signature;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.enums.file.FileTypeEnum;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.signature.InquirySignatureContractApi;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquiryFlushContractReqDto;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquiryFlushContractRespDto;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquirySignatureContractDto;
import com.xyy.saas.inquiry.signature.dto.signature.ElectSignInfoDto;
import com.xyy.saas.inquiry.signature.enums.SignatureSyncPlatformStatusEnum;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractSaveReqVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.convert.signature.InquirySignatureContractConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.service.ca.InquirySignatureCaAuthService;
import com.xyy.saas.inquiry.signature.server.service.fdd.FddSignTaskBussService;
import com.xyy.saas.inquiry.signature.server.service.prescription.InquirySignaturePrescriptionService;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.dto.TemplateSignCheckedDto;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import com.xyy.saas.inquiry.signature.server.util.FileSignatureExtractorUtil;
import com.xyy.saas.inquiry.signature.server.util.FileUtils;
import com.xyy.saas.inquiry.util.FileApiUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 签章合同 Api 接口
 *
 * <AUTHOR>
 */
@DubboService
@Slf4j
public class InquirySignatureContractApiImpl implements InquirySignatureContractApi {

    @Resource
    private InquirySignatureContractService inquirySignatureContractService;

    @Resource
    private InquirySignaturePrescriptionService inquirySignaturePrescriptionService;

    @Resource
    private FddSignTaskBussService fddSignTaskBussService;

    @Resource
    private InquirySignatureCaAuthService inquirySignatureCaAuthService;

    @Autowired
    private TenantApi tenantApi;


    @Override
    public Map<String, ElectSignInfoDto> getSignaturePlatformContractElectSignInfo(String bizId, ContractTypeEnum contractType) {
        InquirySignatureContractDO signatureContractDO = inquirySignatureContractService.querySignatureContractByBizId(bizId, contractType);
        if (signatureContractDO == null || StringUtils.isBlank(signatureContractDO.getThirdId())
            || (Objects.equals(signatureContractDO.getSignaturePlatform(), SignaturePlatformEnum.SELF.getCode()) && !Objects.equals(signatureContractDO.getSyncPlatformStatus(), SignatureSyncPlatformStatusEnum.SYNCED.getCode()))) {
            return null;
        }
        List<ElectSignInfoDto> electSignInfoDtos = signatureContractDO.getParticipants().stream().filter(s -> CommonStatusEnum.isEnable(s.getAccessPlatform()))
            .map(actor -> ElectSignInfoDto.builder().actorField(actor.getActorField()).build())
            .collect(Collectors.toList());
        if (CollUtil.isEmpty(electSignInfoDtos)) {
            return null;
        }
        // 下载合同获取电子签信息
        String pdfUrl =
            StringUtils.isNotBlank(signatureContractDO.extGet().getPlatformPdfUrl()) ? signatureContractDO.extGet().getPlatformPdfUrl()
                : fddSignTaskBussService.downContractAndUpload(signatureContractDO.extGet().getPlatformConfigId(),
                    signatureContractDO.getThirdId(),
                    FileTypeEnum.PDF);
        List<ElectSignInfoDto> extractSignInfos = FileSignatureExtractorUtil.extractSignatureItextPdf(pdfUrl);
        if (CollUtil.isEmpty(extractSignInfos)) {
            return null;
        }
        // 获取文件电子签信息,因为没有标识,按顺序填充到参与方中
        for (int i = 0; i < electSignInfoDtos.size(); i++) {
            if (extractSignInfos.size() > i) {
                electSignInfoDtos.get(i).setCerFile(extractSignInfos.get(i).getCerFile());
                electSignInfoDtos.get(i).setSignValue(extractSignInfos.get(i).getSignValue());
                electSignInfoDtos.get(i).setSignTimestamp(extractSignInfos.get(i).getSignTimestamp());
            }
        }
        return electSignInfoDtos.stream().collect(Collectors.toMap(ElectSignInfoDto::getActorField, ElectSignInfoDto -> ElectSignInfoDto));
    }


    @Override
    public InquirySignatureContractDto getSignaturePlatformContract(String bizId, ContractTypeEnum contractType) {
        InquirySignatureContractDO signatureContractDO = inquirySignatureContractService.querySignatureContractByBizId(bizId, contractType);
        return InquirySignatureContractConvert.INSTANCE.convert(signatureContractDO);
    }


    @Override
    public CommonResult<InquiryFlushContractRespDto> flushContract(InquiryFlushContractReqDto reqDto) {

        InquirySignatureContractDO contract = inquirySignatureContractService.getSignatureContractByPref(reqDto.getPref());
        if (contract == null) {
            return CommonResult.error("合同不存在");
        }

        // 补充参与方签章图片
        Optional.ofNullable(reqDto.getParticipants()).orElse(List.of()).forEach(participant -> {

            if (StringUtils.isBlank(participant.getSignImgUrl()) && participant.getUserId() != null) {

                InquirySignatureCaAuthRespVO caAuthRespVO = inquirySignatureCaAuthService.getInquirySignatureCaInfo(TemplateSignCheckedDto.builder().userId(participant.getUserId()).build(),
                    SignaturePlatformEnum.FDD);
                participant.setSignImgUrl(caAuthRespVO.isDrawnSign() ? caAuthRespVO.getDrawnSignUrl() : caAuthRespVO.getRealSignatureUrl());
            }
        });

        InquirySignatureContractSaveReqVO updateReqVO = InquirySignatureContractConvert.INSTANCE.convertFlushContract(contract, reqDto);
        // 绘制处方
        String pdfUrl = inquirySignaturePrescriptionService.drawnContract(contract, true);

        if (StringUtils.isNotBlank(pdfUrl)) {
            updateReqVO.setPdfUrl(pdfUrl);
            Optional.ofNullable(FileUtils.downLoadFile(pdfUrl, FileTypeEnum.JPG))
                .ifPresent(bytes -> updateReqVO.setImgUrl(FileApiUtil.createFileNoRecord(bytes)));
        }
        // 更新合同
        inquirySignatureContractService.updateSignatureContract(updateReqVO);

        return CommonResult.success(InquirySignatureContractConvert.INSTANCE.convertFlushRespDto(updateReqVO));
    }
}