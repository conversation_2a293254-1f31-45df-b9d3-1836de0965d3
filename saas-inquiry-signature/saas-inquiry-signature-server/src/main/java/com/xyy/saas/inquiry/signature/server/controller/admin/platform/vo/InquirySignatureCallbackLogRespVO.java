package com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 签章回调日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquirySignatureCallbackLogRespVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2027")
    @ExcelProperty("自增主键")
    private Long id;

    @Schema(description = "签章平台 0-自签署 1-法大大", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("签章平台 0-自签署 1-法大大")
    private Integer signaturePlatform;

    @Schema(description = "类型：事件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("类型：事件ID")
    private String type;

    @Schema(description = "业务id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9246")
    @ExcelProperty("业务id")
    private String bizId;

    @Schema(description = "具体事件的请求参数，json字符串")
    @ExcelProperty("具体事件的请求参数，json字符串")
    private String bizContent;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}