package com.xyy.saas.inquiry.signature.server.service.pdf;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.enums.file.FileTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.signature.dto.pdf.RectangleInfo;
import com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants;
import com.xyy.saas.inquiry.signature.server.service.pdf.pdfbox.PdfboxGeneratorHelper;
import com.xyy.saas.inquiry.signature.server.service.pdf.pdfbox.PlainTextFormatter;
import com.xyy.saas.inquiry.signature.server.util.PdfStringUtil;
import com.xyy.saas.inquiry.util.FileApiUtil;
import com.xyy.saas.inquiry.util.UrlConUtil;
import de.danielbechler.util.Collections;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.cos.COSName;
import org.apache.pdfbox.cos.COSString;
import org.apache.pdfbox.io.RandomAccessRead;
import org.apache.pdfbox.io.RandomAccessReadBuffer;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDDocumentCatalog;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.PDResources;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDFont;
import org.apache.pdfbox.pdmodel.font.PDType0Font;
import org.apache.pdfbox.pdmodel.font.encoding.GlyphList;
import org.apache.pdfbox.pdmodel.font.encoding.WinAnsiEncoding;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.apache.pdfbox.pdmodel.interactive.annotation.PDAnnotation;
import org.apache.pdfbox.pdmodel.interactive.annotation.PDAnnotationWidget;
import org.apache.pdfbox.pdmodel.interactive.form.PDAcroForm;
import org.apache.pdfbox.pdmodel.interactive.form.PDField;
import org.apache.pdfbox.pdmodel.interactive.form.PDTextField;
import org.apache.pdfbox.pdmodel.interactive.form.PDVariableText;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

/**
 * desc XXXXX  1.生成文件时无法设置AppendMode为APPEND You are overwriting an existing content, you should use the append mode 2.字体无法设置 Could not process default appearance string '/Helv 0 Tf 0 g ' for field 'no': Could not find font: /Helv
 * 3.图片格式问题（jpg/jpeg/png） Not a JPEG file: starts with 0x89 0x50
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Service("pdfboxService")
//@AllArgsConstructor
@RefreshScope
public class PdfboxServiceImpl implements PdfService {

    private static final Logger log = LoggerFactory.getLogger(PdfboxServiceImpl.class);

    /**
     * 开关控制处方笺生成，调试时直接返回固定处方笺
     */
    @Value("${inquiry.pdf-service.debug:false}")
    private boolean debug;

    /**
     * 默认字体大小
     */
    @Value("${inquiry.pdf-service.default.font.size:15}")
    private float defaultFontSize;

    @Override
    public boolean generateTemplate(String url, List<RectangleInfo> rectangles, File destFile) {
        StopWatch stopWatch = new StopWatch("generateTemplate");
        stopWatch.start("generateTemplate.0");
        // 兼容web链接文件和本地文件地址
        try (
            InputStream is = UrlConUtil.getStreamRetry("GET", url, 3000);
            RandomAccessRead rar = new RandomAccessReadBuffer(is);
            PDDocument pdf = Loader.loadPDF(rar);
            FileOutputStream fos = new FileOutputStream(destFile);
        ) {

            PDDocumentCatalog catalog = pdf.getDocumentCatalog();
            PDAcroForm form = catalog.getAcroForm();
            if (form == null) {
                form = new PDAcroForm(pdf);
                catalog.setAcroForm(form);
            }
            stopWatch.stop();
            stopWatch.start("generateTemplate.1");

            PDPage page = catalog.getPages().get(0);
            List<PDField> fields = new ArrayList<>();
            List<PDAnnotation> annotations = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(rectangles)) {
                // 添加文本域
                for (RectangleInfo rect : rectangles) {
                    PDField textField = addTextField(page, form, rect);
                    fields.add(textField);
                    annotations.add(textField.getWidgets().getFirst());
                }
            }
            // 每次重新设置，相当于清空之前的表单
            form.setFields(fields);
            page.setAnnotations(annotations);
            // 生成pdf，不压缩
            pdf.saveIncremental(fos);
            fos.flush();
            return true;
        } catch (IOException e) {
            log.error("{}.generateTemplate 失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
            return false;
        } finally {
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    private PDField addTextField(PDPage page, PDAcroForm form, RectangleInfo rect) {
        PDTextField textField = new PDTextField(form);
        textField.setPartialName(rect.getFieldName());

        // 暂时不支持重复字段
        PDAnnotationWidget widget = textField.getWidgets().getFirst();
        PDRectangle rectangle = new PDRectangle(rect.getX(), rect.getY(), rect.getWidth(), rect.getHeight());
        widget.setRectangle(rectangle);
        widget.setPage(page);
        widget.setPrinted(true);
        if (rect.getFieldType() == 1) {
            textField.setMultiline(true); // 允许多行
        }
        return textField;
    }
/*
    @Override
    public boolean generate(String url, Map<String,String> dataMap, File destFile) {
        StopWatch stopWatch = new StopWatch("generate");
        stopWatch.start("generate.0");
        File file = new File(url);
        try (InputStream is = UrlConUtil.getStreamRetry("GET", url, 3000);
             RandomAccessRead rar = new RandomAccessReadBuffer(is);
             PDDocument pdf = Loader.loadPDF(rar);
             FileOutputStream fos = new FileOutputStream(destFile);) {
            PDDocumentCatalog catalog = pdf.getDocumentCatalog();
            PDAcroForm form = catalog.getAcroForm();
            if (form == null) {
                FileApiUtil.writeToStream(file, fos);
                return true;
            }

            stopWatch.stop();
            stopWatch.start("generate.1");

            List<PDType0Font> fontList = PdfServiceFactory.getPdfboxFontList().stream().map(ff -> {
                // 不能用 org.apache.pdfbox.pdmodel.font.PDType0Font.load(org.apache.pdfbox.pdmodel.PDDocument, java.io.File) 方法，拿不到fontName
                // embedSubset为false，生成的pdf文件很大（内嵌了字体文件）
                try {
                    return PDType0Font.load(pdf, ff, true);
                } catch (IOException e) {
                    log.error("{}.generate 读取字体文件失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
                    return null;
                }
            }).filter(Objects::nonNull).toList();
            // AcroForm表单 不支持字体子集嵌入, 所以填充数据后的pdf需要有字体环境才能正常展示
            // https://issues.apache.org/jira/browse/PDFBOX-5209?jql=text%20~%20%22form%20subset%22
            PDResources resources = Optional.ofNullable(form.getDefaultResources()).orElseGet(PDResources::new);
            List<String> fontNameList = fontList.stream().map(resources::add).map(COSName::getName).toList();
            form.setDefaultResources(resources);

            stopWatch.stop();
            stopWatch.start("generate.2");

            dataMap.forEach((fieldName, fieldValue) -> {
                if (fieldName == null || fieldValue == null) {
                    return;
                }
                PDTextField field = (PDTextField) form.getField(fieldName);
                try {
                    if (field == null) {
                        return;
                    }
                    // 图片
                    if (fieldName.endsWith("Image")) {
                        insertImage(pdf, field, fieldName, fieldValue);
                        return;
                    }
                    // 设置默认的外观（包含字体和字体大小）
                    // 设置字段的字体
                    // field.getWidgets().forEach(widget -> widget.setFont(font));
                    // String appearance = "/" + fontList.getFirst().getName() + " 12 Tf 0 g";  // Helvetica 字体，12号字体，黑色（灰度 0）
                    // field.getWidgets().getFirst().setAppearance(null);
                    boolean isSuccess = setText(field, fieldValue, fontNameList);
                    if (!isSuccess) {
                        log.error("{}.generate 设置字段 {} 失败: {}", this.getClass().getSimpleName(), fieldName, fieldValue);
                    }
                } catch (IOException e) {
                    log.error("{}.generate 设置字段失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
                }
            });

            form.flatten();
            // 生成pdf，不压缩
            pdf.saveIncremental(fos);
            fos.flush();
            return true;
        } catch (IOException e) {
            log.error("{}.generate 失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
            return false;
        } finally {
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        }
    }*/

    private boolean setText(PDVariableText field, String text, List<String> fontNameList) throws IOException {
        for (String fontName : fontNameList) {
            try {
                String appearance = "/" + fontName + " 12 Tf 0 g"; // Helvetica 字体，12号字体，黑色（灰度 0）
                field.setDefaultAppearance(appearance);
                field.getCOSObject().setItem(COSName.DA, new COSString(appearance));
                field.setValue(text);
                field.setReadOnly(true); // 将字段设置为只读
                return true;
            } catch (IllegalArgumentException ignore) {
                log.warn("字体 {} 不支持，尝试下一个", fontName);
            }
        }
        return false;
    }

    @Override
    public CommonResult<String> generateAndUpload(String url, Map<String, String> dataMap) {
        File destFile = FileUtil.createTempFile("pdf_", ".pdf", true);
        try {
            if (generate(url, dataMap, destFile)) {
                return CommonResult.success(FileApiUtil.createFileNoRecord(destFile.getName(), FileUtil.readBytes(destFile)));
            }
        } catch (IOException e) {
            return CommonResult.error(e.getMessage());
        } finally {
            FileUtil.del(destFile);
        }
        return CommonResult.error("生成pdf失败");
    }


    @Override
    public CommonResult<String> generateAndUpload(byte[] bytes, Map<String, String> dataMap) {
        // 调试模式不生成处方笺
        File destFile = FileUtil.createTempFile("pdf_", ".pdf", true);
        try {
            if (generate(bytes, dataMap, destFile)) {
                // if (debug) {
                //     return CommonResult.success("https://files.test.ybm100.com/INVT/Lzinq/20250703/pdf_15722306182104333149.pdf");
                // }
                return CommonResult.success(FileApiUtil.createFileNoRecord(destFile.getName(), FileUtil.readBytes(destFile)));
            }
        } catch (IOException e) {
            return CommonResult.error(e.getMessage());
        } finally {
            FileUtil.del(destFile);
        }
        return CommonResult.error("生成pdf失败");
    }


    public boolean generate(byte[] bytes, Map<String, String> dataMap, File destFile) throws IOException {
        try (InputStream inputStream = new ByteArrayInputStream(bytes)) {
            return generate(inputStream, dataMap, destFile);
        }
    }

    public boolean generate(String url, Map<String, String> dataMap, File destFile) throws IOException {
        try (InputStream inputStream = UrlConUtil.getStreamRetry("GET", url, 3000)) {
            return generate(inputStream, dataMap, destFile);
        }
    }


    public boolean generate(InputStream is, Map<String, String> dataMap, File destFile) throws IOException {
        StopWatch stopWatch = new StopWatch("generate");
        stopWatch.start("generate.0");
        try (RandomAccessRead rar = new RandomAccessReadBuffer(is);
            PDDocument pdf = Loader.loadPDF(rar);) {

            PDDocumentCatalog catalog = pdf.getDocumentCatalog();
            PDAcroForm form = catalog.getAcroForm();
            if (form == null) {
                // 返回源文件
                // FileApiUtil.copyFile(is, destFile, StandardCopyOption.REPLACE_EXISTING);
                // return true;
                throw exception(ErrorCodeConstants.INQUIRY_PRESCRIPTION_TEMPLATE_FORM_EMPTY);
            }

            PDPage page = catalog.getPages().get(0);

            stopWatch.stop();
            stopWatch.start("generate.1");

            List<PDType0Font> fontList = PdfServiceFactory.getPdfboxFontList().stream().map(trueTypeFont -> {
                // 不能用 org.apache.pdfbox.pdmodel.font.PDType0Font.load(org.apache.pdfbox.pdmodel.PDDocument, java.io.File) 方法，拿不到fontName
                try {
                    return PDType0Font.load(pdf, trueTypeFont, true);
                } catch (IOException e) {
                    log.error("{}.generate 读取字体文件失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
                    return null;
                }
            }).filter(Objects::nonNull).toList();
            PDResources resources = Optional.ofNullable(form.getDefaultResources()).orElseGet(PDResources::new);
            // List<String> fontNameList = fontList.stream().map(resources::add).map(COSName::getName).toList();
            form.setDefaultResources(resources);

            stopWatch.stop();
            stopWatch.start("generate.2");
            String medicineType = dataMap.get(MedicineTypeEnum.CHINESE_MEDICINE.getFieldName()); // 药品类型
            Integer medicineLayoutNumber = 3; // 药品布局 一行3个 (仅针对中药)

            dataMap.forEach((fieldName, fieldValue) -> {
                PDTextField field = null;
                try {
                    field = (PDTextField) form.getField(fieldName);
                    if (field == null || fieldName == null || fieldValue == null) {
                        return;
                    }

                    fieldValue = PdfStringUtil.clean(fieldValue);// filterInvalidControlCharacters(fieldValue);

                    // 图片
                    if (FileTypeEnum.isImage(fieldValue)) {
                        insertImage(pdf, field, fieldName, fieldValue);
                        return;
                    }
                    // 设置默认的外观（包含字体和字体大小）

                    // 手动写文本
                    // boolean isSuccess = setText(pdf, field, fieldValue, fontList, 12f);

                    // 走表单填充-外观
                    // boolean isSuccess = setTextWithAppearance(pdf, field, fieldValue, fontNameList, fontList);

                    // 如果是中药 均分展位 走表单填充-字体
                    boolean isSuccess = StringUtils.equals(medicineType, MedicineTypeEnum.CHINESE_MEDICINE.getCode() + "") && JSONUtil.isTypeJSONArray(fieldValue)
                        ? setTextTableWithFont(pdf, field, fieldValue, medicineLayoutNumber, fontList, defaultFontSize) : setTextWithFont(pdf, field, field.isMultiline(), fieldValue, fontList, defaultFontSize);

                    if (!isSuccess) {
                        log.error("{}.generate 设置字段 {} 失败: {}", this.getClass().getSimpleName(), fieldName, fieldValue);
                    }
                } catch (IOException e) {
                    log.error("{}.generate 设置字段失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
                }
            });

            for (PDField field : form.getFields()) {
                field.setReadOnly(true);
            }

            try (FileOutputStream fos = new FileOutputStream(destFile);) {
                // 每次重新设置，相当于清空之前的表单
                // form.setFields(Collections.emptyList());
                // page.setAnnotations(Collections.emptyList());
                // 生成pdf，不压缩
                pdf.saveIncremental(fos);
                fos.flush();
            }
            return true;
        } catch (IOException e) {
            log.error("{}.generate 失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
            throw e;
//            return false;
        } finally {
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    private <T extends PDFont> boolean setTextWithAppearance(PDDocument document, PDVariableText field, String text, List<String> fontNameList, List<T> fontList) throws IOException {
        for (String fontName : fontNameList) {
            try {
                String appearance = "/" + fontName + " 12 Tf 0 g"; // Helvetica 字体，12号字体，黑色（灰度 0）
                field.setDefaultAppearance(appearance);
                field.getCOSObject().setItem(COSName.DA, new COSString(appearance));

                field.getCOSObject().setString(COSName.V, text);
                // 设置文本
                new PdfboxGeneratorHelper(document, field, fontList).setAppearanceValue(text);

                return true;
            } catch (IllegalArgumentException ignore) {
                log.warn("字体 {} 不支持，尝试下一个", fontName);
            } finally {
                field.setReadOnly(true); // 将字段设置为只读
            }
        }
        return false;
    }

    /**
     * 普通填充文本
     */
    private <T extends PDFont> boolean setTextWithFont(PDDocument document, PDVariableText field, boolean isMultiline, String text, List<T> fontList, float fontSize) throws IOException {
        PDAnnotationWidget widget = field.getWidgets().getFirst();
        PDPage page = widget.getPage();
        return fontList.stream().anyMatch(font -> {
            try {
                if (StringUtils.isBlank(text)) {
                    return true;
                }
                // 设置文本计算定位
                PlainTextFormatter plainTextFormatter = new PdfboxGeneratorHelper(document, field, null).plainTextFormatter(widget, text, font, fontSize);
                if (plainTextFormatter == null) {
                    log.error("{}.setTextWithFont 获取文本定位 {} 失败: {}", this.getClass().getSimpleName(), field.getPartialName(), text);
                    return false;
                }
                List<Float[]> positionList = plainTextFormatter.getPositionList();
                List<String> textList = plainTextFormatter.getTextList();
                if (CollectionUtils.size(positionList) != CollectionUtils.size(textList)) {
                    log.error("{}.setTextWithFont 文本定位异常 {} 失败: {}", this.getClass().getSimpleName(), field.getPartialName(), text);
                    return false;
                }
                int min = positionList.size();

                PDRectangle rectangle = widget.getRectangle();

                float fieldWidth = rectangle.getWidth();

                float adjustedFontSize = fontSize;

                // 如果文本宽度超过列宽，则调整字体大小
                if (!isMultiline) {
                    try {
                        if (font.getStringWidth(text) > fieldWidth) {
                            adjustedFontSize = Math.max(MIN_FONT_SIZE, fieldWidth * 1000 / font.getStringWidth(text));
                            log.info("ASIAN_MEDICINE adjustedFontSize max: {}", adjustedFontSize);
                            adjustedFontSize = Math.min(defaultFontSize, adjustedFontSize);
                            log.info("ASIAN_MEDICINE adjustedFontSize min: {}", adjustedFontSize);
                        }
                    } catch (Exception ignore) {
                        log.warn("计算字体长度失败,忽略,{}", text);
                    }
                }

                try (PDPageContentStream cs = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, false);) {
                    cs.setFont(font, adjustedFontSize);
                    float x = rectangle.getLowerLeftX(), y = rectangle.getLowerLeftY();
                    for (int i = 0; i < min; i++) {
                        x += positionList.get(i)[0];
                        y += positionList.get(i)[1];
                        cs.beginText();
                        cs.newLineAtOffset(x, y);
                        cs.showText(textList.get(i));
                        cs.endText();
                    }
                    return true;
                }
            } catch (IOException | IllegalArgumentException e) {
                log.error("{}.setTextWithFont 设置字段 {} 字体: {} 失败: {}", this.getClass().getSimpleName(), field.getPartialName(), font.getName(), e.getMessage(), e);
                return false;
            } finally {
                field.setReadOnly(true);
            }
        });
    }

    private static final float MIN_FONT_SIZE = 5; // 最小字体大小

    /**
     * 特殊处理填充文字表格 - 针对中药形式
     */
    private <T extends PDFont> boolean setTextTableWithFont(PDDocument document, PDVariableText field, String text, Integer medicineLayoutNumber, List<T> fontList, float fontSize) throws IOException {
        PDAnnotationWidget widget = field.getWidgets().getFirst();
        PDPage page = widget.getPage();
        return fontList.stream().anyMatch(font -> {
            try {

                List<String> list = JSON.parseArray(text, String.class);
                if (Collections.isEmpty(list)) {
                    return true;
                }

                List<List<String>> groupedText = Lists.partition(list, medicineLayoutNumber);

                try (PDPageContentStream cs = new PDPageContentStream(document, document.getPage(0), PDPageContentStream.AppendMode.APPEND, false)) {
                    PDRectangle rectangle = field.getWidgets().get(0).getRectangle();
                    float xStart = rectangle.getLowerLeftX() + 5; // 留出一些边距
                    float y = rectangle.getUpperRightY() - fontSize - 5; // 减去字体大小和一些边距

                    // 计算每列的宽度
                    float columnWidth = (rectangle.getWidth() - (5 * medicineLayoutNumber)) / medicineLayoutNumber; // 总宽度减去左右各5个单位的边距后分成三份

                    for (List<String> line : groupedText) {
                        float currentX = xStart;
                        for (String item : line) {
                            // 计算该项文本在当前字体大小下的宽度
                            float stringWidth = font.getStringWidth(item) / 1000 * fontSize;
                            float adjustedFontSize = fontSize;

                            // 如果文本宽度超过列宽，则调整字体大小
                            if (stringWidth > columnWidth) {
                                adjustedFontSize = Math.max(MIN_FONT_SIZE, columnWidth * 1000 / font.getStringWidth(item));
                                log.info("CHINESE_MEDICINE adjustedFontSize max: {}", adjustedFontSize);
                                adjustedFontSize = Math.min(defaultFontSize, adjustedFontSize);
                                log.info("CHINESE_MEDICINE adjustedFontSize min: {}", adjustedFontSize);
                            }

                            cs.setFont(font, adjustedFontSize);
                            cs.beginText();
                            cs.newLineAtOffset(currentX, y);
                            cs.showText(item);
                            cs.endText();

                            // 移动到下一列的位置
                            currentX += columnWidth + 5;
                        }
                        // 换行，调整y坐标
                        y -= fontSize + 10; // 行间距为字体大小加5个单位
                    }
                    return true;
                }
            } catch (IOException | IllegalArgumentException e) {
                log.error("{}.setTextWithFont 设置字段 {} 字体: {} 失败: {}", this.getClass().getSimpleName(), field.getPartialName(), font.getName(), e.getMessage(), e);
                return false;
            } finally {
                field.setReadOnly(true);
                // font.getCOSObject().clear();
            }
        });
    }

    private <T extends PDFont> boolean setText(PDDocument document, PDTextField field,
        String text, List<T> fontList, float fontSize) throws IOException {
        PDRectangle rect = field.getWidgets().getFirst().getRectangle();
        PDPage page = field.getWidgets().getFirst().getPage();

        boolean multiline = field.isMultiline();
        return fontList.stream().anyMatch(font -> {
            try (PDPageContentStream cs = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, false);) {
                // Get the string height in text space units.
                float stringHeight = font.getFontDescriptor().getFontBoundingBox().getHeight() / 1000f * fontSize;

                cs.setFont(font, fontSize);
                float startY = rect.getUpperRightY() - (multiline ? fontSize : (rect.getHeight() + fontSize) / 2);
                for (String line : handleTabs(text, 4).split("\n")) {
                    cs.beginText();
                    // cs.setTextMatrix(Matrix.getTranslateInstance(rect.getLowerLeftX(), rect.getLowerLeftY() + rect.getHeight() / 2));
                    cs.newLineAtOffset(rect.getLowerLeftX(), startY);
                    cs.showText(line);
                    startY -= stringHeight;
                    cs.endText();
                }
                return true;

                /*// Get the non-justified string width in text space units.
                float stringWidth = font.getStringWidth(text) * fontSize;

                // Now show word justified.
                // The space we have to make up, in text space units.
                float justifyWidth = rect.getWidth() - stringWidth;

                List<Object> message = new ArrayList<>();
                float extraLetterWidth = (justifyWidth / (text.codePointCount(0, text.length()) - 1)) / fontSize;
                for (int i = 0; i < text.length(); i += Character.charCount(text.codePointAt(i))) {
                    if (i != 0) {
                        message.add(-extraLetterWidth);
                    }
                    message.add(String.valueOf(Character.toChars(text.codePointAt(i))));
                }
                cs.beginText();
                cs.setFont(font, fontSize);
                cs.showTextWithPositioning(message.toArray());
                cs.setTextMatrix(Matrix.getTranslateInstance(0, rect.getHeight() - stringHeight));
                cs.showText(text);
                cs.endText();
                return true;*/
            } catch (Exception e) {
                log.error("{}.setText 设置字段 {} 字体: {} 失败: {}", this.getClass().getSimpleName(), field.getPartialName(), font.getName(), e.getMessage(), e);
                return false;
            } finally {
                field.setReadOnly(true);
            }
        });
    }

    /**
     * 处理字符串中的制表符，用指定数量的空格替换每个制表符。
     *
     * @param text    原始字符串
     * @param tabSize 每个制表符用多少个空格替换
     * @return 处理后的字符串
     */
    public static String handleTabs(String text, int tabSize) {
        StringBuilder result = new StringBuilder();
        for (char c : text.toCharArray()) {
            if (c == '\t') {
                for (int i = 0; i < tabSize; i++) {
                    result.append(' ');
                }
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    private void showTextMultiple(PDPageContentStream contentStream, PDTextField field, String text, List<PDFont> fonts, float fontSize) throws IOException {
        if (field == null || StringUtils.isEmpty(text)) {
            return;
        }
        if (CollectionUtils.isEmpty(fonts)) {
            log.error("字体为空，无法设置文本");
            return;
        }
        try {
            fonts.getFirst().encode(text);
            contentStream.setFont(fonts.getFirst(), fontSize);
            contentStream.showText(text);
        } catch (IllegalArgumentException e) {
            log.error("{}.showTextMultiple 字体编码失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
        }

        for (int i = 0, len = text.length(); i < len; i++) {
            boolean found = false;
            for (PDFont font : fonts) {
                try {
                    String character = text.substring(i, i + 1);
                    font.encode(character);
                    int j = i + 1;
                    for (; j < len; ++j) {
                        String nextCharacter = text.substring(j, j + 1);
                        if (isWinAnsiEncoding(nextCharacter.codePointAt(0)) && font != fonts.getFirst()) {
                            // Without this segment, the example would have a flaw:
                            // This code tries to keep the current font, so
                            // the second "abc" would appear in a different font
                            // than the first one, which would be weird.
                            // This segment assumes that the first font has WinAnsiEncoding.
                            // (all static PDType1Font Times / Helvetica / Courier fonts)
                            break;
                        }
                        try {
                            font.encode(nextCharacter);
                        } catch (IllegalArgumentException ex) {
                            // it's over
                            break;
                        }
                    }

                    contentStream.setFont(font, fontSize);
                    contentStream.showText(text.substring(i, j));
                    i = j;
                    found = true;
                    break;
                } catch (IllegalArgumentException ex) {
                    // didn't work, will try next font
                }
            }
            if (!found) {
                throw new IllegalArgumentException("Could not show '" + text.substring(i, i + 1) + "' with the fonts provided");
            }
        }
    }


    private static boolean isWinAnsiEncoding(int unicode) {
        String name = GlyphList.getAdobeGlyphList().codePointToName(unicode);
        if (".notdef".equals(name)) {
            return false;
        }
        return WinAnsiEncoding.INSTANCE.contains(name);
    }

    private boolean insertImage(PDDocument document, PDField field, String filedName, String url) {
        PDAcroForm form = document.getDocumentCatalog().getAcroForm();
        PDAnnotationWidget widget = form.getField(filedName).getWidgets().getFirst();
        PDPage page = widget.getPage();
        PDRectangle rect = widget.getRectangle();

        File tempFile = FileUtil.createTempFile("temp_", ".png", true);
        // 创建 PDPageContentStream 用于绘制
        // 1.生成文件时无法设置AppendMode为APPEND
        //      You are overwriting an existing content, you should use the append mode

        try (PDPageContentStream contentStream = new PDPageContentStream(document, page, PDPageContentStream.AppendMode.APPEND, false);
            InputStream inputStream = UrlConUtil.getStreamRetry("GET", url, 3000)) {

            FileUtil.writeFromStream(inputStream, tempFile);
            PDImageXObject pdImage = PDImageXObject.createFromFile(tempFile.getAbsolutePath(), document);
            // 绘制图片
            contentStream.drawImage(pdImage,
                rect.getLowerLeftX(),
                rect.getLowerLeftY(),
                rect.getWidth(),
                rect.getHeight());

            field.setReadOnly(true); // 将字段设置为只读
        } catch (Exception e) {
            log.error("{}.insertImage 失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
            return false;
        } finally {
            FileUtil.del(tempFile);
        }
        return true;
    }


    /**
     * 过滤PDF中可能导致字体渲染问题的控制字符
     *
     * @param text 原始文本
     * @return 过滤后的文本
     */
    private static String filterInvalidControlCharacters(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }

        // 先处理字面意义的 "\\n" 字符串（不是换行符）
        String result = text.replace("\\n", "");

        // 移除可能导致PDF字体渲染问题的控制字符
        // 包括：0x00-0x08 (基本控制字符)
        //       0x0B (垂直制表符)
        //       0x0C (换页符)
        //       0x0E-0x1F (其他控制字符)
        //       0x200B (零宽空格)
        result = StringUtils.replaceAll(result, "\u200B", "");
        result = result.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\u200B]", "");

        return result;
    }

}
