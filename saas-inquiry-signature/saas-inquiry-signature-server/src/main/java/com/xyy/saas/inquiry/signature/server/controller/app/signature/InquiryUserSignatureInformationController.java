package com.xyy.saas.inquiry.signature.server.controller.app.signature;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquirySignatureRoleVO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureManageVO;
import com.xyy.saas.inquiry.signature.server.service.signature.InquiryUserSignatureInformationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "app - 问诊用户(医生/药师/核对/调配)签章信息")
@RestController
@RequestMapping("/signature/inquiry-user-signature-information")
@Validated
public class InquiryUserSignatureInformationController {

    @Resource
    private InquiryUserSignatureInformationService inquiryUserSignatureInformationService;

    /**
     * (核对) (药师) 单人单选
     *
     * @param createReqVO
     * @return
     */
    @PostMapping("/create")
    @Operation(summary = "新增问诊用户签名")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-user-signature-information:create')")
    public CommonResult<Long> createInquiryUserSignature(@Valid @RequestBody InquiryUserSignatureManageVO createReqVO) {
        return success(inquiryUserSignatureInformationService.createInquiryUserSignature(createReqVO));
    }


    @PutMapping("/update")
    @Operation(summary = "修改问诊用户签名")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-user-signature-information:update')")
    public CommonResult<Long> updateInquiryUserSignature(@Valid @RequestBody InquiryUserSignatureManageVO updateReqVO) {
        return success(inquiryUserSignatureInformationService.updateInquiryUserSignature(updateReqVO));
    }


    @GetMapping("/get")
    @Operation(summary = "获得用户签名信息")
    @Parameter(name = "id", description = "userId", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-user-signature-information:query')")
    public CommonResult<InquiryUserSignatureManageVO> getInquiryUserSignature(@RequestParam("id") Long id) {
        return success(inquiryUserSignatureInformationService.getInquiryUserSignatureTenant(id, SignaturePlatformEnum.SELF));
    }


    @GetMapping("/list-signature-role")
    @Operation(summary = "获取签章用户角色列表", description = "只包含问诊app端签章授权角色列表-核对/发药/调配/药师")
    public CommonResult<List<InquirySignatureRoleVO>> getSignatureRoleList() {
        return success(inquiryUserSignatureInformationService.getSignatureRoleList());
    }

//    @PostMapping("/update-status")
//    @Operation(summary = "修改问诊用户签名状态")
//    @PreAuthorize("@ss.hasPermission('signature:inquiry-user-signature-information:update')")
//    public CommonResult<Long> updateInquiryUserSignatureStatus(@Valid @RequestBody InquiryUserSignatureManageVO updateReqVO) {
//        return success(inquiryUserSignatureInformationService.updateInquiryUserSignature(updateReqVO));
//    }

//    @PostMapping("/page")
//    @Operation(summary = "获取问诊签名管理用户")
//    @PreAuthorize("@ss.hasPermission('signature:inquiry-user-signature-information:query')")
//    public CommonResult<PageResult<InquiryUserSignatureManageVO>> pageInquiryUserSignature(@RequestBody InquiryUserSignatureManageVO manageVO) {
//        return success(inquiryUserSignatureInformationService.pageInquiryUserSignature(manageVO));
//    }

//
//
//    @PostMapping("/create")
//    @Operation(summary = "创建问诊用户(医生/药师/核对/调配)签章信息")
//    @PreAuthorize("@ss.hasPermission('saas:inquiry-user-signature-information:create')")
//    public CommonResult<Long> createInquiryUserSignatureInformation(@Valid @RequestBody InquiryUserSignatureInformationSaveReqVO createReqVO) {
//        return success(inquiryUserSignatureInformationService.createInquiryUserSignatureInformation(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新问诊用户(医生/药师/核对/调配)签章信息")
//    @PreAuthorize("@ss.hasPermission('saas:inquiry-user-signature-information:update')")
//    public CommonResult<Boolean> updateInquiryUserSignatureInformation(@Valid @RequestBody InquiryUserSignatureInformationSaveReqVO updateReqVO) {
//        inquiryUserSignatureInformationService.updateInquiryUserSignatureInformation(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除问诊用户(医生/药师/核对/调配)签章信息")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('saas:inquiry-user-signature-information:delete')")
//    public CommonResult<Boolean> deleteInquiryUserSignatureInformation(@RequestParam("id") Long id) {
//        inquiryUserSignatureInformationService.deleteInquiryUserSignatureInformation(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得问诊用户(医生/药师/核对/调配)签章信息")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('saas:inquiry-user-signature-information:query')")
//    public CommonResult<InquiryUserSignatureInformationRespVO> getInquiryUserSignatureInformation(@RequestParam("id") Long id) {
//        InquiryUserSignatureInformationDO inquiryUserSignatureInformation = inquiryUserSignatureInformationService.getInquiryUserSignatureInformation(id);
//        return success(BeanUtils.toBean(inquiryUserSignatureInformation, InquiryUserSignatureInformationRespVO.class));
//    }


}