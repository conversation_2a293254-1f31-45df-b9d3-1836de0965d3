package com.xyy.saas.inquiry.signature.server;

import cn.iocoder.yudao.framework.web.config.YudaoWebAutoConfiguration;
import cn.iocoder.yudao.module.infra.framework.file.config.YudaoFileAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Import;

@EnableDubbo(scanBasePackages = {"com.xyy.saas.inquiry"})
@EnableDiscoveryClient
//@EnableEventBus
@SpringBootApplication(scanBasePackages = {"com.xyy.common.*", "com.xyy.saas.inquiry"})
@Slf4j
@Import({YudaoFileAutoConfiguration.class, YudaoWebAutoConfiguration.class})
public class SaasInquirySignatureServerApplication {

    public static void main(String[] args) {
        // 设置skywalking服务名称
        System.setProperty("skywalking.agent.service_name", "inquiry-signature-server");
        // 设置文件名编码格式，-Dsun.jnu.encoding=UTF-8 设置无效
        System.setProperty("sun.jnu.encoding", "UTF-8");
        // 禁用Elasticsearch健康检查
        SpringApplication.run(SaasInquirySignatureServerApplication.class, args);
    }
}
