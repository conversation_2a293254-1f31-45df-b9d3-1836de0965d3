package com.xyy.saas.inquiry.signature.server.dal.dataobject.signature;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 问诊用户(医生/药师/核对/调配)签章信息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_user_signature_information")
// @KeySequence("saas_inquiry_user_signature_information_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryUserSignatureInformationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 门店编号,药师医生无
     */
    private Long tenantId;

    /**
     * 签章平台 0-自签署 1-法大大
     */
    private Integer signaturePlatform;
    /**
     * 签章业务类型 1-用户签名 2-授权合同
     */
    private Integer signatureBizType;
    /**
     * 签署任务id(eg:合同号)
     */
    private String signatureTaskId;
    /**
     * 签章状态 1-签署中,2-签署完成
     */
    private Integer signatureStatus;
    /**
     * 签署url(eg:合同链接/签名图片)
     */
    private String signatureUrl;

}