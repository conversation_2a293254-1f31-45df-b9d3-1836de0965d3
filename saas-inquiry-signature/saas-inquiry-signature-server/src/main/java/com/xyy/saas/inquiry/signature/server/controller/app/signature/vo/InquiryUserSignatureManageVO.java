package com.xyy.saas.inquiry.signature.server.controller.app.signature.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.validation.Mobile;
import com.xyy.saas.inquiry.enums.user.UserStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/18 13:20
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "管理后台 - 问诊用户签章信息新增/修改 Request VO")
@Data
@Accessors(chain = true)
public class InquiryUserSignatureManageVO extends PageParam {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5037")
    private Long id;

    @Schema(description = "userId", requiredMode = Schema.RequiredMode.REQUIRED, example = "5037")
    private Long userId;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @Size(max = 30, message = "姓名长度不能超过30个字符")
    @NotBlank
    private String nickname;

    @Schema(description = "手机号码", example = "15601691300")
    @Mobile
    private String mobile;

    @Schema(description = "性别", example = "1")
    @NotNull
    private Integer sex;

    @Schema(description = "身份证号", example = "1")
    private String idCard;

    @Schema(description = "角色ids", example = "wz_check")
    @NotNull
    private Set<Long> roleIds;

    @Schema(description = "角色名称")
    private List<String> roleNames;

    @Schema(description = "签名图片base64")
    private String signatureImgBase64;

    @Schema(description = "签名图片Url")
    private String signatureUrl;

    // user_relation ------
    /**
     * {@link UserStatusEnum}
     */
    @Schema(description = "用户 0启用 1禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;

    @Schema(description = "门店用户关系id")
    private Long tenantUserRelationId;

//    @AssertTrue(message = "签名图片base64不能为空")
//    @JsonIgnore
//    public boolean isSignatureImgBase64Valid() {
//        return id == null // 修改时，不需要传递
//                && StringUtils.isBlank(signatureImgBase64); // 新增时，签名图片base64
//    }

}
