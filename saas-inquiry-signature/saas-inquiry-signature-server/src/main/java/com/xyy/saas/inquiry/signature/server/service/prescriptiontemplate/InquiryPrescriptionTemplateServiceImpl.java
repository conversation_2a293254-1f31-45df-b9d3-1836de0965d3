package com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_TEMPLATE_FIELD_ERROR;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_TEMPLATE_NOT_EXISTS;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_TEMPLATE_SORT_ERROR;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_TEMPLATE_USED_CANNOT_DELETE;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_TEMPLATE_USED_CANNOT_DISABLE;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_CA_AUTH_NOT_FREE_SIGN;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_CA_AUTH_NOT_SIGN_URL;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import com.xyy.saas.inquiry.enums.prescription.template.TemplateFieldTypeEnum;
import com.xyy.saas.inquiry.enums.signature.FddCaConstantEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureAppConfigIdEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateFieldDto;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateCacheVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateFieldRespVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateGenerateReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplatePageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateRespVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateSaveReqVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.SignatureCAExtVO;
import com.xyy.saas.inquiry.signature.server.convert.prescriptiontemplate.PrescriptionTemplateConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.prescriptiontemplate.InquiryPrescriptionTemplateDO;
import com.xyy.saas.inquiry.signature.server.dal.mysql.prescriptiontemplate.InquiryPrescriptionTemplateMapper;
import com.xyy.saas.inquiry.signature.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.signature.server.service.ca.InquirySignatureCaAuthService;
import com.xyy.saas.inquiry.signature.server.service.pdf.MockDataPdfService;
import com.xyy.saas.inquiry.signature.server.service.pdf.PdfServiceFactory;
import com.xyy.saas.inquiry.signature.server.service.person.InquirySignaturePersonService;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.dto.TemplateSignCheckedDto;
import com.xyy.saas.inquiry.signature.server.service.signature.InquiryUserSignatureInformationService;
import com.xyy.saas.inquiry.util.FileApiUtil;
import com.xyy.saas.inquiry.util.UserUtil;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 处方笺模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryPrescriptionTemplateServiceImpl implements InquiryPrescriptionTemplateService {

    private static final Logger log = LoggerFactory.getLogger(InquiryPrescriptionTemplateServiceImpl.class);
    @Resource
    private InquiryPrescriptionTemplateMapper inquiryPrescriptionTemplateMapper;
    @Resource
    private PdfServiceFactory pdfServiceFactory;
    @Resource
    private MockDataPdfService mockDataPdfService;
    @Resource
    protected InquiryUserSignatureInformationService inquiryUserSignatureInformationService;

    @DubboReference
    private AdminUserApi adminUserApi;

    @DubboReference
    private InquiryHospitalApi inquiryHospitalApi;

    @Resource
    @Lazy
    private InquirySignatureCaAuthService inquirySignatureCaAuthService;

    @Resource
    private InquirySignaturePersonService inquirySignaturePersonService;


    private InquiryPrescriptionTemplateServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }


    @Override
    public List<InquiryPrescriptionTemplateDO> loadAllTemplate() {
        return inquiryPrescriptionTemplateMapper.selectList();
    }


    @Override
    @Cacheable(cacheNames = RedisKeyConstants.SIGNATURE_PRESCRIPTION_TEMPLATE_BYTE, key = "#templateId", unless = "#result == null")
    public InquiryPrescriptionTemplateCacheVO getPrescriptionTemplate4Cache(Long templateId) {
        InquiryPrescriptionTemplateDO templateDO = inquiryPrescriptionTemplateMapper.selectById(templateId);
        if (templateDO == null || StringUtils.isBlank(templateDO.getUrl())) {
            return null;
        }
        try (InputStream is = URLUtil.getStream(URLUtil.url(templateDO.getUrl()))) {
            return InquiryPrescriptionTemplateCacheVO.builder().id(templateId).url(templateDO.getUrl()).content(is.readAllBytes()).build();
        } catch (IOException e) {
            log.error("getPrescriptionTemplate4Cache失败,templateId:{},msg:{}", templateId, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Long createInquiryPrescriptionTemplate(InquiryPrescriptionTemplateSaveReqVO createReqVO) {
        // 插入
        InquiryPrescriptionTemplateDO inquiryPrescriptionTemplate = BeanUtils.toBean(createReqVO, InquiryPrescriptionTemplateDO.class);
        // 新增时底版赋值
        inquiryPrescriptionTemplate.setUrl0(StringUtils.defaultIfBlank(createReqVO.getUrl0(), createReqVO.getUrl()));
        // 是否禁用，不传则默认禁用
        inquiryPrescriptionTemplate.setDisable(Optional.ofNullable(createReqVO.getDisable()).orElse(Boolean.TRUE));
        inquiryPrescriptionTemplateMapper.insert(inquiryPrescriptionTemplate);
        // 返回
        return inquiryPrescriptionTemplate.getId();
    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.SIGNATURE_PRESCRIPTION_TEMPLATE_BYTE, key = "#updateReqVO.id")
    public void updateInquiryPrescriptionTemplate(InquiryPrescriptionTemplateSaveReqVO updateReqVO) {
        Long id = updateReqVO.getId();
        // 校验存在
        validateInquiryPrescriptionTemplateExists(id);

        // 校验模板字段
        if (CollUtil.isNotEmpty(updateReqVO.getTemplateFields())) {
            if (updateReqVO.getTemplateFields().stream().anyMatch(f -> Objects.equals(f.getFieldType(), TemplateFieldTypeEnum.SIGN_PICTURE.code) && f.getSorted() == null)) {
                throw exception(INQUIRY_PRESCRIPTION_TEMPLATE_SORT_ERROR);
            }
            if (updateReqVO.getTemplateFields().stream().anyMatch(f -> Objects.equals(f.getFieldType(), TemplateFieldTypeEnum.SIGN_PICTURE.code)
                && !PrescriptionTemplateFieldEnum.getFieldsByType(TemplateFieldTypeEnum.SIGN_PICTURE).contains(f.getField()))) {
                throw exception(INQUIRY_PRESCRIPTION_TEMPLATE_FIELD_ERROR);
            }
        }

        // 处方笺被医院配置状态时无法禁用，操作时提示“该处方正在使用，如需禁用请先解绑后再做禁用操作！”
        if (Boolean.TRUE.equals(updateReqVO.getDisable())) {
            // 查询处方笺关联的医院
            Map<Long, Set<InquiryHospitalRespDto>> presTempUsedHospitalMap = inquiryHospitalApi.getPresTempUsedHospitalMap(List.of(id));

            if (presTempUsedHospitalMap != null && CollUtil.isNotEmpty(presTempUsedHospitalMap.get(id))) {
                throw exception(INQUIRY_PRESCRIPTION_TEMPLATE_USED_CANNOT_DISABLE);
            }
        }
        // 更新
        InquiryPrescriptionTemplateDO updateObj = BeanUtils.toBean(updateReqVO, InquiryPrescriptionTemplateDO.class);
        inquiryPrescriptionTemplateMapper.updateById(updateObj);
    }

    @Override
    public void copyInquiryPrescriptionTemplate(Long id) {
        // 校验存在
        InquiryPrescriptionTemplateDO template = validateInquiryPrescriptionTemplateExists(id);
        // 复制
        InquiryPrescriptionTemplateDO newTemplate = BeanUtils.toBean(template, InquiryPrescriptionTemplateDO.class);
        newTemplate.setId(null);
        newTemplate.setName(newTemplate.getName() + " 副本");
        newTemplate.setCreateTime(LocalDateTime.now());
        newTemplate.setUpdateTime(LocalDateTime.now());
        inquiryPrescriptionTemplateMapper.insert(newTemplate);
    }

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.SIGNATURE_PRESCRIPTION_TEMPLATE_BYTE, key = "#id")
    public void deleteInquiryPrescriptionTemplate(Long id) {
        // 校验存在
        validateInquiryPrescriptionTemplateExists(id);
        // 查询处方笺关联的医院
        Map<Long, Set<InquiryHospitalRespDto>> presTempUsedHospitalMap = inquiryHospitalApi.getPresTempUsedHospitalMap(List.of(id));

        if (presTempUsedHospitalMap != null && CollUtil.isNotEmpty(presTempUsedHospitalMap.get(id))) {
            throw exception(INQUIRY_PRESCRIPTION_TEMPLATE_USED_CANNOT_DELETE);
        }
        // 删除
        inquiryPrescriptionTemplateMapper.deleteById(id);
    }

    public InquiryPrescriptionTemplateDO validateInquiryPrescriptionTemplateExists(Long id) {
        InquiryPrescriptionTemplateDO inquiryPrescriptionTemplateDO = inquiryPrescriptionTemplateMapper.selectById(id);
        if (inquiryPrescriptionTemplateDO == null) {
            throw exception(INQUIRY_PRESCRIPTION_TEMPLATE_NOT_EXISTS);
        }
        return inquiryPrescriptionTemplateDO;
    }

    @Override
    public InquiryPrescriptionTemplateRespVO getInquiryPrescriptionTemplate(Long id) {
        if (id == null) {
            return null;
        }
        InquiryPrescriptionTemplateDO inquiryPrescriptionTemplateDO = inquiryPrescriptionTemplateMapper.selectById(id);
        if (inquiryPrescriptionTemplateDO == null) {
            return null;
        }
        InquiryPrescriptionTemplateRespVO vo = BeanUtils.toBean(inquiryPrescriptionTemplateDO, InquiryPrescriptionTemplateRespVO.class);

        return vo;
    }

    @Override
    public List<InquiryPrescriptionTemplateRespVO> listInquiryPrescriptionTemplate(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<InquiryPrescriptionTemplateDO> doList = inquiryPrescriptionTemplateMapper.selectBatchIds(ids);

        return doList.stream().map(pt -> {
            InquiryPrescriptionTemplateRespVO vo = BeanUtils.toBean(pt, InquiryPrescriptionTemplateRespVO.class);
            if (vo == null) {
                return null;
            }
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public PageResult<InquiryPrescriptionTemplateRespVO> getInquiryPrescriptionTemplatePage(
        InquiryPrescriptionTemplatePageReqVO pageReqVO) {

        // 关联医院：先查询关联医院的处方笺列表
        if (StringUtils.isNotBlank(pageReqVO.getUsedRelatedHospitalPref())) {
            List<Long> presTempIdList = inquiryHospitalApi.getInquiryHospitalByPref(pageReqVO.getUsedRelatedHospitalPref())
                .usedPresTempIdList();
            if (CollUtil.isEmpty(presTempIdList)) {
                return PageResult.empty();
            }
            pageReqVO.setIdList(presTempIdList);
        }

        PageResult<InquiryPrescriptionTemplateDO> pageResult = inquiryPrescriptionTemplateMapper.selectPage(pageReqVO);
        PageResult<InquiryPrescriptionTemplateRespVO> result = BeanUtils.toBean(pageResult, InquiryPrescriptionTemplateRespVO.class);
        List<InquiryPrescriptionTemplateRespVO> list = result.getList();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }

        UserUtil.fillUserInfo(list, adminUserApi::getUserNameMap);

        // 查询处方笺关联的医院
        List<Long> presTempIdList = list.stream().map(InquiryPrescriptionTemplateRespVO::getId).toList();
        Map<Long, Set<InquiryHospitalRespDto>> presTempUsedHospitalMap = inquiryHospitalApi.getPresTempUsedHospitalMap(presTempIdList);

        list.forEach(vo -> {
            // 关联医院
            Optional.ofNullable(presTempUsedHospitalMap.get(vo.getId())).ifPresent(hospitalSet -> {
                vo.setUsedRelatedHospitalNameList(hospitalSet.stream().map(InquiryHospitalRespDto::getName).collect(Collectors.toList()));
            });
        });

        result.setList(list);
        return result;
    }

    @Override
    public InquiryPrescriptionTemplateRespVO generateInquiryPrescriptionTemplate(
        InquiryPrescriptionTemplateGenerateReqVO reqVO) {
        // 校验存在
        InquiryPrescriptionTemplateDO inquiryPrescriptionTemplateDO = validateInquiryPrescriptionTemplateExists(reqVO.getId());
        // 生成pdf并上传
        File destFile = FileUtil.createTempFile("pdf_temp_", ".pdf", true);
        try {
            // 底版url0 + 字段填充 = 模板url
            if (pdfServiceFactory.getInstance().generateTemplate(inquiryPrescriptionTemplateDO.getUrl0(), reqVO.getRectangles(), destFile)) {
                String url = FileApiUtil.createFileNoRecord(destFile.getName(), FileUtil.readBytes(destFile));
                inquiryPrescriptionTemplateDO.setUrl(url);
            }
        } finally {
            FileUtil.del(destFile);
        }

        return BeanUtils.toBean(inquiryPrescriptionTemplateDO, InquiryPrescriptionTemplateRespVO.class);
    }

    @Override
    public String previewInquiryPrescriptionTemplate(Long id, Map<String, String[]> parameterMap) {
        // 校验存在
        InquiryPrescriptionTemplateDO inquiryPrescriptionTemplateDO = validateInquiryPrescriptionTemplateExists(id);
        // mock数据 生成pdf并上传
        Map<String, String> dataMap = mockDataPdfService.dataMap(inquiryPrescriptionTemplateDO.getType(), parameterMap);
        return pdfServiceFactory.getInstance().generateAndUpload(getSelf().getPrescriptionTemplate4Cache(id).getContent(), dataMap).getData();
    }


    @Override
    public PrescriptionTemplateField getSignNextTemplateField(Long id, String field) {
        InquiryPrescriptionTemplateDO templateDO = inquiryPrescriptionTemplateMapper.selectById(id);
        if (templateDO == null || CollUtil.isEmpty(templateDO.getTemplateFields())) {
            return null;
        }
        if (StringUtils.isBlank(field)) {
            return templateDO.getTemplateFields().stream()
                .filter(f -> Objects.equals(f.getFieldType(), TemplateFieldTypeEnum.SIGN_PICTURE.code))
                .min(Comparator.comparing(f -> Optional.ofNullable(f.getSorted()).orElse(0))).orElse(null);
        }
        return Optional.ofNullable(templateDO.getTemplateFields()).orElse(List.of()).stream()
            .filter(f -> Objects.equals(f.getFieldType(), TemplateFieldTypeEnum.SIGN_PICTURE.code))
            .sorted(Comparator.comparing(f -> Optional.ofNullable(f.getSorted()).orElse(0)))
            .dropWhile(f -> !Objects.equals(f.getField(), field)) // 找到第一个满足的条件为止，后面所有的元素
            .skip(1)  // 跳过当前匹配项
            .findFirst().orElse(null);
    }

    @Override
    public PrescriptionTemplateFieldDto getSignNextTemplateFieldValidUrl(TemplateSignCheckedDto checkedDto) {
        PrescriptionTemplateField nextTemplateField = getSignNextTemplateField(checkedDto.getTemplateId(), checkedDto.getField());
        if (nextTemplateField == null) {
            return null;
        }

        // 获取用户签名信息 判断是否必须
        InquirySignatureCaAuthRespVO caAuthRespVO = inquirySignatureCaAuthService.getInquirySignatureCaInfo(checkedDto, checkedDto.getSignaturePlatform() == null ? SignaturePlatformEnum.FDD :
            SignaturePlatformEnum.fromCode(checkedDto.getSignaturePlatform()));
        log.info("签章获取模板下一级字段及checkedDto:{},caAuthRespVO:{}", checkedDto, caAuthRespVO);
        // 自动开方 - 线下审方 ， 不检查签章参数
        if (!checkedDto.isCheckSignParam()) {
            return PrescriptionTemplateConvert.INSTANCE.convertField(nextTemplateField, caAuthRespVO);
        }

        if (CommonStatusEnum.isEnable(nextTemplateField.getRequired())) {
            if (caAuthRespVO.isDrawnSign() && StringUtils.isBlank(caAuthRespVO.getDrawnSignUrl())) {
                throw exception(INQUIRY_SIGNATURE_CA_AUTH_NOT_SIGN_URL);
            }
            if (StringUtils.isBlank(caAuthRespVO.getRealSignatureUrl())) {
                throw exception(INQUIRY_SIGNATURE_CA_AUTH_NOT_FREE_SIGN);
            }
        }

        // 如果接入三方且非降级,需要判断免签
        if (CommonStatusEnum.isEnable(nextTemplateField.getAccessPlatform()) && !caAuthRespVO.isDrawnSign()) {
            InquirySignaturePersonDO fddPersonDO = inquirySignaturePersonService.queryPersonByUserId(checkedDto.getUserId(), SignaturePlatformEnum.FDD, checkedDto.getSignaturePlatformConfigId());
            if (fddPersonDO == null) {
                throw exception(INQUIRY_SIGNATURE_CA_AUTH_NOT_FREE_SIGN);
            }
            // 校验基础CA免签
            if (!FddCaConstantEnum.isFreeSignAndValid(caAuthRespVO.getAuthorizeFreeSignStatus(), caAuthRespVO.getAuthorizeFreeSignDdl())) {
                throw exception(INQUIRY_SIGNATURE_CA_AUTH_NOT_FREE_SIGN);
            }

            // 判断校验其他CA免签
            if (!SignatureAppConfigIdEnum.isDefault(checkedDto.getSignaturePlatformConfigId())) {
                SignatureCAExtVO caExtVO = Optional.ofNullable(caAuthRespVO.getExt()).orElse(List.of()).stream().filter(c -> Objects.equals(c.getSignaturePlatformConfigId(), checkedDto.getSignaturePlatformConfigId()))
                    .findFirst().orElse(null);
                if (caExtVO == null || !FddCaConstantEnum.isFreeSignAndValid(caExtVO.getAuthorizeFreeSignStatus(), caExtVO.getAuthorizeFreeSignDdl())) {
                    throw exception(INQUIRY_SIGNATURE_CA_AUTH_NOT_FREE_SIGN);
                }
            }
        }

        // 处理处方图片 + 降级标识
        return PrescriptionTemplateConvert.INSTANCE.convertField(nextTemplateField, caAuthRespVO);
    }


    @Override
    public Map<String, String> getPrescriptionTemplateDefaultValueFields(Long templateId) {
        InquiryPrescriptionTemplateDO prescriptionTemplate = inquiryPrescriptionTemplateMapper.selectById(templateId);
        if (prescriptionTemplate == null || CollectionUtils.isEmpty(prescriptionTemplate.getTemplateFields())) {
            return Map.of();
        }
        return prescriptionTemplate.getTemplateFields().stream()
            .filter(f -> StringUtils.isNotBlank(f.getDefaultValue())).collect(Collectors.toMap(PrescriptionTemplateField::getField, PrescriptionTemplateField::getDefaultValue, (a, b) -> b));
    }

    @Override
    public List<InquiryPrescriptionTemplateFieldRespVO> getPrescriptionTemplateFields() {
        return Arrays.stream(PrescriptionTemplateFieldEnum.values())
            .map(pt -> InquiryPrescriptionTemplateFieldRespVO.builder()
                .field(pt.getField())
                .fieldName(pt.getFieldName())
                .fieldType(pt.getFieldType()).build())
            .toList();
    }


    @Override
    public List<PrescriptionTemplateField> getTemplateAccessPlatformFields(Long id) {
        if (id == null) {
            return new ArrayList<>();
        }
        InquiryPrescriptionTemplateDO prescriptionTemplate = inquiryPrescriptionTemplateMapper.selectById(id);
        if (prescriptionTemplate == null || CollectionUtils.isEmpty(prescriptionTemplate.getTemplateFields())) {
            return new ArrayList<>();
        }
        return prescriptionTemplate.getTemplateFields().stream().filter(p -> CommonStatusEnum.isEnable(p.getAccessPlatform())).toList();

    }

    @Override
    public List<PrescriptionTemplateField> getTemplateSignPictureFields(Long id) {
        if (id == null) {
            return new ArrayList<>();
        }
        InquiryPrescriptionTemplateDO prescriptionTemplate = inquiryPrescriptionTemplateMapper.selectById(id);
        if (prescriptionTemplate == null || CollectionUtils.isEmpty(prescriptionTemplate.getTemplateFields())) {
            return new ArrayList<>();
        }
        return prescriptionTemplate.getTemplateFields().stream().filter(p -> Objects.equals(p.getFieldType(), TemplateFieldTypeEnum.SIGN_PICTURE.code)).toList();
    }
}