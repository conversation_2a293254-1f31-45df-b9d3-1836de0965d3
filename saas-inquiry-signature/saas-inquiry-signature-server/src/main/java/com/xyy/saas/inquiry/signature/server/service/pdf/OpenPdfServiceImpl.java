package com.xyy.saas.inquiry.signature.server.service.pdf;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Image;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.AcroFields;
import com.lowagie.text.pdf.PdfFormField;
import com.lowagie.text.pdf.PdfReader;
import com.lowagie.text.pdf.PdfStamper;
import com.lowagie.text.pdf.TextField;
import com.xyy.saas.inquiry.enums.file.FileTypeEnum;
import com.xyy.saas.inquiry.signature.dto.pdf.RectangleInfo;
import com.xyy.saas.inquiry.util.FileApiUtil;
import com.xyy.saas.inquiry.util.UrlConUtil;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Service("openPdfService")
//@AllArgsConstructor
public class OpenPdfServiceImpl implements PdfService {

    private static final Logger log = LoggerFactory.getLogger(OpenPdfServiceImpl.class);

    @Override
    public boolean generateTemplate(String url, List<RectangleInfo> rectangles, File destFile) {
        StopWatch stopWatch = new StopWatch("generateTemplate");
        stopWatch.start("generateTemplate.0");
        try (PdfReader reader = new PdfReader(url);
            FileOutputStream fos = new FileOutputStream(destFile);) {
            PdfStamper stamper = new PdfStamper(reader, fos);

            stopWatch.stop();
            stopWatch.start("generateTemplate.1");
            // 先清除所有文本域
            // reader.removeFields();
            stamper.getAcroFields().removeFieldsFromPage(1);

            if (CollectionUtils.isNotEmpty(rectangles)) {
                // 添加文本域
                for (RectangleInfo rect : rectangles) {
                    TextField textField = addTextField(stamper, rect);
                    PdfFormField pdfFormField = textField.getTextField();
                    stamper.addAnnotation(pdfFormField, 1);
                }
            }

            stamper.close();
            fos.flush();
            return true;
        } catch (IOException | DocumentException e) {
            log.error("{}.generateTemplate 失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
            return false;
        } finally {
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    private TextField addTextField(PdfStamper stamper, RectangleInfo rect) {
        // 普通文本框
        Rectangle rectangle = new Rectangle(rect.getX(), rect.getY(), rect.getX() + rect.getWidth(), rect.getY() + rect.getHeight());
        TextField field = new TextField(stamper.getWriter(), rectangle, rect.getFieldName());
        // 防止读取pdf文档时，就是有旋转角度的
        field.setRotation(stamper.getReader().getPageRotation(1));
        // 调试坐标
        // field.setBackgroundColor(Color.LIGHT_GRAY);
        // field.setBorderColor(Color.RED);
        // field.setBorderStyle(PdfBorderDictionary.STYLE_SOLID);
        // field.setBorderWidth(1);
        field.setOptions(field.getOptions() | TextField.VISIBLE_BUT_DOES_NOT_PRINT);// 该字段可见，但不打印。
        if (rect.getFieldType() == 1) {
            field.setOptions(field.getOptions() | TextField.MULTILINE); // 允许多行
        }
        return field;
    }

    @Override
    public CommonResult<String> generateAndUpload(String url, Map<String, String> dataMap) {
        File destFile = FileUtil.createTempFile("pdf_", ".pdf", true);
        try {
            if (generate(url, dataMap, destFile)) {
                return CommonResult.success(FileApiUtil.createFileNoRecord(destFile.getName(), FileUtil.readBytes(destFile)));
            }
        } catch (IOException e) {
            return CommonResult.error(e.getMessage());
        } finally {
            FileUtil.del(destFile);
        }
        return CommonResult.error("生成pdf失败");
    }

    @Override
    public CommonResult<String> generateAndUpload(byte[] bytes, Map<String, String> dataMap) {
        File destFile = FileUtil.createTempFile("pdf_", ".pdf", true);
        try {
            if (generate(bytes, dataMap, destFile)) {
                return CommonResult.success(FileApiUtil.createFileNoRecord(destFile.getName(), FileUtil.readBytes(destFile)));
            }
        } catch (IOException e) {
            return CommonResult.error(e.getMessage());
        } finally {
            FileUtil.del(destFile);
        }
        return CommonResult.error("生成pdf失败");
    }

    public boolean generate(String url, Map<String, String> dataMap, File destFile) throws IOException {
        try (InputStream inputStream = UrlConUtil.getStreamRetry("GET", url, 3000)) {
            return generate(inputStream, dataMap, destFile);
        }
    }

    public boolean generate(byte[] bytes, Map<String, String> dataMap, File destFile) throws IOException {
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes)) {
            return generate(inputStream, dataMap, destFile);
        }
    }


    public boolean generate(InputStream is, Map<String, String> dataMap, File destFile) throws IOException {
        StopWatch stopWatch = new StopWatch("generate");
        stopWatch.start("generate.0");
        try (PdfReader reader = new PdfReader(is);
            FileOutputStream fos = new FileOutputStream(destFile);) {
            PdfStamper stamper = new PdfStamper(reader, fos);

            stopWatch.stop();
            stopWatch.start("generate.1");
            AcroFields fields = stamper.getAcroFields();

            // 添加字体
            if (CollectionUtils.isNotEmpty(PdfServiceFactory.getOpenpdfFontList())) {
                fields.setAllSubstitutionFonts(PdfServiceFactory.getOpenpdfFontList());
            }
            stopWatch.stop();
            stopWatch.start("generate.2");

            dataMap.forEach((fieldName, fieldValue) -> {
                if (fieldName == null || fieldValue == null) {
                    return;
                }
                try {
                    AcroFields.Item fieldItem = fields.getFieldItem(fieldName);
                    if (fieldItem == null) {
                        return;
                    }
                    // 图片
                    if (FileTypeEnum.isImage(fieldValue)) {
                        insertImage(stamper, fieldName, fieldValue);
                        return;
                    }
                    fields.setField(fieldName, fieldValue);
                    // 设置字体大小
                    // fields.setFieldProperty(fieldName, "textsize", 10f, null);
                } catch (DocumentException | IOException e) {
                    log.error("{}.generate 设置字段失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
                }
            });

            stamper.setFormFlattening(true);

            stamper.close();
            fos.flush();
            return true;
        } catch (DocumentException | IOException e) {
            log.error("{}.generate 失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
//            return false;
            throw e;
        } finally {
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.MILLISECONDS));
        }
    }

    private boolean insertImage(PdfStamper stamper, String fieldName, String url) {
        try {
            // 添加图片
            Image img = Image.getInstance(url);
            AcroFields acroFields = stamper.getAcroFields();

            // 获取字段信息
            float[] xywh = acroFields.getFieldPositions(fieldName);
            int pageNo = (int) xywh[0];
            // 设置图片位置
            img.setAbsolutePosition(xywh[1], xywh[2]);
            // 设置图片大小
            img.scaleToFit(xywh[3] - xywh[1], xywh[4] - xywh[2]);
            // 内容上方
            stamper.getOverContent(pageNo).addImage(img);
        } catch (DocumentException | IOException e) {
            log.error("{}.insertImage 失败: {}", this.getClass().getSimpleName(), e.getMessage(), e);
            return false;
        }
        return true;

    }


}
