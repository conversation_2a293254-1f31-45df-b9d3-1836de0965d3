package com.xyy.saas.inquiry.signature.server.service.signature;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_ELEC_EXISTS;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_PERSON_NOT_EXISTS;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_USER_SIGNATURE_BASE64IMG_NOT_EXISTS;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_USER_SIGNATURE_INFORMATION_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import cn.iocoder.yudao.module.system.api.permission.RoleApi;
import cn.iocoder.yudao.module.system.api.permission.dto.RoleRespDTO;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import com.fasc.open.api.v5_1.req.seal.CreatePersonalSealByTemplateReq;
import com.fasc.open.api.v5_1.req.seal.GetPersonalSealListReq;
import com.fasc.open.api.v5_1.res.seal.CreatePersonalSealByTemplateRes;
import com.fasc.open.api.v5_1.res.seal.GetPersonalSealListRes;
import com.fasc.open.api.v5_1.res.seal.PersonalSealInfo;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionAuditTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureAppConfigIdEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureBizTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureStatusEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.signature.server.controller.admin.signature.vo.InquiryUserElectronicSignatureSaveVO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquirySignatureRoleVO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureInformationVO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureManageVO;
import com.xyy.saas.inquiry.signature.server.convert.signature.InquiryUserSignatureConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquiryUserSignatureInformationDO;
import com.xyy.saas.inquiry.signature.server.dal.mysql.signature.InquiryUserSignatureInformationMapper;
import com.xyy.saas.inquiry.signature.server.service.ca.InquirySignatureCaAuthService;
import com.xyy.saas.inquiry.signature.server.service.fdd.FddSealService;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddBaseReqDto;
import com.xyy.saas.inquiry.signature.server.service.person.InquirySignaturePersonService;
import com.xyy.saas.inquiry.signature.server.util.FileUtils;
import com.xyy.saas.inquiry.util.FileApiUtil;
import jakarta.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 问诊用户(医生/药师/核对/调配)签章信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryUserSignatureInformationServiceImpl implements InquiryUserSignatureInformationService {

    @Resource
    private InquiryUserSignatureInformationMapper inquiryUserSignatureInformationMapper;

    @Resource
    @Lazy
    private InquirySignaturePersonService inquirySignaturePersonService;

    @Resource
    @Lazy
    private InquirySignatureCaAuthService inquirySignatureCaAuthService;

    @Resource
    private FddSealService fddSealService;

    @DubboReference
    private AdminUserApi adminUserApi;

    @DubboReference
    private PermissionApi permissionApi;

    @DubboReference
    private RoleApi roleApi;

    @DubboReference
    private TenantParamConfigApi tenantParamConfigApi;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createInquiryUserSignature(InquiryUserSignatureManageVO createReqVO) {
        if (StringUtils.isBlank(createReqVO.getSignatureImgBase64())) {
            throw new ServiceException(INQUIRY_USER_SIGNATURE_BASE64IMG_NOT_EXISTS);
        }
        // check role
        checkSignRole(null, createReqVO.getRoleIds().stream().toList());
        Long userId = adminUserApi.createUser(InquiryUserSignatureConvert.INSTANCE.convertUser(createReqVO));
        createReqVO.setUserId(userId);
        InquiryUserSignatureInformationDO signatureInformationDO = saveOrUpdateInquiryUserSignature(createReqVO);
        return signatureInformationDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateInquiryUserSignature(InquiryUserSignatureManageVO updateReqVO) {
        // check role
        checkSignRole(updateReqVO.getUserId(), updateReqVO.getRoleIds().stream().toList());
        adminUserApi.updateUser(InquiryUserSignatureConvert.INSTANCE.convertUser(updateReqVO));
        InquiryUserSignatureInformationDO informationDO = saveOrUpdateInquiryUserSignature(updateReqVO);
        return informationDO.getId();
    }

    private InquiryUserSignatureInformationDO saveOrUpdateInquiryUserSignature(InquiryUserSignatureManageVO createReqVO) {
        // 处理角色 - 此处特殊处理,仅能选 调配 核对 发药
        Set<Long> roleIds = getSignatureRoleList().stream().map(InquirySignatureRoleVO::getId).collect(Collectors.toSet());
        permissionApi.assignUserRoleWithRoleRanges(createReqVO.getUserId(), createReqVO.getRoleIds(), roleIds);

        if (StringUtils.isBlank(createReqVO.getSignatureImgBase64())) { // 修改时不改图片
            return new InquiryUserSignatureInformationDO();
        }
        if (FileUtils.isBlankImage(createReqVO.getSignatureImgBase64())) {
            throw new ServiceException(INQUIRY_USER_SIGNATURE_BASE64IMG_NOT_EXISTS);
        }

        InquiryUserSignatureInformationVO informationVO = InquiryUserSignatureInformationVO.builder().userId(createReqVO.getUserId())
            .signatureImgBase64(createReqVO.getSignatureImgBase64())
            .signaturePlatform(SignaturePlatformEnum.SELF.getCode())
            .signatureBizType(SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN.getCode())
            .tenantId(TenantContextHolder.getRequiredTenantId()).build();
        return saveOrUpdateInquiryUserSign(informationVO);
    }


    public InquiryUserSignatureInformationDO saveOrUpdateInquiryUserSign(InquiryUserSignatureInformationVO informationVO) {
        InquiryUserSignatureInformationDO signatureInformationDO = InquiryUserSignatureConvert.INSTANCE.convertDo(informationVO);
        Optional.ofNullable(FileUtils.base642byte(informationVO.getSignatureImgBase64())).ifPresent(bytes -> {
            String userSignatureImgUrl = FileApiUtil.createFileNoRecord(bytes);
            signatureInformationDO.setSignatureUrl(userSignatureImgUrl);
        });
        // check是否存在
        InquiryUserSignatureInformationDO informationDO = inquiryUserSignatureInformationMapper.queryOneByCondition(InquiryUserSignatureInformationVO
            .builder().tenantId(informationVO.getTenantId()).userId(informationVO.getUserId()).signaturePlatform(informationVO.getSignaturePlatform()).signatureBizType(informationVO.getSignatureBizType()).build());
        if (informationDO != null) {
            inquiryUserSignatureInformationMapper.updateById(signatureInformationDO.setId(informationDO.getId()));
        } else {
            inquiryUserSignatureInformationMapper.insert(signatureInformationDO);
        }
        return signatureInformationDO;
    }

    /**
     * 校验前置用户角色 (核对) (药师) 单人单选
     *
     * @param roleIds 角色ids
     */
    private void checkSignRole(Long userId, List<Long> roleIds) {
        // if (CollUtil.isEmpty(roleIds)) {
        //     return;
        // }
        // // 判断用户是否有药师
        // boolean hasPharmacist = permissionApi.hasAnyRoles(userId, RoleCodeEnum.PHARMACIST.getCode());
        // if (hasPharmacist) {
        //     throw exception(INQUIRY_USER_SIGNATURE_ROLE_ERROR, RoleCodeEnum.PHARMACIST.getName());
        // }
        // List<RoleRespDTO> roleRespDTOS = TenantUtils.executeIgnore(() -> roleApi.selectRoleListByIds(roleIds));
        // if (roleRespDTOS.stream().anyMatch(r -> Objects.equals(r.getCode(), RoleCodeEnum.CHECK.getCode())) && CollUtil.size(roleRespDTOS) > 1) {
        //     throw exception(INQUIRY_USER_SIGNATURE_ROLE_ERROR, RoleCodeEnum.CHECK.getName());
        // }
    }

    private InquiryUserSignatureInformationDO validateInquiryUserSignatureInformationExists(Long id) {
        final InquiryUserSignatureInformationDO informationDO = inquiryUserSignatureInformationMapper.selectById(id);
        if (informationDO == null) {
            throw exception(INQUIRY_USER_SIGNATURE_INFORMATION_NOT_EXISTS);
        }
        return informationDO;
    }

    @Override
    public InquiryUserSignatureManageVO getInquiryUserSignatureTenant(Long id, SignaturePlatformEnum signaturePlatformEnum) {
        // 查询用户基本信息
        AdminUserRespDTO user = adminUserApi.getUser(id);
        // 查询签章信息
        InquiryUserSignatureInformationVO signatureInformationDO = InquiryUserSignatureInformationVO.builder()
            .signaturePlatform(Optional.ofNullable(signaturePlatformEnum).orElse(SignaturePlatformEnum.SELF).getCode())
            .signatureBizType(SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN.getCode())
            .userId(id)
            .tenantId(TenantContextHolder.getRequiredTenantId())
            .build();
        InquiryUserSignatureInformationDO informationDO = inquiryUserSignatureInformationMapper.queryOneByCondition(signatureInformationDO);
        return InquiryUserSignatureConvert.INSTANCE.convert(user, informationDO);
    }


    @Override
    public String getInquiryUserSignature(Long userId, SignaturePlatformEnum signaturePlatformEnum, SignatureBizTypeEnum signatureBizTypeEnum) {
        // 先获取传入的平台类型 签章url信息
        InquiryUserSignatureInformationVO signatureInformationDO = InquiryUserSignatureInformationVO.builder()
            .signaturePlatform(signaturePlatformEnum == null ? null : signaturePlatformEnum.getCode())
            .signatureBizType(signatureBizTypeEnum == null ? SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN.getCode() : signatureBizTypeEnum.getCode())
            .tenantId(Objects.equals(signaturePlatformEnum, SignaturePlatformEnum.SELF) ? TenantContextHolder.getTenantId() : null)
            .userId(userId)
            .build();
        InquiryUserSignatureInformationDO informationDO = inquiryUserSignatureInformationMapper.queryOneByCondition(signatureInformationDO);
        if (informationDO != null) {
            return informationDO.getSignatureUrl();
        }
        return null;
    }


    @Override
    public Map<Long, String> getInquiryUserSignatures(List<Long> userIds, SignaturePlatformEnum signaturePlatformEnum) {
        // 先获取传入的平台类型 签章url信息
        InquiryUserSignatureInformationVO signatureInformationDO = InquiryUserSignatureInformationVO.builder()
            .signaturePlatform(Optional.ofNullable(signaturePlatformEnum).orElse(SignaturePlatformEnum.SELF).getCode())
            .signatureBizType(SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN.getCode())
            .userIds(userIds)
            .build();
        Map<Long, String> userSignMap = inquiryUserSignatureInformationMapper.queryByCondition(signatureInformationDO)
            .stream().collect(Collectors.toMap(InquiryUserSignatureInformationDO::getUserId, InquiryUserSignatureInformationDO::getSignatureUrl, (a, b) -> b));

        // 获取不到在获取自绘url 兼容核对/发药/调配/降级药师
        inquiryUserSignatureInformationMapper.queryByCondition(signatureInformationDO
            .setTenantId(TenantContextHolder.getTenantId())
            .setSignaturePlatform(SignaturePlatformEnum.SELF.getCode())).forEach(s -> {
            userSignMap.putIfAbsent(s.getUserId(), s.getSignatureUrl()); // 存在不放入 法大大优先级更高
        });
        return userSignMap;
    }

    @Override
    public void deleteBySignTaskId(String signTaskId, SignaturePlatformEnum signaturePlatformEnum) {
        inquiryUserSignatureInformationMapper.deleteBySignTaskId(signTaskId, signaturePlatformEnum);
    }

    @Override
    public InquiryUserSignatureInformationVO queryOneByCondition(InquiryUserSignatureInformationVO informationVO) {
        InquiryUserSignatureInformationDO informationDO = inquiryUserSignatureInformationMapper.queryOneByCondition(informationVO);
        return InquiryUserSignatureConvert.INSTANCE.convertVo(informationDO);
    }


    @Override
    public void updateUserSignatureInformation(InquiryUserSignatureInformationVO informationVO) {
        validateInquiryUserSignatureInformationExists(informationVO.getId());
        InquiryUserSignatureInformationDO informationDO = InquiryUserSignatureConvert.INSTANCE.convertDo(informationVO);
        inquiryUserSignatureInformationMapper.updateById(informationDO);
    }


    @Override
    public CommonResult<String> createUserElectronicSignature(InquiryUserElectronicSignatureSaveVO createReqVO) {
        // 1. 校验三方平台用户
        SignaturePlatformEnum platformEnum = createReqVO.getSignaturePlatform() == null ? SignaturePlatformEnum.FDD : SignaturePlatformEnum.fromCode(createReqVO.getSignaturePlatform());
        List<InquirySignaturePersonDO> personDOS = inquirySignaturePersonService.queryPersonByUserId(createReqVO.getUserId(),
            platformEnum);
        if (CollUtil.isEmpty(personDOS)) {
            throw exception(INQUIRY_SIGNATURE_PERSON_NOT_EXISTS);
        }

        // 2. 判断是否已存在电子签名
        InquiryUserSignatureInformationDO informationDO = inquiryUserSignatureInformationMapper.queryOneByCondition(InquiryUserSignatureInformationVO.builder()
            .userId(createReqVO.getUserId())
            .signaturePlatform(platformEnum.getCode())
            .signatureBizType(SignatureBizTypeEnum.USER_ELE_SIGN.getCode()).build());
        if (informationDO != null) {
            throw exception(INQUIRY_SIGNATURE_ELEC_EXISTS);
        }

        // 3. 创建法大大签名 + 缩放
        InquirySignaturePersonDO personDO = personDOS.getFirst();
        String electronicUrl = getFddUserSignatureElectronicUrl(personDO);
        String fileUrl = FileApiUtil.createFile(electronicUrl);

        // 4. 存储
        InquiryUserSignatureInformationDO signatureInformationDO = InquiryUserSignatureInformationDO.builder().userId(createReqVO.getUserId())
            .signaturePlatform(platformEnum.getCode())
            .signatureBizType(SignatureBizTypeEnum.USER_ELE_SIGN.getCode())
            .signatureStatus(SignatureStatusEnum.SIGNED.getStatusCode())
            .signatureUrl(fileUrl).build();
        inquiryUserSignatureInformationMapper.insert(signatureInformationDO);

        return CommonResult.success(fileUrl);
    }

    private String getFddUserSignatureElectronicUrl(InquirySignaturePersonDO personDO) {
        CommonResult<CreatePersonalSealByTemplateRes> sealByTempRes = getCreatePersonalSealByTemplate(personDO);
        if (sealByTempRes == null || sealByTempRes.isError()) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), sealByTempRes == null ? "" : sealByTempRes.getMsg());
        }
        // 4. 获取图片 转换存储
        GetPersonalSealListReq getPersonalSealListReq = new GetPersonalSealListReq();
        getPersonalSealListReq.setOpenUserId(personDO.getOpenUserId());
        CommonResult<GetPersonalSealListRes> listResCommonResult = fddSealService.getPersonalSealList(FddBaseReqDto.buildReq(SignatureAppConfigIdEnum.DEFAULT.getCode(), getPersonalSealListReq));
        if (listResCommonResult == null || listResCommonResult.isError() || CollUtil.isEmpty(listResCommonResult.getData().getSealInfos())) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), listResCommonResult == null ? "个人签章列表获取异常 或者 为空" : listResCommonResult.getMsg());
        }
        String sealUrl = listResCommonResult.getData().getSealInfos().stream().filter(s -> Objects.equals(sealByTempRes.getData().getSealId(), s.getSealId())).map(PersonalSealInfo::getPicFileUrl).findFirst().orElse(null);
        if (StringUtils.isBlank(sealUrl)) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "未查询到对应医生签章，请稍后重试");
        }
        return sealUrl;
    }

    private CommonResult<CreatePersonalSealByTemplateRes> getCreatePersonalSealByTemplate(InquirySignaturePersonDO personDO) {
        CreatePersonalSealByTemplateReq sealByImageReq = new CreatePersonalSealByTemplateReq();
        sealByImageReq.setOpenUserId(personDO.getOpenUserId());
        sealByImageReq.setSealName(personDO.getUserName() + DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        sealByImageReq.setSealSize("rectangle_20_11"); // 尺寸 mm
        sealByImageReq.setSealTemplateStyle("rectangle_frame"); // 矩形带框
        return fddSealService.createSealByTemplate(FddBaseReqDto.buildReq(SignatureAppConfigIdEnum.DEFAULT.getCode(), sealByImageReq));
    }

    @Override
    public CommonResult<?> deleteUserElectronicSignature(InquiryUserElectronicSignatureSaveVO updateVO) {

        inquiryUserSignatureInformationMapper.deleteByUserIdBizType(updateVO.getUserId(), updateVO.getSignaturePlatform(), SignatureBizTypeEnum.USER_ELE_SIGN.getCode());

        return CommonResult.success(null);
    }

    @Override
    public List<InquirySignatureRoleVO> getSignatureRoleList() {
        List<RoleCodeEnum> roleCodeEnums = RoleCodeEnum.listWzCadRole();
        // 判断门店是否有线下审方
        Integer prescriptionAuditType = tenantParamConfigApi.getParamConfigValueSelf2SysInteger(TenantParamConfigTypeEnum.INQUIRY_PRES_AUDIT_TYPE);
        if (Objects.equals(PrescriptionAuditTypeEnum.OFFLINE.getStatusCode(), prescriptionAuditType)) {
            roleCodeEnums.add(RoleCodeEnum.PHARMACIST);
        }

        List<RoleRespDTO> roleRespDTOS = roleApi.getSystemRoleIdByCodes(roleCodeEnums.stream().map(RoleCodeEnum::getCode).collect(Collectors.toList()));
        return InquiryUserSignatureConvert.INSTANCE.convertRole(roleRespDTOS);
    }


    @Override
    public void createMigrationUserSignatureInformation(String nickName, String signatureImgUrl, List<RoleCodeEnum> roleCodeEnums, Integer clockInStatus) {
        AdminUserSaveDTO adminUserSaveDTO = new AdminUserSaveDTO();
        adminUserSaveDTO.setNickname(nickName);
        Long userId = adminUserApi.createUser(adminUserSaveDTO);

        if (CollUtil.isNotEmpty(roleCodeEnums)) {
            List<RoleRespDTO> roleRespDTOS = roleApi.getSystemRoleIdByCodes(roleCodeEnums.stream().map(RoleCodeEnum::getCode).toList());
            // 处理角色 - 此处特殊处理,仅能选 调配 核对 发药
            Set<Long> roleIds = getSignatureRoleList().stream().map(InquirySignatureRoleVO::getId).collect(Collectors.toSet());
            permissionApi.assignUserRoleWithRoleRanges(userId, roleRespDTOS.stream().map(RoleRespDTO::getId).collect(Collectors.toSet()), roleIds);
        }

        // 创建签章信息
        InquiryUserSignatureInformationDO signatureInformationDO = InquiryUserSignatureInformationDO.builder()
            .signaturePlatform(SignaturePlatformEnum.SELF.getCode())
            .signatureBizType(SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN.getCode())
            .signatureStatus(SignatureStatusEnum.SIGNED.getStatusCode())
            .userId(userId)
            .tenantId(TenantContextHolder.getRequiredTenantId())
            .signatureUrl(signatureImgUrl)
            .build();

        inquiryUserSignatureInformationMapper.insert(signatureInformationDO);

    }
}