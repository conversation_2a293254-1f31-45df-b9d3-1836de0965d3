package com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 签章合同 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquirySignatureContractRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "4478")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "合同编号,系统生成", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("合同编号,系统生成")
    private String pref;

    @Schema(description = "签章平台  1-法大大", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("签章平台  1-法大大")
    private Integer signaturePlatform;

    @Schema(description = "合同状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("合同状态")
    private Integer contractStatus;

    /**
     * 自绘合同标识  0-是,1-否 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "自绘合同标识  0-是,1-否", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer selfDrawn;


    @Schema(description = "合同业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("合同业务类型")
    private Integer contractType;

    @Schema(description = "业务方唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "2369")
    @ExcelProperty("业务方唯一标识")
    private String bizId;

    @Schema(description = "三方签署任务id signTaskId", requiredMode = Schema.RequiredMode.REQUIRED, example = "10496")
    @ExcelProperty("三方签署任务id signTaskId")
    private String thirdId;

    /**
     * 发起方uerId
     */
    private Long initiatorUserId;

    @Schema(description = "发起方姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("发起方姓名")
    private String initiatorName;

    @Schema(description = "发起方联系方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发起方联系方式")
    private String initiatorMobile;

    @Schema(description = "参与方集合")
    @ExcelProperty("参与方集合")
    private List<ParticipantItem> participants;

    @Schema(description = "合同参数详情", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("合同参数详情")
    private String paramDetail;

    @Schema(description = "合同图片", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("合同图片")
    private String imgUrl;

    @Schema(description = "合同PDF文件", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("合同PDF文件")
    private String pdfUrl;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}