package com.xyy.saas.inquiry.signature.server.dal.dataobject.platform;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 签章回调日志 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_signature_callback_log")
// @KeySequence("saas_inquiry_signature_callback_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquirySignatureCallbackLogDO implements Serializable {

    /**
     * 自增主键
     */
    @TableId
    private Long id;
    /**
     * 签章平台 0-自签署 1-法大大
     */
    private Integer signaturePlatform;
    /**
     * 类型：事件ID
     */
    private String type;
    /**
     * 业务id
     */
    private String bizId;
    /**
     * 具体事件的请求参数，json字符串
     */
    private String bizContent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}