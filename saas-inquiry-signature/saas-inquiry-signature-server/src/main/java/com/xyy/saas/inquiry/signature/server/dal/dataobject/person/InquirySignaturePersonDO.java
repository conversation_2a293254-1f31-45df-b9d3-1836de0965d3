package com.xyy.saas.inquiry.signature.server.dal.dataobject.person;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 签章平台用户 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_signature_person")
// @KeySequence("saas_inquiry_signature_person_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquirySignaturePersonDO extends BaseDO {

    /**
     * 自增主键
     */
    @TableId
    private Long id;
    /**
     * 用户uid
     */
    private Long userId;
    /**
     * 法大大平台为该用户在该应用appId范围内分配的唯一标识
     */
    private String openUserId;

    /**
     * 签章平台  0-无 1-法大大 {@link com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum}
     */
    private Integer signaturePlatform;

    /**
     * 签章平台应用标识id
     */
    private Integer signaturePlatformConfigId;

    /**
     * 个人用户的法大大帐号，仅限手机号或邮箱
     */
    private String accountName;
    /**
     * 个人用户真实姓名
     */
    private String userName;
    /**
     * 个人手机号
     */
    private String mobile;
    /**
     * 个人银行账户号
     */
    private String bankAccountNo;
    /**
     * 证件类型 ：id_card: 身份证;passport: 护照;hk_macao: 港澳居民来往内地通行证;taiwan: 台湾居民来往大陆通行证
     */
    private String userIdentType;
    /**
     * 证件号。跟证件类型关联
     */
    private String userIdentNo;
    /**
     * 法大大签名印章ID
     */
    private String sealId;
    /**
     * 0-未认证,1-已认证,3已设置签名
     */
    private Integer userStatus;

}