package com.xyy.saas.inquiry.signature.server.controller.admin.person.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 签章平台用户 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InquirySignaturePersonRespVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "5521")
    @ExcelProperty("自增主键")
    private Long id;

    @Schema(description = "用户uid", requiredMode = Schema.RequiredMode.REQUIRED, example = "10984")
    @ExcelProperty("用户uid")
    private Long userId;

    @Schema(description = "法大大平台为该用户在该应用appId范围内分配的唯一标识", requiredMode = Schema.RequiredMode.REQUIRED, example = "7810")
    @ExcelProperty("法大大平台为该用户在该应用appId范围内分配的唯一标识")
    private String openUserId;

    /**
     * 签章平台  0-无 1-法大大 {@link com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum}
     */
    private Integer signaturePlatform;

    @Schema(description = "个人用户的法大大帐号，仅限手机号或邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("个人用户的法大大帐号，仅限手机号或邮箱")
    private String accountName;

    @Schema(description = "个人用户真实姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("个人用户真实姓名")
    private String userName;

    @Schema(description = "个人手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("个人手机号")
    private String mobile;

    @Schema(description = "个人银行账户号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("个人银行账户号")
    private String bankAccountNo;

    @Schema(description = "证件类型 ：id_card: 身份证;passport: 护照;hk_macao: 港澳居民来往内地通行证;taiwan: 台湾居民来往大陆通行证 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("证件类型 ：id_card: 身份证;passport: 护照;hk_macao: 港澳居民来往内地通行证;taiwan: 台湾居民来往大陆通行证 ")
    private String userIdentType;

    @Schema(description = "证件号。跟证件类型关联", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("证件号。跟证件类型关联")
    private String userIdentNo;

    @Schema(description = "法大大签名印章ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3985")
    @ExcelProperty("法大大签名印章ID")
    private String sealId;

    @Schema(description = "0-未认证,1-已认证,3已设置签名", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("0-未认证,1-已认证,3已设置签名")
    private Integer userStatus;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}