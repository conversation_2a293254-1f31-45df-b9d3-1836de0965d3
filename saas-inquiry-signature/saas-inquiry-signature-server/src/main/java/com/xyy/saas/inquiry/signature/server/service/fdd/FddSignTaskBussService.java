package com.xyy.saas.inquiry.signature.server.service.fdd;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.fasc.open.api.bean.common.OpenId;
import com.fasc.open.api.enums.common.IdTypeEnum;
import com.fasc.open.api.v5_1.req.doc.FddFileUrl;
import com.fasc.open.api.v5_1.req.doc.FileProcessReq;
import com.fasc.open.api.v5_1.req.doc.UploadFileByUrlReq;
import com.fasc.open.api.v5_1.req.signtask.AddActorsReq;
import com.fasc.open.api.v5_1.req.signtask.AddFieldReq;
import com.fasc.open.api.v5_1.req.signtask.DocFieldValueInfo;
import com.fasc.open.api.v5_1.req.signtask.FillFieldValuesReq;
import com.fasc.open.api.v5_1.req.signtask.FinishSignTaskReq;
import com.fasc.open.api.v5_1.req.signtask.GetOwnerDownloadUrlReq;
import com.fasc.open.api.v5_1.req.signtask.GetSignTaskUrlReq;
import com.fasc.open.api.v5_1.req.signtask.ListSignTaskActorReq;
import com.fasc.open.api.v5_1.req.signtask.SignTaskActorGetUrlReq;
import com.fasc.open.api.v5_1.req.signtask.SignTaskBaseReq;
import com.fasc.open.api.v5_1.req.signtask.SignTaskGetCerInfoReq;
import com.fasc.open.api.v5_1.res.doc.FileProcessRes;
import com.fasc.open.api.v5_1.res.doc.UploadFileByUrlRes;
import com.fasc.open.api.v5_1.res.signtask.CreateSignTaskRes;
import com.fasc.open.api.v5_1.res.signtask.GetSignTaskPreviewUrlRes;
import com.fasc.open.api.v5_1.res.signtask.ListSignTaskActorRes;
import com.fasc.open.api.v5_1.res.signtask.OwnerDownloadUrlRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskActorGetUrlRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskDetailRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskDocRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskGetCerInfoRes;
import com.xyy.saas.inquiry.enums.file.FileTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.pojo.signature.PlatformConfigExtDto;
import com.xyy.saas.inquiry.signature.dto.platform.PlatformConfigDto;
import com.xyy.saas.inquiry.signature.server.convert.fdd.InquiryFddConvert;
import com.xyy.saas.inquiry.signature.server.convert.signature.InquirySignatureContractConvert;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddBaseReqDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.FddSignTaskCreateDto;
import com.xyy.saas.inquiry.signature.server.service.person.InquirySignaturePersonService;
import com.xyy.saas.inquiry.signature.server.service.platform.InquirySignaturePlatformService;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import com.xyy.saas.inquiry.signature.server.util.FileUtils;
import com.xyy.saas.inquiry.util.FileApiUtil;
import io.swagger.v3.oas.models.security.SecurityScheme.In;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 法大大签章业务Service
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/20 14:23
 */
@Component
@Slf4j
public class FddSignTaskBussService {

    @Autowired
    private FddSignTaskService fddSignTaskService;

    @Autowired
    @Lazy
    private InquirySignatureContractService inquirySignatureContractService;

    @Resource
    @Lazy
    private InquirySignaturePersonService inquirySignaturePersonService;

    @Resource
    @Lazy
    private InquirySignaturePlatformService inquirySignaturePlatformService;

    @Autowired
    private FddDocService fddDocService;

    /**
     * 基于文档创建签署任务 - 需要填充好文件并上传文件服务器(不加密),这里传入上传文件的url
     *
     * @param taskCreateDto 创建dto
     * @return
     */
    public CommonResult<CreateSignTaskRes> createWithFile(FddSignTaskCreateDto taskCreateDto) {
        // 获取应用配置
        PlatformConfigExtDto platformConfig = inquirySignaturePlatformService.getSignaturePlatformConfig(SignaturePlatformEnum.FDD.getCode(), taskCreateDto.getConfigId());
        taskCreateDto.setConfigId(platformConfig.getConfigId());
        taskCreateDto.setPlatformConfig(platformConfig);

        // 1.上传处理网络文件
        CommonResult<FileProcessRes> processRes = processUploadFileByUrl(taskCreateDto);
        if (processRes.isError()) {
            return CommonResult.error(processRes.getMsg());
        }
        // 2.基于文档创建签署任务
        taskCreateDto.setAddDoc(true).setFileId(processRes.getData().getFileIdList().getFirst().getFileId());
        return fddSignTaskService.createByFile(FddBaseReqDto.buildReq(taskCreateDto.getConfigId(), InquiryFddConvert.INSTANCE.convertCreateWithFile(taskCreateDto)));
    }


    /**
     * 基于模板创建签署任务
     *
     * @param taskCreateDto 创建dto
     * @return 创建任务
     */
    public CommonResult<CreateSignTaskRes> createWithTemplate(FddSignTaskCreateDto taskCreateDto) {
        // 获取应用配置
        PlatformConfigExtDto platformConfig = inquirySignaturePlatformService.getSignaturePlatformConfig(SignaturePlatformEnum.FDD.getCode(), taskCreateDto.getConfigId());
        taskCreateDto.setConfigId(platformConfig.getConfigId());
        taskCreateDto.setPlatformConfig(platformConfig);

        // 1.基于模板创建签署任务
        CommonResult<CreateSignTaskRes> signTaskRes = fddSignTaskService.createWithTemplate(FddBaseReqDto.buildReq(platformConfig.getConfigId(), InquiryFddConvert.INSTANCE.convertCreateWithTemplate(taskCreateDto)));
        if (signTaskRes.isError()) {
            return CommonResult.error(signTaskRes.getMsg());
        }
        // 2.记录签署任务合同
        inquirySignatureContractService.createSignatureContract(InquirySignatureContractConvert.INSTANCE.convertTaskCreateSaveVo(taskCreateDto, signTaskRes.getData().getSignTaskId()));

        // 3.填充签署任务并提交
        if (MapUtils.isNotEmpty(taskCreateDto.getParamMap())) {
            // 3.1查询签署任务详情
            CommonResult<SignTaskDetailRes> signTaskDetailRes = getSignTaskDetail(platformConfig.getConfigId(), signTaskRes.getData().getSignTaskId());
            if (signTaskDetailRes.isError()) {
                return CommonResult.error(signTaskDetailRes.getMsg());
            }
            // 3.2 填充参数
            CommonResult<?> signTaskFillFieldsRes = signTaskFillFields(platformConfig.getConfigId(), signTaskRes.getData().getSignTaskId(), signTaskDetailRes.getData(), taskCreateDto.getParamMap());
            if (signTaskFillFieldsRes.isError()) {
                return CommonResult.error(signTaskFillFieldsRes.getMsg());
            }
            // 3.3 手动提交
            SignTaskBaseReq signTaskBaseReq = new SignTaskBaseReq();
            signTaskBaseReq.setSignTaskId(signTaskRes.getData().getSignTaskId());
            fddSignTaskService.start(FddBaseReqDto.buildReq(platformConfig.getConfigId(), signTaskBaseReq));
        }
        return signTaskRes;
    }

    /**
     * 填充签署任务参数
     *
     * @param signTaskId        签署任务ID
     * @param signTaskDetailRes 签署任务详情
     * @param paramMap          参数
     * @return res
     */
    public CommonResult<?> signTaskFillFields(Integer configId, String signTaskId, SignTaskDetailRes signTaskDetailRes, Map<String, String> paramMap) {
        if (StringUtils.isBlank(signTaskId) || signTaskDetailRes == null || MapUtils.isEmpty(paramMap)) {
            return CommonResult.error("填充签署任务参数异常");
        }
        List<DocFieldValueInfo> fieldValueInfos = new ArrayList<>();
        for (SignTaskDocRes doc : signTaskDetailRes.getDocs()) {
            for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                if (entry.getValue() == null) {
                    continue;
                }
                DocFieldValueInfo docFieldValueInfo = new DocFieldValueInfo();
                docFieldValueInfo.setDocId(doc.getDocId());
                docFieldValueInfo.setFieldName(entry.getKey());
                docFieldValueInfo.setFieldValue(entry.getValue());
                fieldValueInfos.add(docFieldValueInfo);
            }
        }
        FillFieldValuesReq fieldValuesReq = new FillFieldValuesReq();
        fieldValuesReq.setSignTaskId(signTaskId);
        fieldValuesReq.setDocFieldValues(fieldValueInfos);
        return fddSignTaskService.signTaskFillFields(FddBaseReqDto.buildReq(configId, fieldValuesReq));
    }


    /**
     * 获取签署任务详情
     *
     * @param signTaskId 签署任务id
     * @return
     */
    public CommonResult<SignTaskDetailRes> getSignTaskDetail(Integer configId, String signTaskId) {
        SignTaskBaseReq signTaskBaseReq = new SignTaskBaseReq();
        signTaskBaseReq.setSignTaskId(signTaskId);
        return fddSignTaskService.getDetail(FddBaseReqDto.buildReq(configId, signTaskBaseReq));
    }

    /**
     * 获取签署任务预览图
     *
     * @param signTaskId  签署任务id
     * @param redirectUrl 重定向url
     * @return
     */
    public CommonResult<String> getSignTaskPreviewUrl(Integer configId, String signTaskId, String redirectUrl) {
        GetSignTaskUrlReq getSignTaskUrlReq = new GetSignTaskUrlReq();
        getSignTaskUrlReq.setSignTaskId(signTaskId);
        getSignTaskUrlReq.setRedirectUrl(redirectUrl);
        CommonResult<GetSignTaskPreviewUrlRes> previewUrlRes = fddSignTaskService.getSignTaskPreviewUrl(FddBaseReqDto.buildReq(configId, getSignTaskUrlReq));
        if (previewUrlRes.isError()) {
            return CommonResult.error(previewUrlRes.getMsg());
        }
        return CommonResult.success(previewUrlRes.getData().getSignTaskPreviewUrl());
    }

    /**
     * 获取签署任务链接
     *
     * @param signTaskId  签署任务id
     * @param actorId     参与方id
     * @param redirectUrl 重定向url
     * @return
     */
    public CommonResult<String> signTaskActorGetUrl(Integer configId, String signTaskId, String actorId, String redirectUrl) {
        SignTaskActorGetUrlReq actorGetUrlReq = new SignTaskActorGetUrlReq();
        actorGetUrlReq.setSignTaskId(signTaskId);
        actorGetUrlReq.setActorId(actorId);
        actorGetUrlReq.setRedirectUrl(redirectUrl);
        CommonResult<SignTaskActorGetUrlRes> urlRes = fddSignTaskService.signTaskActorGetUrl(FddBaseReqDto.buildReq(configId, actorGetUrlReq));
        if (urlRes.isError()) {
            return CommonResult.error(urlRes.getMsg());
        }
        return CommonResult.success(urlRes.getData().getActorSignTaskEmbedUrl());
    }

    /**
     * 下载合同并上传至云存储
     *
     * @param signTaskId
     * @return
     */
    public String downContractAndUpload(Integer configId, String signTaskId, FileTypeEnum fileTypeEnum) {
        log.info("下载法大大合同并存储, signTaskId: {},fileTypeEnum:{}", signTaskId, fileTypeEnum);
        if (StringUtils.isBlank(signTaskId)) {
            return null;
        }
        PlatformConfigExtDto platformConfig = inquirySignaturePlatformService.getSignaturePlatformConfig(SignaturePlatformEnum.FDD.getCode(), configId);
        GetOwnerDownloadUrlReq downloadUrlReq = new GetOwnerDownloadUrlReq();
        downloadUrlReq.setOwnerId(OpenId.getInstance(IdTypeEnum.CORP.getCode(), platformConfig.getMainOpenCorpId()));
        downloadUrlReq.setSignTaskId(signTaskId);
        CommonResult<OwnerDownloadUrlRes> downloadUrlRes = fddSignTaskService.getOwnerDownloadUrl(FddBaseReqDto.buildReq(configId, downloadUrlReq));
        if (downloadUrlRes.isError()) {
            return null;
        }
        byte[] bytes = FileUtils.downLoadFile(downloadUrlRes.getData().getDownloadUrl(), fileTypeEnum);
        if (bytes == null) {
            return null;
        }
        String fileUrl = FileApiUtil.createFileCore(System.currentTimeMillis() + "." + fileTypeEnum.getType(), "", false, bytes);
        log.info("下载法大大合同并存储, signTaskId: {},fileUrl:{}", signTaskId, fileUrl);
        return fileUrl;
    }


    /**
     * 上传并处理文件
     *
     * @param url
     * @return
     */
    public CommonResult<FileProcessRes> processUploadFileByUrl(FddSignTaskCreateDto fddSignTaskCreateDto) {
        CommonResult<UploadFileByUrlRes> urlRes = uploadFileByUrl(fddSignTaskCreateDto.getConfigId(), fddSignTaskCreateDto.getFileUrl());
        if (urlRes.isError()) {
            return CommonResult.error(urlRes.getMsg());
        }
        FileProcessReq processReq = new FileProcessReq();
        FddFileUrl fddFileUrl = new FddFileUrl();
        fddFileUrl.setFddFileUrl(urlRes.getData().getFddFileUrl());
        fddFileUrl.setFileType("doc");
        fddFileUrl.setFileName(StringUtils.substringAfterLast(fddSignTaskCreateDto.getFileUrl(), "/"));
        processReq.setFddFileUrlList(Collections.singletonList(fddFileUrl));
        return fddDocService.process(FddBaseReqDto.buildReq(fddSignTaskCreateDto.getConfigId(), processReq));
    }

    /**
     * 上传网络文件
     *
     * @param url 网络文件url , https://xxx.pdf
     * @return
     */
    public CommonResult<UploadFileByUrlRes> uploadFileByUrl(Integer configId, String url) {
        UploadFileByUrlReq byUrlReq = new UploadFileByUrlReq();
        byUrlReq.setFileUrl(url);
        byUrlReq.setFileType("doc");
        return fddDocService.uploadFileByUrl(FddBaseReqDto.buildReq(configId, byUrlReq));
    }

    /**
     * 添加签署任务控件
     */
    public CommonResult<?> addField(FddSignTaskCreateDto taskCreateDto) {
        AddFieldReq fieldReq = InquiryFddConvert.INSTANCE.convertAddField(taskCreateDto);
        return fddSignTaskService.addField(FddBaseReqDto.buildReq(taskCreateDto.getConfigId(), fieldReq));
    }

    /**
     * 添加签署任务参与方
     */
    public CommonResult<?> addActor(FddSignTaskCreateDto taskCreateDto) {
        AddActorsReq actorsReq = InquiryFddConvert.INSTANCE.convertAddActor(taskCreateDto);
        return fddSignTaskService.addActor(FddBaseReqDto.buildReq(taskCreateDto.getConfigId(), actorsReq));
    }


    /**
     * 查询参与方证书文件
     *
     * @param signTaskId 签署任务id
     * @return
     */
    public CommonResult<SignTaskGetCerInfoRes> getSignTaskCerInfo(Integer configId, String signTaskId) {
        SignTaskGetCerInfoReq signTaskBaseReq = new SignTaskGetCerInfoReq();
        signTaskBaseReq.setSignTaskId(signTaskId);
        return fddSignTaskService.getCerInfo(FddBaseReqDto.buildReq(configId, signTaskBaseReq));
    }

    /**
     * 查询参与方身份信息
     *
     * @param signTaskId 签署任务id
     * @return
     */
    public CommonResult<List<ListSignTaskActorRes>> listSignTaskActor(Integer configId, String signTaskId) {
        ListSignTaskActorReq signTaskBaseReq = new ListSignTaskActorReq();
        signTaskBaseReq.setSignTaskId(signTaskId);
        return fddSignTaskService.listSignTaskActor(FddBaseReqDto.buildReq(configId, signTaskBaseReq));
    }

    /**
     * 结束签署任务
     *
     * @param configId
     * @param signTaskId
     * @return
     */
    public CommonResult<Void> signTaskFinish(Integer configId, String signTaskId) {
        FinishSignTaskReq signTaskBaseReq = new FinishSignTaskReq();
        signTaskBaseReq.setSignTaskId(signTaskId);
        return fddSignTaskService.signTaskFinish(FddBaseReqDto.buildReq(configId, signTaskBaseReq));
    }


}
