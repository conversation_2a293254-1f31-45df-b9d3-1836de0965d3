package com.xyy.saas.inquiry.signature.server.config.core;

import cn.iocoder.yudao.framework.apilog.config.YudaoApiLogAutoConfiguration;
import cn.iocoder.yudao.framework.tenant.config.YudaoTenantAutoConfiguration;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.infra.api.logger.ApiAccessLogApi;
import cn.iocoder.yudao.module.infra.api.logger.ApiErrorLogApi;
import cn.iocoder.yudao.module.system.api.logger.OperateLogApi;
import cn.iocoder.yudao.module.system.api.oauth2.OAuth2TokenApi;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;

/**
 * @Desc 商户配置类
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/9/9 下午2:27
 */
@AutoConfiguration(before = {YudaoTenantAutoConfiguration.class, YudaoApiLogAutoConfiguration.class})
@AutoConfigureBefore({YudaoTenantAutoConfiguration.class, YudaoApiLogAutoConfiguration.class})
public class SignatureConfig {

    @DubboReference
    private ApiAccessLogApi apiAccessLogApi;

    @DubboReference
    private ApiErrorLogApi apiErrorLogApi;

    @DubboReference
    private OAuth2TokenApi oAuth2TokenApi;

    @DubboReference
    private OperateLogApi operateLogApi;

    @DubboReference
    private FileApi fileApi;

}
