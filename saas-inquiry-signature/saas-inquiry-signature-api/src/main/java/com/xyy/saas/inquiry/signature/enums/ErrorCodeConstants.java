package com.xyy.saas.inquiry.signature.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 使用 2_007_000_000 段
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/12 20:47
 */
public interface ErrorCodeConstants {

    ErrorCode INQUIRY_USER_SIGNATURE_INFORMATION_NOT_EXISTS = new ErrorCode(2_007_000_000, "问诊用户(医生/药师/核对/调配)签章信息不存在");

    ErrorCode INQUIRY_USER_SIGNATURE_BASE64IMG_NOT_EXISTS = new ErrorCode(2_007_000_001, "请检查签名图片");


    ErrorCode INQUIRY_USER_SIGNATURE_ROLE_ERROR = new ErrorCode(2_007_000_002, "[{}]只可单人单角色");

    // ========== 处方笺模板 ==========
    ErrorCode INQUIRY_PRESCRIPTION_TEMPLATE_NOT_EXISTS = new ErrorCode(2_007_000_003, "处方笺模板不存在");

    ErrorCode INQUIRY_PRESCRIPTION_TEMPLATE_SORT_ERROR = new ErrorCode(2_007_000_004, "处方笺模板节点排序异常,请检查");

    ErrorCode INQUIRY_PRESCRIPTION_TEMPLATE_FIELD_ERROR = new ErrorCode(2_007_000_005, "处方笺模板签章图片字段错误,仅可选择不可新增");

    ErrorCode INQUIRY_PRESCRIPTION_TEMPLATE_FORM_EMPTY = new ErrorCode(2_007_000_006, "处方笺模板没有添加表单字段,请先设计");

    ErrorCode INQUIRY_PRESCRIPTION_TEMPLATE_SIGN_FIELD_NOT_BLANK = new ErrorCode(2_007_000_007, "处方笺[{}]签名不可为空");

    ErrorCode INQUIRY_PRESCRIPTION_TEMPLATE_USED_CANNOT_DISABLE = new ErrorCode(2_007_000_008, "该处方正在使用，如需禁用请先解绑后再做禁用操作！");
    ErrorCode INQUIRY_PRESCRIPTION_TEMPLATE_USED_CANNOT_DELETE = new ErrorCode(2_007_000_009, "该处方正在使用，如需删除请先解绑后再做删除操作！");

    /**
     * app端特殊处理code
     */
    ErrorCode INQUIRY_SIGNATURE_CA_AUTH_NOT_FREE_SIGN = new ErrorCode(2_007_000_010, "您当前未完成免验证签，请在App端我的-CA认证页面中完成免验证签后，再进行操作");
    ErrorCode INQUIRY_SIGNATURE_CA_AUTH_NOT_SIGN_URL = new ErrorCode(2_007_000_010, "请在App端我的-CA认证页面中完成认证或者手绘签名后，再进行操作");


    ErrorCode INQUIRY_SIGNATURE_PLATFORM_EXISTS = new ErrorCode(2_007_000_011, "签章平台配置[{}]已存在");

    ErrorCode INQUIRY_SIGNATURE_CA_AUTH_NOT_EXISTS = new ErrorCode(2_007_000_012, "CA认证不存在");

    ErrorCode INQUIRY_SIGNATURE_PLATFORM_NOT_EXISTS = new ErrorCode(2_007_000_013, "签章平台配置不存在");


    ErrorCode INQUIRY_SIGNATURE_CA_AUTH_USER_NOT_EXISTS = new ErrorCode(2_007_000_014, "CA认证用户基本信息不存在,请先完成外层实名认证");

    ErrorCode INQUIRY_SIGNATURE_CA_AUTHED_EXISTS = new ErrorCode(2_007_000_014, "当前手机号已认证法大大,可继续操作,如有疑问可联系管理员");

    ErrorCode INQUIRY_SIGNATURE_PERSON_NOT_EXISTS = new ErrorCode(2_007_000_015, "签章平台用户不存在");

    ErrorCode INQUIRY_SIGNATURE_ELEC_EXISTS = new ErrorCode(2_007_000_016, "用户电子签章图片已存在");


    // ============ 签章合同=============
    ErrorCode SIGNATURE_CONTRACT_NOT_EXISTS = new ErrorCode(2_008_000_016, "签章合同不存在");


}
