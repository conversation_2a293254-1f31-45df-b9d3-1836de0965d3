package com.xyy.saas.inquiry.pojo.condition;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import java.util.Arrays;

/**
 * 条件关系：支持小于、大于，小于等于、大于等于，等于五个条件关系；
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Getter
public enum ConditionRuleOp {
    LT("lt", "<", "小于"),
    GT("gt", ">", "大于"),
    LE("le", "<=", "小于等于"),
    GE("ge", ">=", "大于等于"),
    EQ("eq", "=", "等于"),
    ;

    private String type;
    private String el;
    private String desc;

    ConditionRuleOp(String type, String el, String desc) {
        this.type = type;
        this.el = el;
        this.desc = desc;
    }

    public static ConditionRuleOp fromType(String type) {
        return Arrays.stream(values())
            .filter(t -> StringUtils.equalsIgnoreCase(t.getType(), type))
            .findFirst()
            .orElse(null);
    }
}
