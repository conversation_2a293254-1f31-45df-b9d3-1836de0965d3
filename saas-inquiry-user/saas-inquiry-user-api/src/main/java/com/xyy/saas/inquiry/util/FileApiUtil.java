package com.xyy.saas.inquiry.util;


import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.framework.common.util.io.FileUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.infra.api.file.dto.FileCreateReqDTO;
import cn.iocoder.yudao.module.infra.framework.file.config.FileUploadProperties;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClientFactory;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileTypeUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;
import java.io.File;
import java.io.InputStream;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
@Import(FileUploadProperties.class)
public class FileApiUtil {

    @Resource
    private FileUploadProperties fileUploadProperties;
    @Resource
    private FileApi fileApi;
    @Resource
    private FileClientFactory fileClientFactory;

    private static FileUploadProperties fileUploadPropertiesGlobal;
    private static FileApi fileApiGlobal;
    private static FileClientFactory fileClientFactoryGlobal;

    @PostConstruct
    public void init() {
        FileApiUtil.fileUploadPropertiesGlobal = this.fileUploadProperties;
        FileApiUtil.fileApiGlobal = this.fileApi;
        FileApiUtil.fileClientFactoryGlobal = this.fileClientFactory;
    }

    /**
     * 将网络地址上传至自己服务器
     *
     * @param url 网络地址url
     * @return
     */
    @SneakyThrows
    public static String createFile(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }
        try (HttpResponse response = HttpUtil.createGet(url).execute();
            InputStream inputStream = response.bodyStream()
        ) {
            return createFile(IoUtil.readBytes(inputStream));
        }
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param file
     * @return
     */
    public static String createFile(String name, File file) {
        if (file == null) {
            return "";
        }
        return createFile(name, null, FileUtil.readBytes(file));
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param content 文件内容
     * @return 文件路径
     */
    public static String createFile(byte[] content) {
        return createFile(null, null, content);
    }

    public static String createFileNoRecord(byte[] content) {
        return createFileCore(null, null, false, content);
    }

    /**
     * 保存文件，并返回文件的访问路径
     *
     * @param path    文件路径
     * @param content 文件内容
     * @return 文件路径
     */
    public static String createFile(String path, byte[] content) {
        return createFile(null, path, content);
    }

    public static String createFileNoRecord(String path, byte[] content) {
        return createFileCore(null, path, false, content);
    }

    public static String createFile(String name, String path, byte[] content, boolean... pub) {
        return createFileCore(name, path, true, content, pub);
    }

    /**
     * 上传文件到存储服务（upload为本地上传，文件不走dubbo流量）
     *
     * @param name
     * @param path
     * @param record  是否记录文件
     * @param content
     * @param pub
     * @return
     */
    @SneakyThrows
    public static String createFileCore(String name, String path, boolean record, byte[] content, boolean... pub) {
        // 参数校验
        if (content == null || content.length == 0) {
            throw new IllegalArgumentException("文件内容不能为空");
        }

        log.info("开始创建文件: name={}, originalPath={}, size={}", name, path, content.length);

        try {
            // 计算默认的 path 名
            String type = FileTypeUtils.getMineType(content, name);
            if (StrUtil.isEmpty(path)) {
                path = FileUtils.generatePath(content, name);
            }
            // 如果 name 为空，则使用 path 填充
            if (StrUtil.isEmpty(name)) {
                name = path;
            }

            // 上传到文件存储器
            FileClient client = fileClientFactoryGlobal.getMasterFileClientFromCache();
            Assert.notNull(client, "客户端(master) 不能为空");

            String url;
            try {
                url = client.upload(content, path, type, pub);
                log.info("文件上传成功: name={}, path={}, url={}", name, path, url);
            } catch (Exception e) {
                log.error("文件上传失败: name={}, path={}, size={} {}", name, path, content.length, e.getMessage(), e);
                throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
            }

            if (!record) { // 不产生文件记录
                return url;
            }
            // 保存到数据库 - 如果失败需要删除已上传的文件
            FileCreateReqDTO file = new FileCreateReqDTO();
            file.setConfigId(client.getId());
            file.setName(name);
            file.setPath(path);
            file.setUrl(url);
            file.setType(type);
            file.setSize(content.length);

            try {
                Long id = fileApiGlobal.createFileRecord(file);
                log.info("文件记录保存成功: id={}, name={}, path={}", id, name, path);
            } catch (Exception e) {
                log.error("文件记录保存失败，尝试删除已上传的文件: path={} {}", path, e.getMessage(), e);
                // 尝试删除已上传的文件以保持数据一致性
                try {
                    client.delete(path);
                    log.info("已删除上传的文件: path={}", path);
                } catch (Exception deleteException) {
                    log.error("删除上传文件失败，可能存在孤立文件: path={} {}", path, e.getMessage(), deleteException);
                }
                throw new RuntimeException("保存文件记录失败: " + e.getMessage(), e);
            }

            return url;

        } catch (Exception e) {
            log.error("创建文件失败: name={}, path={}, size={} {}", name, path, content.length, e.getMessage(), e);
            throw e;
        }
    }
}

