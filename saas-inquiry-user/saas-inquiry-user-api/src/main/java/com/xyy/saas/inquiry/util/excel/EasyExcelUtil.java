package com.xyy.saas.inquiry.util.excel;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.constant.ErrorCodeConstants.ANALYSIS_IMPORT_EXCEL_ERROR;
import static com.xyy.saas.inquiry.constant.ErrorCodeConstants.EXCEL_DETAIL_DATA_IS_NULL;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.pojo.excel.ImportExcelVoDto;
import com.xyy.saas.inquiry.pojo.excel.ImportReqDto;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import com.xyy.saas.inquiry.util.FileApiUtil;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EasyExcelUtil {

    @Resource
    private FileApi fileApi;

    @Resource
    private ConfigApi configApi;

    private static final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 定义导入流程
     *
     * @param importReqDto 导入dto
     * @param tClass       转换类型
     * @param consumer     基础校验已经通过,传入的数据必然有值,进行数据库等逻辑校验处理即可
     * @param groups       校验参数的分组
     * @param <T>
     * @return
     */
    public <R extends ImportReqDto, T extends ImportExcelVoDto> ImportResultDto importData(R importReqDto, Class<T> tClass, BiConsumer<R, List<T>> consumer, Class<?>... groups) {
        log.info("importData#开始导入,excelName:{},limitCount:{}", importReqDto.getExcelName(), importReqDto.getLimitCount());

        // TODO 导入限流、最大条数限制等

        // 读取excel
        List<T> updateExcelVOS = analysisAndCheckExcel(importReqDto.getFileUrl(), tClass, importReqDto.getLimitCount());
        // 校验excel
        checkAndValidatorImportList(updateExcelVOS, groups);
        // 过滤掉异常数据
        List<T> readyImportDataList = updateExcelVOS.stream().filter(e -> StringUtils.isBlank(e.getErrMsg())).collect(Collectors.toList());
        if (CollUtil.isEmpty(readyImportDataList)) {
            return buildImportResult((long) updateExcelVOS.size(), importReqDto.getExcelName(), importReqDto.getConfirmCount(), tClass, updateExcelVOS);
        }
        List<T> errorList = updateExcelVOS.stream().filter(e -> StringUtils.isNotBlank(e.getErrMsg())).collect(Collectors.toList());
        // 处理数据
        consumer.accept(importReqDto, readyImportDataList);
        // 构建异常数据出参
        errorList.addAll(readyImportDataList.stream().filter(e -> StringUtils.isNotBlank(e.getErrMsg())).toList());

        return buildImportResult((long) updateExcelVOS.size(), importReqDto.getExcelName(), importReqDto.getConfirmCount(), tClass, errorList);
    }


    /**
     * 解析和校验excel
     *
     * @param fileUrl
     * @param clazz
     * @param limitCount
     * @return
     */
    public <T> List<T> analysisAndCheckExcel(String fileUrl, Class<T> clazz, Integer limitCount, String... ignoreExcelColumnNameList) {

        if (StringUtils.isBlank(fileUrl) || clazz == null || limitCount == null || limitCount == 0) {
            return Lists.newArrayList();
        }

        try (HttpResponse httpResponse = HttpUtil.createGet(fileUrl).execute()) {

            if (!httpResponse.isOk()) {
                log.error("analysisAndCheckExcel#createGet#解析Excel失败,url:{}", fileUrl);
                throw exception(ANALYSIS_IMPORT_EXCEL_ERROR, "文件下载异常");
            }

            // 构建解析excel监听器,此监听器会校验表头和校验最大条数
            AutoCheckExcelListener<T> autoCheckExcelListener = new AutoCheckExcelListener<T>(limitCount, clazz, ignoreExcelColumnNameList);
            EasyExcel.read(httpResponse.bodyStream(), clazz, autoCheckExcelListener).ignoreEmptyRow(false).sheet(0).doRead();

            final List<T> dataList = autoCheckExcelListener.getDataList();

            if (CollUtil.isEmpty(dataList)) {
                throw exception(EXCEL_DETAIL_DATA_IS_NULL);
            }
            // 基础格式校验
            return dataList;
        } catch (Exception e) {
            log.error("analysisAndCheckExcel#解析Excel失败,url:{}", fileUrl, e);
            throw exception(ANALYSIS_IMPORT_EXCEL_ERROR, e.getMessage());
        }
    }

    /**
     * 基础表单检查校验
     *
     * @param list   数据源
     * @param groups 校验分组
     * @param <T>
     * @return
     */
    public <T extends ImportExcelVoDto> void checkAndValidatorImportList(List<T> list, Class<?>... groups) {
        for (T t : list) {
            valid(t, validator, groups);
            t.valid();
        }
    }

    public static <T extends ImportExcelVoDto> void checkAndValidatorImportData(T data, Class<?>... groups) {
        valid(data, validator, groups);
    }

    private static <T extends ImportExcelVoDto> void valid(T data, Validator validator, Class<?>[] groups) {
        Set<ConstraintViolation<T>> validate = validator.validate(data, groups);
        String errMsg = Optional.ofNullable(validate).orElse(new HashSet<>()).stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(errMsg)) {
            data.setErrMsg(StringUtils.defaultString(data.getErrMsg()) + errMsg + ",");
        }
    }


    /**
     * 验证列表中是否存在重复元素
     *
     * @param data         待验证的数据列表
     * @param keyExtractor 用于提取比较键的函数
     * @param errorMessage 错误消息模板，可以使用%s作为占位符
     * @param <T>          列表元素类型
     * @param <K>          比较键类型
     */
    public static <T, K> void validateDuplicates(List<T> data, Function<T, K> keyExtractor, String errorMessage) {

        // 找出所有重复的键
        Map<K, List<T>> grouped = data.stream()
            .collect(Collectors.groupingBy(keyExtractor));

        // 收集重复的键
        String duplicates = grouped.entrySet().stream()
            .filter(e -> e.getValue().size() > 1)
            .map(Entry::getKey)
            .map(Object::toString) // 确保键可以转为字符串
            .collect(Collectors.joining(","));

        if (StringUtils.isNotBlank(duplicates)) {
            throw exception(ANALYSIS_IMPORT_EXCEL_ERROR, errorMessage + duplicates);
        }
    }

    /**
     * 构建导入结果Vo
     *
     * @param totalCount
     * @param excelName
     * @param clazz
     * @param errList
     * @param <T>
     * @return
     */
    public <T> ImportResultDto buildImportResult(Long totalCount, String excelName, Long confirmCount, Class<T> clazz, List<T> errList) {

        ImportResultDto result = ImportResultDto.builder()
            .totalCount(totalCount)
            .successCount(totalCount - errList.size() - confirmCount)
            .failureCount((long) errList.size())
            .confirmCount(confirmCount)
            .build();

        if (CollUtil.isNotEmpty(errList)) {
            result.setFileUrl(writeExcelAndUpload(excelName, clazz, errList)).setFileName(excelName + "异常数据");
        }

        return result;
    }


    /**
     * 写入excel并上传
     *
     * @param excelName
     * @param clazz
     * @param dataList
     * @return
     */
    public <T> String writeExcelAndUpload(String excelName, Class<T> clazz, List<T> dataList) {

        if (StringUtils.isBlank(excelName) || clazz == null || CollectionUtils.isEmpty(dataList)) {
            return "";
        }
        // 使用 ByteArrayOutputStream 来存储生成的 Excel 文件
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream();) {

            // 创建 ExcelWriterBuilder
            ExcelWriterBuilder writerBuilder = EasyExcel.write(outputStream, clazz);

            // 写入数据
            writerBuilder.sheet(excelName).doWrite(dataList);

            outputStream.close();

            // 上传并保存
            return FileApiUtil.createFileCore(excelName + ".xlsx", null, false, outputStream.toByteArray());
        } catch (IOException e) {
            log.error("writeExcelAndUpload#写入Excel失败,excelName:{},dataList:{}", excelName, dataList);
            return "";
        }
    }
}
