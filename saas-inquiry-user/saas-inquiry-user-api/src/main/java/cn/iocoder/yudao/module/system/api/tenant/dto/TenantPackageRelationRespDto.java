package cn.iocoder.yudao.module.system.api.tenant.dto;

import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.PackageNatureEnum;
import com.xyy.saas.inquiry.enums.tenant.PackagePaymentTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageEffectiveStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 门店套餐订单 DO
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TenantPackageRelationRespDto extends BaseDto {

    // 开通信息
    /**
     * 开通表id
     */
    private Long id;

    /**
     * 编码
     */
    private String pref;

    /**
     * 门店id
     */
    private Long tenantId;

    /**
     * 系统业务类型 {@link BizTypeEnum}
     */
    private Integer bizType;

    /**
     * 套餐类型
     */
    private Integer packageType;

    /**
     * 问诊业务类型 {@link com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum}
     */
    private Integer inquiryBizType;

    /**
     * 问诊审方类型 {@link com.xyy.saas.inquiry.enums.inquiry.InquiryAuditTypeEnum}
     */
    private Integer inquiryAuditType;

    // 套餐信息
    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 套餐包性质 {@link PackageNatureEnum}
     */
    private Integer packageNature;

    /**
     * 金额
     */
    private BigDecimal price;

    /**
     * 套餐额度
     */
    private List<InquiryPackageItem> inquiryPackageItems;

    /**
     * 套餐额度文案
     */
    private String inquiryPackageItemStr;

    /**
     * 问诊套餐包关联医院pref
     */
    private List<String> hospitalPrefs;
    /**
     * 问诊套餐包关联医院名称
     */
    private String hospitalName;

    /**
     * 套餐时限
     */
    private Integer term;

    /**
     * 时限类型 {@link com.xyy.saas.inquiry.enums.tenant.DateTermTypeEnum}
     */
    private Integer termType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 开通状态
     */
    private Integer status;

    /**
     * 门店套餐编号
     */
    private Long packageId;
    /**
     * 收款方式：0线上，1线下 {@link PackagePaymentTypeEnum}
     */
    private Integer paymentType;
    /**
     * 签约日期
     */
    private LocalDateTime signTime;
    /**
     * 签约人
     */
    private String signUser;
    /**
     * 代理人/中间人
     */
    private String proxyUser;
    /**
     * 签约渠道(eg:3智鹿)
     */
    private Integer signChannel;
    /**
     * 实收金额
     */
    private BigDecimal actualAmount;
    /**
     * 收款账户 eg:(微信对公-成都)
     */
    private Integer collectAccount;
    /**
     * 付款流水号
     */
    private String payNo;
    /**
     * 付款凭证url
     */
    private List<String> payVoucherUrls;

    /**
     * 备注
     */
    private String remark;

    @Schema(description = "状态变更信息备注")
    private String statusRemark;

    /**
     * 剩余到期天数
     */
    private Long endDay;

    // ---------------- 门店相关信息 -----------------

    @Schema(description = "门店名称", example = "xx")
    private String tenantName;

    @Schema(description = "省", example = "xx")
    private String province;

    @Schema(description = "市", example = "xx")
    private String city;

    @Schema(description = "区", example = "xx")
    private String area;

    // ---------------- 套餐相关信息 ----------------

    @Schema(description = "套餐编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String packagePref;

    @Schema(description = "门店编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String tenantPref;


    @Schema(description = "套餐时限", example = "1")
    private String termStr;


    /**
     * 生效状态 {@link TenantPackageEffectiveStatusEnum}
     */
    private Integer effective;

    /**
     * 剩余额度
     */
    private List<InquiryPackageItem> surplusCosts;

    @Schema(description = "剩余额度文案")
    private String surplusCostStr;

    // public String getInquiryPackageItemStr() {
    //     return InquiryPackageItem.getInquiryPackageItemStr(getInquiryBizType(), getInquiryPackageItems());
    // }

    public String getSurplusCostStr() {
        return InquiryPackageItem.getInquiryPackageItemStr(getInquiryBizType(), getSurplusCosts());
    }


    public Integer getEffective() {
        return TenantPackageEffectiveStatusEnum.getEffectiveStatus(bizType, startTime, endTime, TenantPackageRelationStatusEnum.fromStatusCode(status), surplusCosts);
    }
}