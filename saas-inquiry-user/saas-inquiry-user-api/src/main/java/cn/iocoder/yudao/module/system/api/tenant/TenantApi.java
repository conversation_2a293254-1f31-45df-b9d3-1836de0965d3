package cn.iocoder.yudao.module.system.api.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantRespDto;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 多门店的 API 接口
 *
 * <AUTHOR>
 */
public interface TenantApi {

    /**
     * 获得所有门店
     *
     * @return 门店编号数组
     */
//    List<Long> getTenantIdList();

    /**
     * 获取门店信息 对外暴露，根据header中tenantId获取
     *
     * @return 门店DTO
     */
    TenantDto getTenant();

    /**
     * 是否是连锁总部
     *
     * @return
     */
    default boolean isHeadTenant() {
        return Objects.equals(getTenant().getType(), TenantTypeEnum.CHAIN_HEADQUARTERS);
    }

    /**
     * 获取门店信息对外暴露
     *
     * @return 门店DTO
     */
    TenantDto getTenant(Long tenantId);

    /**
     * 批量获取门店信息对外暴露
     *
     * @return 门店DTO
     */
    List<TenantDto> getTenantList(List<Long> tenantId);

    default Map<Long, TenantDto> getTenantListMap(List<Long> tenantId) {
        return getTenantList(tenantId).stream().collect(Collectors.toMap(TenantDto::getId, Function.identity()));
    }

    /**
     * 根据门店名称或者编号等搜索门店列表
     *
     * @param nameOrPref
     * @return
     */
    List<TenantDto> getTenantList(String nameOrPref);


    PageResult<TenantRespDto> pageTenant(TenantReqDto tenantReqDto);

    /**
     * 校验门店是否合法
     *
     * @param id 门店编号
     */
    void validateTenant(Long id);

    /**
     * 获取当前门店下 所属连锁门店编号列表
     *
     * @return
     */
    List<Long> getTenantIdsByHeadId();

    /**
     * 根据名称或者营业执照名称查询 门店列表
     *
     * @param name
     * @return
     */
    List<TenantDto> getTenantListByNames(List<String> names);

    /**
     * 禁用/启用门店
     *
     * @param id
     */
    void updateTenantStatus(Long id, Integer status);

    /**
     * 查询门店和资质信息
     *
     * @return
     */
    TenantDto getTenantAndCertDto();
}
