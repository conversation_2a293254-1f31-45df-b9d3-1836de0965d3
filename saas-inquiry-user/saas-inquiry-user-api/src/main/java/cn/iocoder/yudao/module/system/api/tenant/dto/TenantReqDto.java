package cn.iocoder.yudao.module.system.api.tenant.dto;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/04/01 20:50
 */
@Data
public class TenantReqDto extends PageParam {

    @Schema(description = "编码", example = "xx100001")
    private String pref;

    @Schema(description = "门店id", example = "14967")
    private Long tenantId;

    @Schema(description = "门店名称", example = "14967")
    private String name;

    private String nameOrPref;

    @Schema(description = "联系手机")
    private String contactMobile;

    /**
     * 选项类型 {@link com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum}
     */
    @Schema(description = "选项类型", example = "1")
    private Integer optionType;

}
