package cn.iocoder.yudao.module.system.service.logger;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.iocoder.yudao.module.system.controller.admin.logger.vo.loginlog.LoginLogPageReqVO;
import cn.iocoder.yudao.module.system.convert.logger.LoggerConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.logger.LoginLogDO;
import cn.iocoder.yudao.module.system.dal.mysql.logger.LoginLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * 登录日志 Service 实现
 */
@Service
@Validated
public class LoginLogServiceImpl implements LoginLogService {

    @Resource
    private LoginLogMapper loginLogMapper;

    @Override
    public PageResult<LoginLogDO> getLoginLogPage(LoginLogPageReqVO pageReqVO) {
        return loginLogMapper.selectPage(pageReqVO);
    }

    @Override
    public Long createLoginLog(LoginLogCreateReqDTO reqDTO) {
        LoginLogDO loginLog = BeanUtils.toBean(reqDTO, LoginLogDO.class);
        loginLogMapper.insert(loginLog);
        return loginLog.getId();
    }

    @Override
    public void createLoginLog(List<LoginLogCreateReqDTO> reqDTOList) {
        List<LoginLogDO> loginLogDOS = LoggerConvert.INSTANCE.convertLoginLogCreateReqDTOList2LoginLogDOList(reqDTOList);
        loginLogMapper.insertBatch(loginLogDOS);
    }

    @Override
    public LoginLogDO getKickOffLogsByUserId(Long userId) {
        LoginLogDO loginLogDO = loginLogMapper.selectKickOffLogsByUserId(userId);
        // 根据被迫下线的记录，获取对应登录日志id
        if(ObjectUtil.isEmpty(loginLogDO) || ObjectUtil.isEmpty(loginLogDO.getLoginLogId())){
            return null;
        }
        return loginLogMapper.selectById(loginLogDO.getLoginLogId());
    }
}
