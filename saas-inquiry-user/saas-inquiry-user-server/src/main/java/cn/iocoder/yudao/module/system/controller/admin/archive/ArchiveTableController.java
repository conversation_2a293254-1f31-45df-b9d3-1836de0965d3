package cn.iocoder.yudao.module.system.controller.admin.archive;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.archive.vo.ArchiveTablePageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.archive.ArchiveTableDO;
import cn.iocoder.yudao.module.system.enums.archive.ArchiveProgressStatusEnum;
import cn.iocoder.yudao.module.system.service.archive.ArchiveTableService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 归档表配置 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 归档表配置")
@RestController
@RequestMapping("/system/archive-table")
@Validated
@Slf4j
public class ArchiveTableController {

    @Resource
    private ArchiveTableService archiveTableService;

    @PostMapping("/create")
    @Operation(summary = "创建归档配置")
    @PreAuthorize("@ss.hasPermission('system:archive-table:create')")
    public CommonResult<Long> createArchiveTable(@Valid @RequestBody ArchiveTableDO createReqVO) {
        return success(archiveTableService.createArchiveTable(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新归档配置")
    @PreAuthorize("@ss.hasPermission('system:archive-table:update')")
    public CommonResult<Boolean> updateArchiveTable(@Valid @RequestBody ArchiveTableDO updateReqVO) {
        archiveTableService.updateArchiveTable(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除归档配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:archive-table:delete')")
    public CommonResult<Boolean> deleteArchiveTable(@RequestParam("id") Long id) {
        archiveTableService.deleteArchiveTable(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得归档配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('system:archive-table:query')")
    public CommonResult<ArchiveTableDO> getArchiveTable(@RequestParam("id") Long id) {
        ArchiveTableDO archiveTable = archiveTableService.getArchiveTable(id);
        return success(archiveTable);
    }

    @GetMapping("/page")
    @Operation(summary = "获得归档配置分页")
    @PreAuthorize("@ss.hasPermission('system:archive-table:query')")
    public CommonResult<PageResult<ArchiveTableDO>> getArchiveTablePage(@Valid ArchiveTablePageReqVO pageReqVO) {
        PageResult<ArchiveTableDO> pageResult = archiveTableService.getArchiveTablePage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/list")
    @Operation(summary = "获得启用的归档配置列表")
    @PreAuthorize("@ss.hasPermission('system:archive-table:query')")
    public CommonResult<List<ArchiveTableDO>> getEnabledArchiveTableList() {
        List<ArchiveTableDO> list = archiveTableService.getEnabledArchiveTableList();
        return success(list);
    }

    @PostMapping("/execute/{id}")
    @Operation(summary = "手动执行归档任务")
    @Parameter(name = "id", description = "配置编号", required = true)
    public CommonResult<Boolean> executeArchive(@PathVariable("id") Long id) {
        ArchiveTableDO config = archiveTableService.getArchiveTable(id);
        if (config == null) {
            throw new IllegalArgumentException("归档配置不存在");
        }

        if (!CommonStatusEnum.ENABLE.getStatus().equals(config.getStatus())) {
            throw new IllegalStateException("归档配置未启用");
        }

        if (ArchiveProgressStatusEnum.RUNNING.getStatus().equals(config.getProgressStatus())) {
            throw new IllegalStateException("归档任务正在执行中");
        }

        // 异步执行归档任务
        new Thread(() -> {
            try {
                archiveTableService.executeArchiveForTable(config);
            } catch (Exception e) {
                log.error("手动执行归档任务失败", e);
            }
        }).start();

        return success(true);
    }


    @PostMapping("/execute-all")
    @Operation(summary = "手动执行所有归档任务")
    public CommonResult<Boolean> executeAllArchive() {
        // 异步执行所有归档任务
        new Thread(() -> {
            try {
                archiveTableService.executeAllArchiveTasks();
            } catch (Exception e) {
                log.error("手动执行所有归档任务失败", e);
            }
        }).start();

        return success(true);
    }
}
