package cn.iocoder.yudao.module.system.service.user;

import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantTransfersVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileChangeMobileReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserForgetPasswordVO;

/**
 * 后台用户 Service 接口
 *
 * <AUTHOR>
 */
public interface UserMobileBusinessService {

    /**
     * 用户修改手机号
     *
     * @param loginUserId
     * @param reqVO
     */
    void changeMobile(Long loginUserId, UserProfileChangeMobileReqVO reqVO);


    /**
     * 门店转让
     *
     * @param transfersVO
     */
    void transfers(TenantTransfersVO transfersVO);


    /**
     * 释放登录锁
     */
    void releaseLoginLock(Long userId);

    /**
     * 忘记密码 - 重新设置密码
     *
     * @param forgetPasswordVO
     */
    void forgetPassword(UserForgetPasswordVO forgetPasswordVO);
}
