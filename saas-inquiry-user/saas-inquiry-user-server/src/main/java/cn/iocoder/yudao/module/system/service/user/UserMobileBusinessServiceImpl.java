package cn.iocoder.yudao.module.system.service.user;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.CHANGE_MOBILE_FAIL_LOCKED;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.CHANGE_MOBILE_TIPS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.SMS_SEND_MOBILE_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_ADMIN_MOBILE_NOT_EXIST;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_MOBILE_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.sms.SmsCodeApi;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantTransfersVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileChangeMobileReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserForgetPasswordVO;
import cn.iocoder.yudao.module.system.convert.auth.AuthConvert;
import cn.iocoder.yudao.module.system.convert.user.UserConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantMapper;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantUserRelationMapper;
import cn.iocoder.yudao.module.system.dal.mysql.user.AdminUserMapper;
import cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.system.dal.redis.common.UserLockRedisDAO;
import cn.iocoder.yudao.module.system.enums.sms.SmsSceneEnum;
import cn.iocoder.yudao.module.system.mq.producer.user.UserBaseInfoChangeProducer;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.permission.RoleService;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.mq.user.UserBaseInfoChangeEvent;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 后台用户 Service 接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserMobileBusinessServiceImpl implements UserMobileBusinessService {

    @Resource
    private AdminUserService userService;

    @Resource
    private AdminUserMapper userMapper;

    @Resource
    private TenantMapper tenantMapper;

    @Resource
    private UserLockRedisDAO userLockRedisDAO;

    @Resource
    private SmsCodeApi smsCodeApi;

    @Resource
    private UserBaseInfoChangeProducer userBaseInfoChangeProducer;

    @Resource
    private TenantUserRelationMapper tenantUserRelationMapper;

    @Resource
    private PermissionService permissionService;

    @Resource
    private RoleService roleService;

    @Resource
    private PasswordEncoder passwordEncoder;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeMobile(Long loginUserId, UserProfileChangeMobileReqVO reqVO) {

        if (!reqVO.isConfirm()) {
            changeMobileValid(loginUserId, reqVO);
        }

        if (reqVO.isConfirm()) {
            // 更新user
            AdminUserDO userDO = AdminUserDO.builder().id(loginUserId).mobile(reqVO.getMobile()).build();
            userMapper.updateById(userDO);

            TenantUserRelationDO tenantUserRelation = tenantUserRelationMapper.selectByTenantUserId(loginUserId, TenantContextHolder.getRequiredTenantId());
            // 更新员工
            List<TenantUserRelationDO> userRelationDOS = TenantUtils.executeIgnore(() -> tenantUserRelationMapper.selectListByUserId(loginUserId));
            if (CollUtil.isNotEmpty(userRelationDOS)) {
                List<TenantUserRelationDO> list = userRelationDOS.stream().peek(tenantUserRelationDO -> {
                    tenantUserRelationDO.setMobile(reqVO.getMobile());
                }).toList();
                tenantUserRelationMapper.updateBatch(list);
            }
            // 发送用户基础信息修改mq
            userBaseInfoChangeProducer.sendMessage(UserBaseInfoChangeEvent.builder().msg(UserConvert.INSTANCE.convertChangeDto(tenantUserRelation)).build(), LocalDateTime.now().plusSeconds(10));
        }
    }

    private void changeMobileValid(Long loginUserId, UserProfileChangeMobileReqVO reqVO) {
        // 校验手机号
        userLockRedisDAO.isLocked(RedisKeyConstants.CHANGE_MOBILE_FAIL_COUNT, reqVO.getMobile(), CHANGE_MOBILE_FAIL_LOCKED);

        // 校验正确性
        AdminUserDO userExists = userService.validateUserExists(loginUserId);

        // 校验修改后的新手机号
        if (reqVO.isNewMobile()) {
            if (StringUtils.equals(userExists.getMobile(), reqVO.getMobile())) {
                throw exception(USER_MOBILE_EXISTS);
            }
            userService.validateMobileUnique(loginUserId, reqVO.getMobile());
        }

        // 校验验证码
        try {
            smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convertChangeMobile(reqVO, SmsSceneEnum.USER_UPDATE_MOBILE.getScene(), getClientIP()));
        } catch (ServiceException e) {
            userLockRedisDAO.lockRecord(RedisKeyConstants.CHANGE_MOBILE_FAIL_COUNT, reqVO.getMobile());
            throw e;
        }

        if (reqVO.isNewMobile()) {
            String msg = "";

            List<TenantUserRelationDO> userRelationDOS = TenantUtils.executeIgnore(() -> tenantUserRelationMapper.selectListByUserId(loginUserId));
            if (CollUtil.size(userRelationDOS) > 1) {
                msg += "您的账号关联了" + CollUtil.size(userRelationDOS) + "家药店，手机号更换成功后，关联药店的登录账号会同步修改;";
            }
            if (permissionService.hasAnyRoles(loginUserId, RoleCodeEnum.DOCTOR.getCode(), RoleCodeEnum.PHARMACIST.getCode())) {
                msg += "当前员工存在医生/药师角色,修改手机号后可能需要重新认证CA,是否确认修改?";
            }
            if (StringUtils.isNotBlank(msg)) {
                throw exception0(CHANGE_MOBILE_TIPS.getCode(), msg);
            }
            // 新手机号校验通过,设置确认修改
            reqVO.setConfirm(true);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transfers(TenantTransfersVO transfersVO) {

        // 连锁门店不让操作转让
        // TenantDO tenantDO = tenantMapper.selectById(transfersVO.getTenantId());
        // if (tenantDO == null || Objects.equals(tenantDO.getType(), TenantTypeEnum.CHAIN_STORE.getCode())) {
        //     throw exception(TENANT_TYPE_UN_SUPPORT);
        // }

        // 校验userId
        AdminUserDO user = userService.getUserById(transfersVO.getUserId());

        if (StringUtils.isBlank(user.getMobile())) {
            throw exception(TENANT_ADMIN_MOBILE_NOT_EXIST);
        }
        if (!TenantConstant.isSystemTenant()) {
            // 校验手机号
            userLockRedisDAO.isLocked(RedisKeyConstants.TRANSFERS_MOBILE_FAIL_COUNT, transfersVO.getMobile(), CHANGE_MOBILE_FAIL_LOCKED);
            // 校验验证码
            try {
                smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convertTransfers(transfersVO, SmsSceneEnum.TENANT_UPDATE_TRANSFERS.getScene(), getClientIP()));
            } catch (ServiceException e) {
                userLockRedisDAO.lockRecord(RedisKeyConstants.TRANSFERS_MOBILE_FAIL_COUNT, transfersVO.getMobile());
                throw e;
            }
        }
        // 更新门店联系人id
        tenantMapper.updateById(TenantDO.builder().id(transfersVO.getTenantId()).contactUserId(transfersVO.getUserId()).build());
        // 分配门店管理员角色
        RoleDO roleDO = roleService.selectByCode(RoleCodeEnum.STORE_ADMIN.getCode());
        TenantUtils.execute(transfersVO.getTenantId(), () -> permissionService.assignUserRoleWithRoleRanges(transfersVO.getUserId(), Collections.singleton(roleDO.getId()), Collections.singleton(roleDO.getId())));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void forgetPassword(UserForgetPasswordVO reqVO) {
        // 校验手机号锁定
        userLockRedisDAO.isLocked(RedisKeyConstants.FORGET_PASSWORD_MOBILE_FAIL_COUNT, reqVO.getMobile(), CHANGE_MOBILE_FAIL_LOCKED);

        AdminUserDO user = userService.getUserByMobileSystem(reqVO.getMobile());
        if (user == null) {
            throw exception(SMS_SEND_MOBILE_NOT_EXISTS);
        }
        // 校验验证码
        try {
            smsCodeApi.useSmsCode(AuthConvert.INSTANCE.convertForgetPassword(reqVO, SmsSceneEnum.USER_RESET_PASSWORD.getScene(), getClientIP()));
        } catch (ServiceException e) {
            userLockRedisDAO.lockRecord(RedisKeyConstants.FORGET_PASSWORD_MOBILE_FAIL_COUNT, reqVO.getMobile());
            throw e;
        }
        // 执行更新
        AdminUserDO updateObj = new AdminUserDO().setId(user.getId()).setPassword(passwordEncoder.encode(reqVO.getPassword()));  // 加密密码
        userMapper.updateById(updateObj);
    }

    @Override
    public void releaseLoginLock(Long userId) {
        AdminUserDO userDO = userMapper.selectById(userId);
        if (userDO == null) {
            return;
        }
        Stream.of(userDO.getMobile(), userDO.getUsername()).filter(StringUtils::isNotBlank).forEach(value -> {
            userLockRedisDAO.releaseLock(RedisKeyConstants.LOGIN_FAIL_COUNT, value);
        });
    }

}
