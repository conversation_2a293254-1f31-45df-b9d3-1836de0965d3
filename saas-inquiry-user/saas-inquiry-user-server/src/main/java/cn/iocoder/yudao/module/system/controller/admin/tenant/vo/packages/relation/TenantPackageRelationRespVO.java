package cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import cn.iocoder.yudao.module.system.enums.DictTypeConstants;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.constant.TenantPackageConstant;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.DateTermTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import com.xyy.saas.inquiry.pojo.tenant.TenantPackageRelationExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Schema(description = "管理后台 - 门店套餐订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantPackageRelationRespVO extends BaseDto {

    @Schema(description = "门店套餐关系id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13351")
    @ExcelProperty("门店套餐关系id")
    private Long id;

    @Schema(description = "编码", example = "xx100001")
    private String pref;

    @Schema(description = "门店套餐编号 0自定义套餐", requiredMode = Schema.RequiredMode.REQUIRED, example = "14967")
    @ExcelProperty("门店套餐编号 0自定义套餐")
    private Long packageId;

    @Schema(description = "门店id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14967")
    @ExcelProperty("门店id")
    private Long tenantId;

    /**
     * {@link BizTypeEnum}
     */
    @Schema(description = "系统业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer bizType;

    @Schema(description = "租户类型（1-单店 2连锁门店 3连锁总部）", example = "1")
    private Integer packageType;

    @Schema(description = "套餐名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("套餐名")
    private String packageName;

    /**
     * 问诊医院id
     */
    @Schema(description = "问诊医院id", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private List<String> hospitalPrefs;


    @Schema(description = "问诊业务类型", example = "1")
    private Integer inquiryBizType;

    @Schema(description = "问诊审方类型", example = "1")
    private Integer inquiryAuditType;

    /**
     * 套餐定价
     */
    @Schema(description = "套餐定价", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private BigDecimal price;

    /**
     * 问诊方式类型 {@link InquiryWayTypeEnum}
     */
    @Schema(description = "问诊方式类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private List<Integer> inquiryWayTypes;

    /**
     * 问诊包信息
     */
    @Schema(description = "问诊包信息", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private List<InquiryPackageItem> inquiryPackageItems;

    /**
     * {@link TenantPackageRelationStatusEnum}
     */
    @Schema(description = "订单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "订单状态", converter = DictConvert.class)
    @DictFormat(DictTypeConstants.USER_SEX)
    private Integer status;

    @Schema(description = "套餐包性质：0赠送 1购买 2体验", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer packageNature;

    @Schema(description = "开始时间")
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    @Schema(description = "收款方式：0线上，1线下", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("收款方式：0线上，1线下")
    private Integer paymentType;

    @Schema(description = "签约日期")
    @ExcelProperty("签约日期")
    private LocalDateTime signTime;

    @Schema(description = "签约人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("签约人")
    private String signUser;

    @Schema(description = "签约人工号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("签约人工号")
    private String signUserNo;

    @Schema(description = "代理人/中间人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("代理人/中间人")
    private String proxyUser;

    @Schema(description = "签约渠道(eg:3智鹿)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("签约渠道(eg:3智鹿)")
    private Integer signChannel;

    @Schema(description = "实收金额 ")
    @ExcelProperty("实收金额 ")
    private BigDecimal actualAmount;

    @Schema(description = "收款账户 eg:(微信对公-成都)", requiredMode = Schema.RequiredMode.REQUIRED, example = "31497")
    @ExcelProperty("收款账户 eg:(微信对公-成都)")
    private Integer collectAccount;

    @Schema(description = "付款流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("付款流水号")
    private String payNo;

    @Schema(description = "付款凭证url", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("付款凭证url")
    private List<String> payVoucherUrls;

    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "扩展信息")
    private TenantPackageRelationExtDto ext;

    @ExcelProperty("状态变更原因")
    @Schema(description = "状态变更信息备注")
    private String statusRemark;

    // ---------------- 门店相关信息 -----------------

    @Schema(description = "门店名称", example = "xx")
    private String tenantName;

    @Schema(description = "省", example = "xx")
    private String province;

    @Schema(description = "市", example = "xx")
    private String city;

    @Schema(description = "区", example = "xx")
    private String area;

    // ---------------- 套餐相关信息 ----------------

    @Schema(description = "问诊医院", example = "xx医院")
    private String hospitalName;

    private Integer term;
    private Integer termType;

    @Schema(description = "套餐时限", example = "1")
    private String termStr;

    @Schema(description = "问诊包信息", example = "图文300,视频不限")
    private String inquiryPackageItemStr;

    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否生效", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer effective;

    /**
     * 是否冲额度套餐 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否冲额", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer recharge;


    @Schema(description = "套餐编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String packagePref;

    @Schema(description = "门店编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String tenantPref;


    public String getInquiryPackageItemStr() {
        return InquiryPackageItem.getInquiryPackageItemStr(getInquiryBizType(), getInquiryPackageItems());
    }

    public String getTermStr() {
        if (getTerm() == null || getTermType() == null) {
            return null;
        }
        return getTerm() + DateTermTypeEnum.fromCode(getTermType()).getDesc();
    }

    public Integer getRecharge() {
        return TenantPackageConstant.isRechargePackageId(getPackageId()) ? CommonStatusEnum.ENABLE.getStatus() : CommonStatusEnum.DISABLE.getStatus();
    }

}