package cn.iocoder.yudao.module.system.dal.mysql.archive;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.archive.vo.ArchiveTablePageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.archive.ArchiveTableDO;
import cn.iocoder.yudao.module.system.enums.archive.ArchiveProgressStatusEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.Arrays;
import java.util.List;

/**
 * 归档表配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ArchiveTableMapper extends BaseMapperX<ArchiveTableDO> {

    /**
     * 查询启用的归档配置
     *
     * @return 启用的归档配置列表
     */
    default List<ArchiveTableDO> selectEnabledConfigs() {
        return selectList(new LambdaQueryWrapperX<ArchiveTableDO>()
            .eq(ArchiveTableDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
            .in(ArchiveTableDO::getProgressStatus, Arrays.asList(
                ArchiveProgressStatusEnum.PENDING.getStatus(),
                ArchiveProgressStatusEnum.FAILED.getStatus()
            ))
        );
    }

    /**
     * 根据表名查询配置
     *
     * @param tableName 表名
     * @return 归档配置
     */
    default ArchiveTableDO selectByTableName(String tableName) {
        return selectOne(new LambdaQueryWrapperX<ArchiveTableDO>()
            .eq(ArchiveTableDO::getTableName, tableName)
        );
    }

    /**
     * 查询执行中的任务
     *
     * @return 执行中的任务列表
     */
    default List<ArchiveTableDO> selectRunningTasks() {
        return selectList(new LambdaQueryWrapperX<ArchiveTableDO>()
            .eq(ArchiveTableDO::getProgressStatus, ArchiveProgressStatusEnum.RUNNING.getStatus())
        );
    }

    /**
     * 分页查询归档配置
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    default PageResult<ArchiveTableDO> selectPage(ArchiveTablePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ArchiveTableDO>()
            .likeIfPresent(ArchiveTableDO::getTableName, reqVO.getTableName())
            .eqIfPresent(ArchiveTableDO::getStatus, reqVO.getStatus())
            .orderByDesc(ArchiveTableDO::getCreateTime)
        );
    }
}
