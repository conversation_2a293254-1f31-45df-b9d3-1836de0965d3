package cn.iocoder.yudao.module.system.controller.admin.archive.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 归档表配置分页查询 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 归档表配置分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ArchiveTablePageReqVO extends PageParam {

    @Schema(description = "表名", example = "inquiry_record")
    private String tableName;

    @Schema(description = "状态，参见 CommonStatusEnum 枚举类", example = "1")
    private Integer status;
}
