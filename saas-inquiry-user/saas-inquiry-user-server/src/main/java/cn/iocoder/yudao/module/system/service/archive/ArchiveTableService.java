package cn.iocoder.yudao.module.system.service.archive;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.archive.vo.ArchiveTablePageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.archive.ArchiveTableDO;

import java.util.List;

/**
 * 归档表配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ArchiveTableService {

    /**
     * 创建归档配置
     *
     * @param archiveTable 归档配置
     * @return 归档配置ID
     */
    Long createArchiveTable(ArchiveTableDO archiveTable);

    /**
     * 更新归档配置
     *
     * @param archiveTable 归档配置
     */
    void updateArchiveTable(ArchiveTableDO archiveTable);

    /**
     * 删除归档配置
     *
     * @param id 归档配置ID
     */
    void deleteArchiveTable(Long id);

    /**
     * 获得归档配置
     *
     * @param id 归档配置ID
     * @return 归档配置
     */
    ArchiveTableDO getArchiveTable(Long id);

    /**
     * 分页查询归档配置
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    PageResult<ArchiveTableDO> getArchiveTablePage(ArchiveTablePageReqVO reqVO);

    /**
     * 获得启用的归档配置列表
     *
     * @return 归档配置列表
     */
    List<ArchiveTableDO> getEnabledArchiveTableList();

    /**
     * 执行所有归档任务
     */
    void executeAllArchiveTasks();

    /**
     * 执行指定表的归档任务
     *
     * @param config 归档配置
     */
    void executeArchiveForTable(ArchiveTableDO config);

    /**
     * 更新归档状态
     *
     * @param configId 配置ID
     * @param progressStatus 执行状态
     * @param errorMessage 错误信息
     */
    void updateArchiveStatus(Long configId, Integer progressStatus, String errorMessage);

    /**
     * 更新归档进度
     *
     * @param configId 配置ID
     * @param lastMinId 最后处理的最小ID
     */
    void updateArchiveProgress(Long configId, Long lastMinId);
}
