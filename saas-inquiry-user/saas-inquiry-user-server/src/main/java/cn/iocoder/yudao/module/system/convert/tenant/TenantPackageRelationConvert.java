package cn.iocoder.yudao.module.system.convert.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationPageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationRespDto;
import cn.iocoder.yudao.module.system.api.tenant.migration.dto.MigrationTrailPackageDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantAndPackageExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantPackageRelationOpenExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantPackageRelationUpdateExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationStatusChangeReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO;
import com.xyy.saas.inquiry.constant.TenantPackageConstant;
import com.xyy.saas.inquiry.enums.inquiry.InquiryAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.DateTermTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.PackageNatureEnum;
import com.xyy.saas.inquiry.enums.tenant.PackagePaymentTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import com.xyy.saas.inquiry.pojo.migration.MigrationPackageRespDto;
import com.xyy.saas.inquiry.pojo.tenant.TenantPackageRelationExtDto;
import com.xyy.saas.inquiry.pojo.tenant.TenantPackageRelationStatusChangeDto;
import com.xyy.saas.inquiry.util.LocalDateUtil;
import com.xyy.saas.inquiry.util.PrefUtil;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * 门店 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantPackageRelationConvert {

    TenantPackageRelationConvert INSTANCE = Mappers.getMapper(TenantPackageRelationConvert.class);

    //    @Mapping(target = "inquiryWayTypes",expression = "java(createReqVO.getInquiryPackageItems().stream().map(InquiryPackageItem::getInquiryWayType).distinct().collect(java.util.stream.Collectors.toList()))")
    @Mapping(target = "inquiryPackageItems", expression = "java(com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem.convertToNewFormat(createReqVO.getInquiryBizType(),createReqVO.getInquiryPackageItems(),createReqVO.getHospitalPrefs()))")
    TenantPackageRelationDO tenantPackageRelationUpdateReqVO2DO(TenantPackageRelationSaveReqVO createReqVO);

    default TenantPackageRelationDO tenantPackageRelationInitReqVO2DO(TenantPackageRelationSaveReqVO createReqVO) {

        TenantPackageRelationDO relationDO = tenantPackageRelationUpdateReqVO2DO(createReqVO);
        relationDO.setPref(PrefUtil.getTcbPref());
        relationDO.setHospitalPrefs(extractHospitalPrefsFromItems(createReqVO.getInquiryPackageItems(), createReqVO.getHospitalPrefs()));
        relationDO.setInquiryWayTypes(Optional.ofNullable(createReqVO.getInquiryPackageItems()).orElse(List.of()).stream()
            .flatMap(i -> i.effectiveItems().stream())
            .map(InquiryPackageItem::getInquiryWayType).distinct().collect(Collectors.toList()));
        relationDO.setPrescriptionTypes(Optional.ofNullable(createReqVO.getInquiryPackageItems()).orElse(List.of()).stream()
            .flatMap(i -> Optional.ofNullable(i.getPrescriptionValue()).orElse(List.of()).stream()).distinct().collect(Collectors.toList()));

        return relationDO;
    }

    /**
     * 从 InquiryPackageItems 中提取 hospitalPrefs，用于兼容处理
     *
     * @param items                 问诊套餐项目列表
     * @param fallbackHospitalPrefs 备用的医院编码列表
     * @return 医院编码列表
     */
    default List<String> extractHospitalPrefsFromItems(List<InquiryPackageItem> items, List<String> fallbackHospitalPrefs) {
        // 优先从 items 中提取
        List<String> extracted = InquiryPackageItem.extractHospitalPrefs(items);
        if (CollUtil.isNotEmpty(extracted)) {
            return extracted;
        }

        // 如果提取不到，使用备用值
        return fallbackHospitalPrefs != null ? fallbackHospitalPrefs : new ArrayList<>();
    }


    TenantPackageRelationDO invalidConvert(TenantPackageRelationStatusChangeReqVO invalidReqVO);

    @Named("defaultVoMapping")
    @Mapping(target = "statusRemark", expression = "java(tp.getStatusChangeInfo() == null ? null : tp.getStatusChangeInfo().getRemark())")
    TenantPackageRelationRespVO convertVO(TenantPackageRelationDO tp);

    /**
     * 转换为响应VO，并处理 hospitalPref 兼容性
     *
     * @param tp 套餐关系DO
     * @return 响应VO
     */
    default TenantPackageRelationRespVO convertVOWithHospitalPrefCompat(TenantPackageRelationDO tp) {
        TenantPackageRelationRespVO respVO = convertVO(tp);
        if (respVO != null) {
            // 为旧格式的 inquiryPackageItems 设置 hospitalPref
            respVO.setInquiryPackageItems(InquiryPackageItem.convertToNewFormat(tp.getInquiryBizType(), respVO.getInquiryPackageItems(), tp.getHospitalPrefs()));
        }
        return respVO;
    }

    /**
     * 填充套餐信息 至 套餐包
     * @param vo 套餐包
     * @param pk 套餐
     */
//    default void fillPackage(TenantPackageRelationRespVO vo, TenantPackageRespVO pk){
//        vo.setPlatformReview(pk.getPlatformReview());
//        vo.setPrice(pk.getPrice());
//        vo.setTerm(pk.getTerm() + DateTermTypeEnum.fromCode(pk.getTermType()).getDesc());
//        vo.setInquiryPackageItemStr(pk.getInquiryPackageItemStr());
//        vo.setHospitalName(pk.getHospitalName());
//    }

//    @Mapping(source = "createTime",target = "openTime")
//    @Mapping(source = "id",target = "tenantPackageId")
//    TenantPackageRelationRespDto convertDo2Dto(TenantPackageRelationDO p);

    /**
     * 填充套餐关系信息  服务时间和状态
     *
     * @param tenantPackageDO       套餐
     * @param tenantPackageRelation 套餐订单
     */
    default void fillPackageRelationInfo(TenantPackageDO tenantPackageDO, TenantPackageRelationDO tenantPackageRelation) {
        LocalDateTime endTime = DateTermTypeEnum.fromCode(tenantPackageDO.getTermType()).calculationTermDate(tenantPackageRelation.getStartTime(), tenantPackageDO.getTerm());
        tenantPackageRelation.setEndTime(tenantPackageRelation.getEndTime() == null ? endTime : tenantPackageRelation.getEndTime());
        fillPackageInfo(tenantPackageDO, tenantPackageRelation);
        tenantPackageRelation.setInquiryPackageItems(InquiryPackageItem.convertToNewFormat(tenantPackageDO.getInquiryBizType(), tenantPackageDO.getInquiryPackageItems(), tenantPackageDO.getHospitalPrefs()));
    }

    /**
     * 填充套餐信息
     *
     * @param tenantPackageDO
     * @param tenantPackageRelation
     */
    @Mapping(target = "packageId", source = "id")
    @Mapping(target = "status", expression = "java(com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum.NORMAL.getCode())")
    @Mapping(target = "packageName", source = "name")
    // @Mapping(target = "inquiryWayTypes", expression = "java(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(tenantPackageDO.getInquiryPackageItems(), InquiryPackageItem::getInquiryWayType))")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "pref", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "updater", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "deleted", ignore = true)
    void fillPackageInfo(TenantPackageDO tenantPackageDO, @MappingTarget TenantPackageRelationDO tenantPackageRelation);
    // tenantPackageRelation.setPackageId(tenantPackageDO.getId());
    // tenantPackageRelation.setStatus(TenantPackageRelationStatusEnum.NORMAL.getCode());
    // tenantPackageRelation.setPackageName(tenantPackageDO.getName());
    // // 套餐包信息
    // tenantPackageRelation.setBizType(tenantPackageDO.getBizType());
    // tenantPackageRelation.setHospitalPrefs(tenantPackageDO.getHospitalPrefs());
    // tenantPackageRelation.setInquiryPackageItems(tenantPackageDO.getInquiryPackageItems());
    // tenantPackageRelation.setInquiryWayTypes(CollectionUtils.convertList(tenantPackageDO.getInquiryPackageItems(), InquiryPackageItem::getInquiryWayType));
    // tenantPackageRelation.setPrice(tenantPackageDO.getPrice());
    // tenantPackageRelation.setPlatformReview(tenantPackageDO.getPlatformReview());

    @IterableMapping(qualifiedByName = "defaultVoMapping")
    List<TenantPackageRelationRespVO> convertDo2Vo(List<TenantPackageRelationDO> relationList);

    /**
     * 转换 VO 列表到 DTO 列表，处理 hospitalPref 兼容性
     */
    default List<TenantPackageRelationRespDto> convertVo2Dto(List<TenantPackageRelationRespVO> tenantPackageRelationList) {
        if (CollUtil.isEmpty(tenantPackageRelationList)) {
            return new ArrayList<>();
        }

        return tenantPackageRelationList.stream()
            .map(this::convertVo2DtoWithHospitalPrefCompat)
            .collect(Collectors.toList());
    }

    /**
     * 转换单个 VO 到 DTO，处理 hospitalPref 兼容性
     */
    // @Mapping(target = "inquiryPackageItems", expression = "java(setHospitalPrefsForApiDto(vo.getInquiryPackageItems(), vo.getHospitalPrefs()))")
    TenantPackageRelationRespDto convertVo2DtoWithHospitalPrefCompat(TenantPackageRelationRespVO vo);


    default void fillBaseInfo(TenantPackageRelationRespVO tp, TenantPackageDO tenantPackageDO, TenantDO tenantDO) {
        tp.setTerm(tenantPackageDO.getTerm());
        tp.setTermType(tenantPackageDO.getTermType());
        tp.setPackagePref(tenantPackageDO.getPref());
        tp.setTenantPref(tenantDO.getPref());
        tp.setTenantName(tenantDO.getName());
        tp.setProvince(tenantDO.getProvince());
        tp.setCity(tenantDO.getCity());
        tp.setArea(tenantDO.getArea());

        if (tp.getExt() != null && StringUtils.isNotBlank(tp.getExt().getGuid())) {
            tp.setPackageName("【老系统结转】" + tp.getPackageName());
        }

    }


    default List<TenantPackageRelationDO> convertUpdateStatus(List<TenantPackageRelationDO> tcbList, TenantPackageRelationStatusEnum statusEnum, TenantPackageRelationStatusChangeDto changeDto) {
        return tcbList.stream().map(t -> new TenantPackageRelationDO()
                .setId(t.getId()).setTenantId(t.getTenantId()).setStatus(statusEnum.getCode())
                .setStatusChangeInfo(changeDto.setOperateTime(LocalDateUtil.nowStr())))
            .collect(Collectors.toList());
    }

    @Mapping(target = "pref", expression = "java(com.xyy.saas.inquiry.util.PrefUtil.getTcbPref())")
    TenantPackageRelationDO tenantPackageRelationInitCopy(TenantPackageRelationDO tcb);

    TenantPackageRelationPageReqVO convertDto2Vo(TenantPackageRelationPageReqDto pageReqDto);

    @Mapping(target = "endTime", expression = "java(updateExcelVO.localEndTime())")
    @Mapping(target = "signTime", expression = "java(updateExcelVO.localSignTime())")
    @Mapping(target = "actualAmount", expression = "java(updateExcelVO.actualAmount())")
    @Mapping(target = "status", expression = "java(updateExcelVO.status())")
    TenantPackageRelationSaveReqVO convertImportVo(TenantPackageRelationUpdateExcelVO updateExcelVO);

    default TenantPackageRelationSaveReqVO convertImportVo(TenantPackageRelationUpdateExcelVO updateExcelVO, TenantPackageRelationDO oldRelation) {
        TenantPackageRelationSaveReqVO relationSaveReqVO = convertImportVo(updateExcelVO);
        TenantPackageRelationStatusChangeDto changeInfo = Optional.ofNullable(oldRelation.getStatusChangeInfo()).orElse(TenantPackageRelationStatusChangeDto.builder().build());
        changeInfo.setRefundType(updateExcelVO.refundType());
        changeInfo.setRemark(updateExcelVO.getReason());
        changeInfo.setRefundPrice(updateExcelVO.refundPrice());
        changeInfo.setOperateTime(LocalDateUtil.nowStr());
        relationSaveReqVO.setStatusChangeInfo(changeInfo);
        relationSaveReqVO.setId(oldRelation.getId());
        relationSaveReqVO.setInquiryPackageItems(oldRelation.getInquiryPackageItems());
        return relationSaveReqVO;
    }


    default TenantPackageRelationSaveReqVO convertImportVo(TenantPackageRelationOpenExcelVO excelVO, TenantDO tenantDO, TenantPackageDO packageDO) {
        TenantPackageRelationSaveReqVO saveReqVO = convertImportVo(excelVO);
        saveReqVO.setTenantId(tenantDO.getId());
        saveReqVO.setPackageId(packageDO.getId());
        saveReqVO.setPackagePref(packageDO.getPref());
        saveReqVO.setBizType(packageDO.getBizType());
        saveReqVO.setPackageName(packageDO.getName());
        return saveReqVO;
    }

    @Mapping(target = "startTime", expression = "java(excelVO.localStartTime())")
    @Mapping(target = "actualAmount", expression = "java(excelVO.actualAmount())")
    @Mapping(target = "signTime", expression = "java(excelVO.localSignTime())")
    TenantPackageRelationSaveReqVO convertImportVo(TenantPackageRelationOpenExcelVO excelVO);


    default TenantPackageRelationSaveReqVO convertImportVo(TenantAndPackageExcelVO excelVO, Long tenantId, TenantPackageDO packageDO) {
        TenantPackageRelationSaveReqVO saveReqVO = convertImportVo(excelVO);
        saveReqVO.setTenantId(tenantId);
        saveReqVO.setPackageId(packageDO.getId());
        saveReqVO.setPackagePref(packageDO.getPref());
        saveReqVO.setBizType(packageDO.getBizType());
        saveReqVO.setPackageName(packageDO.getName());
        return saveReqVO;
    }

    @Mapping(target = "startTime", expression = "java(excelVO.localStartTime())")
    @Mapping(target = "signTime", expression = "java(excelVO.localSignTime())")
    @Mapping(target = "status", expression = "java(excelVO.status())")
    @Mapping(target = "actualAmount", expression = "java(excelVO.actualAmount())")
    @Mapping(target = "pref", ignore = true)
    @Mapping(target = "bizType", ignore = true)
    TenantPackageRelationSaveReqVO convertImportVo(TenantAndPackageExcelVO excelVO);

    default TenantPackageRelationSaveReqVO convertMigrationTrailPackageDto(MigrationTrailPackageDto trailPackageDto) {
        return new TenantPackageRelationSaveReqVO()
            .setTenantId(trailPackageDto.getTenantId())
            .setBizType(BizTypeEnum.HYWZ.getCode())
            .setPackageId(TenantPackageConstant.RECHARGE_PACKAGE_ID)
            .setPackageName("迁移" + PackageNatureEnum.TRIAL.getDesc() + "套餐")
            .setPrice(BigDecimal.ZERO)
            .setHospitalPrefs(Collections.singletonList(trailPackageDto.getHospitalPref()))
            .setInquiryBizType(InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode())
            .setInquiryAuditType(InquiryAuditTypeEnum.DRUGSTORE.getCode())
            .setInquiryPackageItems(InquiryPackageItem.convertToNewFormat(InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode(),
                Collections.singletonList(new InquiryPackageItem().setInquiryWayType(InquiryWayTypeEnum.TEXT.getCode()).setCount(TenantPackageConstant.UN_LIMITED_COST).setUnlimited(true)),
                Collections.singletonList(trailPackageDto.getHospitalPref())))
            .setStartTime(LocalDate.now().atStartOfDay())
            .setEndTime(LocalDate.now().plusDays(trailPackageDto.getPackageTrialDay() - 1).atTime(23, 59, 59))
            .setSignTime(LocalDateTime.now())
            .setPackageNature(PackageNatureEnum.TRIAL.getCode()) // 体验
            .setPaymentType(PackagePaymentTypeEnum.OFFLINE.getCode()) // 线下
            .setSignChannel(4) // 荷叶问诊
            .setSignSource(0) // 运营线下提单
            ;
    }

    default List<TenantPackageRelationSaveReqVO> convertMigrationPackages(List<MigrationPackageRespDto> saveDtos) {
        return saveDtos.stream().filter(s -> CollUtil.isNotEmpty(s.getCountLimitRule()))
            .map(s -> {
                TenantPackageRelationSaveReqVO saveReqVO = new TenantPackageRelationSaveReqVO();
                saveReqVO.setTenantId(s.getTenantId());
                saveReqVO.setBizType(BizTypeEnum.HYWZ.getCode());
                saveReqVO.setPackageId(TenantPackageConstant.RECHARGE_PACKAGE_ID);
                saveReqVO.setPackageName(s.getPackageName());
                saveReqVO.setHospitalPrefs(Collections.singletonList(s.getHospitalPref()));
                saveReqVO.setInquiryBizType(InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode());
                saveReqVO.setInquiryAuditType(InquiryAuditTypeEnum.DRUGSTORE.getCode()); // 迁移成门店审方套餐
                saveReqVO.setPrice(Optional.ofNullable(Optional.ofNullable(s.getPackagePrice()).orElse(s.getPackageActualAmount())).orElse(BigDecimal.ZERO));

                List<InquiryPackageItem> packageItems = s.getCountLimitRule().stream().map(c -> {
                    InquiryPackageItem packageItem;
                    if (MapUtil.isEmpty(c.getOverallPlanRule()) || CollUtil.isEmpty(c.getOverallPlanRule().values())) {
                        packageItem = new InquiryPackageItem()
                            .setInquiryWayType(Objects.equals(c.getCountLimitType(), 1) ? InquiryWayTypeEnum.VIDEO.getCode() : InquiryWayTypeEnum.TEXT.getCode())
                            .setCount(Objects.equals(c.getIsInfinite(), 0) ? TenantPackageConstant.UN_LIMITED_COST : Optional.ofNullable(c.getSurplusLimit()).orElse(0))
                            .setUnlimited(Objects.equals(c.getIsInfinite(), 0));
                    } else {
                        // 检查是否存在任意一个规则项是"不限"
                        boolean hasUnlimited = c.getOverallPlanRule().values().stream().anyMatch(rule -> Objects.equals(rule.getIsInfinite(), 0));
                        if (hasUnlimited) {
                            packageItem = new InquiryPackageItem()
                                .setInquiryWayType(Objects.equals(c.getCountLimitType(), 1) ? InquiryWayTypeEnum.VIDEO.getCode() : InquiryWayTypeEnum.TEXT.getCode())
                                .setCount(TenantPackageConstant.UN_LIMITED_COST)
                                .setUnlimited(true);
                        } else {
                            // 没有不限项，累加所有surplusLimit
                            Long count = c.getOverallPlanRule().values().stream()
                                .mapToLong(rule -> Optional.ofNullable(rule.getSurplusLimit()).orElse(0))
                                .sum();
                            packageItem = new InquiryPackageItem()
                                .setInquiryWayType(Objects.equals(c.getCountLimitType(), 1) ? InquiryWayTypeEnum.VIDEO.getCode() : InquiryWayTypeEnum.TEXT.getCode())
                                .setCount(count)
                                .setUnlimited(false);
                        }
                    }
                    return packageItem;
                }).toList();

                // 转换为新格式以确保兼容性
                List<InquiryPackageItem> compatibleItems = InquiryPackageItem.convertToNewFormat(InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode(), packageItems, saveReqVO.getHospitalPrefs());
                saveReqVO.setInquiryPackageItems(compatibleItems);

                saveReqVO.setStatus(TenantPackageRelationStatusEnum.convertStatus(s.getValidStatus()));
                saveReqVO.setStartTime(Optional.ofNullable(s.getServerStart()).orElse(new Date()).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                saveReqVO.setEndTime(Optional.ofNullable(s.getServerEnd()).orElse(new Date()).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                saveReqVO.setPackageNature(PackageNatureEnum.convertPackageType(s.getPackageType()));
                saveReqVO.setPaymentType(Objects.equals(s.getReceivablesType(), (byte) 1) ? PackagePaymentTypeEnum.OFFLINE.getCode() : PackagePaymentTypeEnum.ONLINE.getCode());
                saveReqVO.setSignTime(Optional.ofNullable(s.getSignTime()).orElse(new Date()).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                saveReqVO.setSignUser(s.getSignUser());
                saveReqVO.setSignUserNo(s.getSignUserno());
                saveReqVO.setProxyUser(s.getInviteCode());
                saveReqVO.setSignChannel(s.getSignChannel() == null ? 4 : s.getSignChannel().intValue());
                saveReqVO.setSignSource(s.getSignSource() == null || Objects.equals(s.getSignSource(), 6) ? 0 : s.getSignSource()); // 6 - 转换成运营线下提单
                saveReqVO.setActualAmount(s.getPackageActualAmount());
                saveReqVO.setCollectAccount(NumberUtil.parseInt(s.getPackageCollectAccount(), null));
                saveReqVO.setPayNo(s.getPackagePayorderNo());
                saveReqVO.setRemark(s.getRemark());
                saveReqVO.setCreator(s.getCreateUser());//提单人

                // 0未生效,1生效中 的创建时间以now为准，否则平迁旧服务
                if (Objects.equals(s.getValidStatus().intValue(), 0) || Objects.equals(s.getValidStatus().intValue(), 1)) {
                    saveReqVO.setCreateTime(LocalDateTime.now());
                } else {
                    saveReqVO.setCreateTime(s.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                }

                saveReqVO.setExt(TenantPackageRelationExtDto.builder()
                    .guid(s.getGuid())
                    .countLimitRule(JsonUtils.toJsonString(s.getCountLimitRule()))
                    .build());

                if (Objects.equals(s.getValidStatus().intValue(), 4)) {
                    // 退款 的创建时间取原 退款时间
                    saveReqVO.setStatusChangeInfo(TenantPackageRelationStatusChangeDto.builder()
                        .operateTime(s.getRefundTime() == null ? null : DateUtil.format(s.getRefundTime(), "yyyy-MM-dd HH:mm:ss"))
                        .remark(s.getRefundReason())
                        .refundType(NumberUtil.parseInt(s.getRefundType(), 0))
                        .refundPrice(s.getRefundAmount()).build());
                }

                if (Objects.equals(s.getValidStatus().intValue(), 5)) {
                    // 作废 的创建时间取原 作废时间
                    saveReqVO.setStatusChangeInfo(TenantPackageRelationStatusChangeDto.builder()
                        .operateTime(s.getObsoleteTime() == null ? null : DateUtil.format(s.getObsoleteTime(), "yyyy-MM-dd HH:mm:ss"))
                        .remark(s.getObsoleteReason()).build());
                }

                return saveReqVO;
            }).collect(Collectors.toList());

    }

}
