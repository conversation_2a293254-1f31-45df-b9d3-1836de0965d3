package cn.iocoder.yudao.module.system.dal.dataobject.archive;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 归档表配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "archive_table", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ArchiveTableDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 数据源标识
     */
    private String datasourceKey;

    /**
     * 最大保留天数
     */
    private Integer maxDay;

    /**
     * 每批次处理数量
     */
    private Integer batchSize;

    /**
     * 每日最大处理数量
     */
    private Integer dailyMaxCount;

    /**
     * 状态，参见 CommonStatusEnum 枚举类
     */
    private Integer status;

    /**
     * 执行状态：0-待执行，1-执行中，2-执行完成，3-执行失败
     */
    private Integer progressStatus;

    /**
     * 最后执行时间
     */
    private LocalDateTime lastExecuteTime;

    /**
     * 最后处理的最小ID
     */
    private Long lastMinId;

    /**
     * 当日已处理数量
     */
    private Integer dailyProcessedCount;

    /**
     * 错误信息
     */
    private String errorMessage;
}
