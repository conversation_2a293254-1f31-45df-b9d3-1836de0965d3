package cn.iocoder.yudao.module.system.dal.redis.common;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.AUTH_LOGIN_USER_LOCKED;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.util.MathUtil;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.LocalTime;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

@Repository
public class UserLockRedisDAO {

    @Resource
    private ConfigApi configApi;


    /**
     * 记录用户锁次数
     *
     * @param lockKey   锁定key
     * @param lockValue 锁定值
     */
    public Integer lockRecord(String lockKey, String lockValue) {
        String key = lockKey + lockValue;
        //锁定多少秒，默认30分钟  =  30 * 60 = 1800 秒
        int timeOut = MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(lockKey.concat("_time")), 1800);
        Integer count = (Integer) RedisUtils.get(key);
        if (ObjectUtil.isEmpty(count)) {
            RedisUtils.set(key, 1, timeOut);
            return 1;
        }
        //次数加1 , 并重新设置当前key的过期时间
        RedisUtils.set(key, count + 1, timeOut);
        return count + 1;
    }

    /**
     * 校验当前锁次数
     *
     * @param lockKey
     * @param lockValue
     * @param errorCode
     */
    public void isLocked(String lockKey, String lockValue, ErrorCode errorCode) {
        String key = lockKey + lockValue;
        Integer count = (Integer) RedisUtils.get(key);
        // 当前key不存在直接返回
        if (ObjectUtil.isEmpty(count)) {
            return;
        }
        int failCount = MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(lockKey), 5);
        if (count >= failCount) {
            // 获取key 剩余时长，单位秒
            long time = RedisUtils.getExpire(key);
            throw exception(errorCode, Math.ceilDiv(time, 60));
        }
    }


    /**
     * 记录失败用户锁定
     *
     * @param username
     */
    public Integer lockRecord(String username) {

        return lockRecord(RedisKeyConstants.LOGIN_FAIL_COUNT, username);
        // String key = RedisKeyConstants.LOGIN_FAIL_COUNT + username;
        // //锁定多少秒，默认30分钟  =  30 * 60 = 1800 秒
        // int timeOut = MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(SystemConstant.LOGIN_FAIL_LOCK_TIME), 1800);
        // Integer count = (Integer) RedisUtils.get(key);
        // if (ObjectUtil.isEmpty(count)) {
        //     RedisUtils.set(key, 1, timeOut);
        //     return;
        // }
        // //次数加1 , 并重新设置当前key的过期时间
        // RedisUtils.set(key, count + 1, timeOut);
    }

    /**
     * 校验当前登录用户锁定
     *
     * @param username
     */
    public void isLocked(String username) {
        isLocked(RedisKeyConstants.LOGIN_FAIL_COUNT, username, AUTH_LOGIN_USER_LOCKED);
        // String key = RedisKeyConstants.LOGIN_FAIL_COUNT + username;
        // Integer count = (Integer) RedisUtils.get(key);
        // // 当前key不存在直接返回
        // if (ObjectUtil.isEmpty(count)) {
        //     return;
        // }
        // int failCount = MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(SystemConstant.LOGIN_FAIL_LOCK_COUNT), 5);
        // if (count >= failCount) {
        //     // 获取key 剩余时长，单位秒
        //     long time = RedisUtils.getExpire(key);
        //     throw exception(AUTH_LOGIN_USER_LOCKED, Math.ceilDiv(time, 60));
        // }
    }

    /**
     * 校验api授权登陆nonce是否重复
     *
     * @param nonce
     * @param expireSeconds
     */
    public boolean setNonceExpire(String nonce, long expireSeconds) {
        if (StringUtils.isBlank(nonce) || expireSeconds <= 0) {
            return false;
        }
        String key = RedisKeyConstants.LOGIN_API_NONCE + nonce;
        Object exist = RedisUtils.get(key);
        // 当前key不存在直接返回
        if (exist != null) {
            return false;
        }
        RedisUtils.set(key, 1, expireSeconds);
        return true;
    }

    /**
     * 释放锁
     *
     * @param lockKey
     * @param lockValue
     * @return
     */
    public void releaseLock(String lockKey, String lockValue) {
        RedisUtils.del(lockKey + lockValue);
    }

    /**
     * 记录用户操作次数，用于限制用户在一天内的操作次数 过期时间设置为当天 23:59:59
     *
     * @param keyPrefix key前缀
     * @param userId    用户ID
     */
    public void incrementUserOperationCount(String keyPrefix, Long userId) {
        String key = keyPrefix + userId;
        // 获取当前key的值，如果不存在则初始化为0
        Integer count = (Integer) RedisUtils.get(key);
        if (ObjectUtil.isEmpty(count)) {
            // 第一次操作，设置初始值为1，过期时间为当天剩余时间
            RedisUtils.set(key, 1, getSecondsUntilEndOfDay());
        } else {
            // 次数加1，并重新设置过期时间
            RedisUtils.set(key, count + 1, getSecondsUntilEndOfDay());
        }
    }

    /**
     * 检查用户在一天内的操作次数是否超过限制，如果超过则锁定用户30分钟 ，锁30分钟后，如果再设置
     *
     * @param keyPrefix key前缀
     * @param userId    用户ID
     * @param maxCount  一天内允许的最大操作次数
     * @param errorCode 错误码
     */
    public void checkAndLockUserIfExceeded(String keyPrefix, Long userId, int maxCount, ErrorCode errorCode) {
        String dailyKey = keyPrefix + userId;
        String lockKey = keyPrefix + ":locked:" + userId;

        // 先检查用户是否已被锁定
        Object locked = RedisUtils.get(lockKey);
        if (!ObjectUtil.isEmpty(locked)) {
            // 用户已被锁定，获取剩余锁定时间
            long time = RedisUtils.getExpire(lockKey);
            throw exception(errorCode, maxCount, Math.ceilDiv(time, 60));
        }

        // 检查用户当天操作次数
        Integer count = (Integer) RedisUtils.get(dailyKey);
        if (!ObjectUtil.isEmpty(count) && count >= maxCount) {
            // 操作次数超过限制，锁定用户30分钟 后 只放一次
            RedisUtils.set(dailyKey, maxCount - 1, getSecondsUntilEndOfDay());
            RedisUtils.set(lockKey, true, 30 * 60L); // 锁定30分钟
            throw exception(errorCode, maxCount, 30L); // 30分钟后解锁
        }
    }

    /**
     * 计算当前时间到今天 23:59:59 的秒数
     *
     * @return 秒数
     */
    private long getSecondsUntilEndOfDay() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endOfDay = LocalDateTime.of(now.toLocalDate(), LocalTime.MAX);
        return Duration.between(now, endOfDay).getSeconds();
    }

}