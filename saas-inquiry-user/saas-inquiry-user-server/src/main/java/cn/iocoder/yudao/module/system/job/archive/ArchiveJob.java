package cn.iocoder.yudao.module.system.job.archive;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.module.system.service.archive.ArchiveTableService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 大表归档定时任务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ArchiveJob implements JobHandler {

    @Resource
    private ArchiveTableService archiveTableService;

    @Override
    public String execute(String param) {
        log.info("大表归档定时任务开始执行");

        try {
            // 执行所有启用的归档任务
            archiveTableService.executeAllArchiveTasks();
            
            log.info("大表归档定时任务执行完成");
            return "SUCCESS";
            
        } catch (Exception e) {
            log.error("大表归档定时任务执行失败", e);
            return "FAILED: " + e.getMessage();
        }
    }
}
