### 1.门店登录 - 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "15172440267",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}


### 请求 /system/operate-log/page 接口 => 成功
GET {{baseAdminSystemUrl}}/system/operate-log/page
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}



### 请求 /system/operate-log/page 接口 => 成功
GET {{baseAdminSystemUrl}}/system/login-log/kick-off-logs?userId =1928093937485590529
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}
X-Developer: {{developer}}

