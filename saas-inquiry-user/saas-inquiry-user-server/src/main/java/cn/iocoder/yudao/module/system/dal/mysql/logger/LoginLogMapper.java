package cn.iocoder.yudao.module.system.dal.mysql.logger;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.logger.vo.loginlog.LoginLogPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.logger.LoginLogDO;
import cn.iocoder.yudao.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.yudao.module.system.enums.logger.LoginResultEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface LoginLogMapper extends BaseMapperX<LoginLogDO> {

    default PageResult<LoginLogDO> selectPage(LoginLogPageReqVO reqVO) {
        LambdaQueryWrapperX<LoginLogDO> query = new LambdaQueryWrapperX<LoginLogDO>()
            .likeIfPresent(LoginLogDO::getUserIp, reqVO.getUserIp())
            .likeIfPresent(LoginLogDO::getUsername, reqVO.getUsername())
            .betweenIfPresent(LoginLogDO::getCreateTime, reqVO.getCreateTime());
        if (Boolean.TRUE.equals(reqVO.getStatus())) {
            query.eq(LoginLogDO::getResult, LoginResultEnum.SUCCESS.getResult());
        } else if (Boolean.FALSE.equals(reqVO.getStatus())) {
            query.gt(LoginLogDO::getResult, LoginResultEnum.SUCCESS.getResult());
        }
        query.orderByDesc(LoginLogDO::getId); // 降序
        return selectPage(reqVO, query);
    }

    default LoginLogDO selectKickOffLogsByUserId(Long userId) {
       List<LoginLogDO> loginLogDOS = selectList(new LambdaQueryWrapperX<LoginLogDO>()
            .eq(LoginLogDO::getUserId, userId)
            .eq(LoginLogDO::getLogType, LoginLogTypeEnum.LOGOUT_KICK.getType())
            .eq(LoginLogDO::getResult, LoginResultEnum.SUCCESS.getResult())
            .orderByDesc(LoginLogDO::getCreateTime)
            .last("LIMIT 1"));
       return CollectionUtil.isEmpty(loginLogDOS) ? null : loginLogDOS.getFirst();
    }

}
