package cn.iocoder.yudao.module.system.enums.archive;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 归档执行状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ArchiveProgressStatusEnum {

    /**
     * 待执行
     */
    PENDING(0, "待执行"),

    /**
     * 执行中
     */
    RUNNING(1, "执行中"),

    /**
     * 执行完成
     */
    COMPLETED(2, "执行完成"),

    /**
     * 执行失败
     */
    FAILED(3, "执行失败");

    /**
     * 状态值
     */
    private final Integer status;

    /**
     * 状态名
     */
    private final String name;
}
