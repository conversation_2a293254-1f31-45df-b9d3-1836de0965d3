package cn.iocoder.yudao.module.system.service.archive;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.archive.vo.ArchiveTablePageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.archive.ArchiveTableDO;
import cn.iocoder.yudao.module.system.dal.mysql.archive.ArchiveTableMapper;
import cn.iocoder.yudao.module.system.enums.archive.ArchiveProgressStatusEnum;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 归档表配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class ArchiveTableServiceImpl implements ArchiveTableService {

    @Resource
    private ArchiveTableMapper archiveTableMapper;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    @Transactional
    public Long createArchiveTable(ArchiveTableDO archiveTable) {
        // 检查表名是否已存在
        ArchiveTableDO existing = archiveTableMapper.selectByTableName(archiveTable.getTableName());
        if (existing != null) {
            throw new IllegalArgumentException("表名已存在归档配置: " + archiveTable.getTableName());
        }

        // 设置默认值
        if (archiveTable.getBatchSize() == null) {
            archiveTable.setBatchSize(1000);
        }
        if (archiveTable.getDailyMaxCount() == null) {
            archiveTable.setDailyMaxCount(1000000); // 默认每日最大处理100万条
        }
        if (archiveTable.getStatus() == null) {
            archiveTable.setStatus(CommonStatusEnum.ENABLE.getStatus());
        }
        if (archiveTable.getProgressStatus() == null) {
            archiveTable.setProgressStatus(ArchiveProgressStatusEnum.PENDING.getStatus());
        }
        if (archiveTable.getDailyProcessedCount() == null) {
            archiveTable.setDailyProcessedCount(0);
        }

        archiveTableMapper.insert(archiveTable);
        return archiveTable.getId();
    }

    @Override
    @Transactional
    public void updateArchiveTable(ArchiveTableDO archiveTable) {
        // 检查配置是否存在
        ArchiveTableDO existing = archiveTableMapper.selectById(archiveTable.getId());
        if (existing == null) {
            throw new IllegalArgumentException("归档配置不存在: " + archiveTable.getId());
        }

        archiveTableMapper.updateById(archiveTable);
    }

    @Override
    @Transactional
    public void deleteArchiveTable(Long id) {
        // 检查是否有正在执行的任务
        ArchiveTableDO config = archiveTableMapper.selectById(id);
        if (config != null && ArchiveProgressStatusEnum.RUNNING.getStatus().equals(config.getProgressStatus())) {
            throw new IllegalStateException("无法删除正在执行的归档配置");
        }

        archiveTableMapper.deleteById(id);
    }

    @Override
    public ArchiveTableDO getArchiveTable(Long id) {
        return archiveTableMapper.selectById(id);
    }

    @Override
    public PageResult<ArchiveTableDO> getArchiveTablePage(ArchiveTablePageReqVO reqVO) {
        return archiveTableMapper.selectPage(reqVO);
    }

    @Override
    public List<ArchiveTableDO> getEnabledArchiveTableList() {
        return archiveTableMapper.selectEnabledConfigs();
    }

    @Override
    public void executeAllArchiveTasks() {
        List<ArchiveTableDO> configs = getEnabledArchiveTableList();
        log.info("开始执行归档任务，共{}个配置", configs.size());

        for (ArchiveTableDO config : configs) {
            try {
                executeArchiveForTable(config);
            } catch (Exception e) {
                log.error("表{}归档失败", config.getTableName(), e);
                updateArchiveStatus(config.getId(), ArchiveProgressStatusEnum.FAILED.getStatus(), e.getMessage());
            }
        }

        log.info("归档任务执行完成");
    }

    @Override
    public void executeArchiveForTable(ArchiveTableDO config) {
        log.info("开始执行表{}的归档任务", config.getTableName());

        // 更新状态为执行中
        updateArchiveStatus(config.getId(), ArchiveProgressStatusEnum.RUNNING.getStatus(), null);

        try {
            // 执行归档逻辑
            executeArchiveLogic(config);

            // 更新状态为完成
            updateArchiveStatus(config.getId(), ArchiveProgressStatusEnum.COMPLETED.getStatus(), null);
            log.info("表{}归档任务执行完成", config.getTableName());

        } catch (Exception e) {
            log.error("表{}归档任务执行失败", config.getTableName(), e);
            updateArchiveStatus(config.getId(), ArchiveProgressStatusEnum.FAILED.getStatus(), e.getMessage());
            throw e;
        }
    }

    /**
     * 执行归档核心逻辑
     */
    private void executeArchiveLogic(ArchiveTableDO config) {
        // 切换到指定数据源
        DynamicDataSourceContextHolder.push(config.getDatasourceKey());

        try {
            // 检查是否需要重置当日计数器
            resetDailyCounterIfNeeded(config);

            Long currentMinId = config.getLastMinId();
            if (currentMinId == null) {
                currentMinId = getTableMinId(config.getTableName());
                if (currentMinId == null) {
                    log.info("表{}为空表，跳过归档", config.getTableName());
                    return;
                }
            }

            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(config.getMaxDay());
            log.info("表{}开始归档，截止时间: {}, 起始ID: {}",
                config.getTableName(), cutoffTime, currentMinId);

            int totalDeleted = 0;
            int dailyProcessedCount = config.getDailyProcessedCount() != null ? config.getDailyProcessedCount() : 0;

            while (true) {
                // 检查是否超过每日最大处理数量
                if (dailyProcessedCount >= config.getDailyMaxCount()) {
                    log.info("表{}今日已达到最大处理数量{}，停止归档", config.getTableName(), config.getDailyMaxCount());
                    break;
                }

                Long endId = currentMinId + config.getBatchSize();

                // 执行删除
                int deletedCount = deleteByIdRangeWithTimeCheck(
                    config.getTableName(),
                    currentMinId,
                    endId,
                    cutoffTime,
                    config.getBatchSize()
                );

                totalDeleted += deletedCount;
                dailyProcessedCount += deletedCount;

                if (deletedCount == 0) {
                    log.info("表{}归档完成，共删除{}条记录", config.getTableName(), totalDeleted);
                    break;
                }

                // 更新进度和当日处理数量
                currentMinId = endId;
                updateArchiveProgressWithDailyCount(config.getId(), currentMinId, dailyProcessedCount);

                log.debug("表{}归档进度: 当前ID={}, 本批删除={}条, 今日已处理={}条",
                    config.getTableName(), currentMinId, deletedCount, dailyProcessedCount);

                // 短暂休眠，避免对数据库造成压力
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("归档任务被中断", e);
                }
            }

        } finally {
            DynamicDataSourceContextHolder.poll();
        }
    }

    /**
     * 获取表的最小ID
     */
    private Long getTableMinId(String tableName) {
        try {
            String sql = String.format("SELECT MIN(id) FROM %s", tableName);
            return jdbcTemplate.queryForObject(sql, Long.class);
        } catch (Exception e) {
            log.error("获取表{}最小ID失败", tableName, e);
            throw new RuntimeException("获取表最小ID失败: " + e.getMessage(), e);
        }
    }

    /**
     * 按ID范围和时间条件删除数据
     */
    private int deleteByIdRangeWithTimeCheck(String tableName, Long startId, Long endId,
        LocalDateTime cutoffTime, Integer batchSize) {
        try {
            String sql = String.format(
                "DELETE FROM %s WHERE id > ? AND id <= ? AND create_time < ? LIMIT ?",
                tableName
            );

            return jdbcTemplate.update(sql, startId, endId, cutoffTime, batchSize);
        } catch (Exception e) {
            log.error("删除表{}数据失败, startId={}, endId={}", tableName, startId, endId, e);
            throw new RuntimeException("删除数据失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void updateArchiveStatus(Long configId, Integer progressStatus, String errorMessage) {
        ArchiveTableDO update = ArchiveTableDO.builder()
            .id(configId)
            .progressStatus(progressStatus)
            .lastExecuteTime(LocalDateTime.now())
            .errorMessage(errorMessage)
            .build();

        archiveTableMapper.updateById(update);
    }

    @Override
    @Transactional
    public void updateArchiveProgress(Long configId, Long lastMinId) {
        ArchiveTableDO update = ArchiveTableDO.builder()
            .id(configId)
            .lastMinId(lastMinId)
            .lastExecuteTime(LocalDateTime.now())
            .build();

        archiveTableMapper.updateById(update);
    }

    /**
     * 更新归档进度和当日处理数量
     *
     * @param configId            配置ID
     * @param lastMinId           最后处理的最小ID
     * @param dailyProcessedCount 当日已处理数量
     */
    @Transactional
    private void updateArchiveProgressWithDailyCount(Long configId, Long lastMinId, Integer dailyProcessedCount) {
        ArchiveTableDO update = ArchiveTableDO.builder()
            .id(configId)
            .lastMinId(lastMinId)
            .lastExecuteTime(LocalDateTime.now())
            .dailyProcessedCount(dailyProcessedCount)
            .build();

        archiveTableMapper.updateById(update);
    }

    /**
     * 检查是否需要重置当日计数器 如果最后执行时间不是今天，则重置当日处理数量为0
     *
     * @param config 归档配置
     */
    @Transactional
    private void resetDailyCounterIfNeeded(ArchiveTableDO config) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastExecuteTime = config.getLastExecuteTime();

        // 如果最后执行时间为空或者不是今天，重置当日计数器
        if (lastExecuteTime == null || !lastExecuteTime.toLocalDate().equals(now.toLocalDate())) {
            ArchiveTableDO update = ArchiveTableDO.builder()
                .id(config.getId())
                .dailyProcessedCount(0)
                .build();

            archiveTableMapper.updateById(update);

            // 更新内存中的配置对象
            config.setDailyProcessedCount(0);
            log.info("表{}重置当日处理计数器", config.getTableName());
        }
    }


}
