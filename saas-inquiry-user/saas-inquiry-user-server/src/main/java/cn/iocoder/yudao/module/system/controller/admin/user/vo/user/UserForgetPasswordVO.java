package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import com.xyy.saas.inquiry.constant.SystemConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

@Schema(description = "管理后台 - 门店转让 VO")
@Data
@Accessors(chain = true)
public class UserForgetPasswordVO {

    @Schema(description = "手机号码", example = "15601691300")
    @Length(min = 11, max = 11, message = "手机号长度必须 11 位")
    @NotEmpty(message = "手机号码不能为空")
    private String mobile;

    @Schema(description = "短信验证码", requiredMode = Schema.RequiredMode.REQUIRED, example = "默认验证码:1111")
    @NotEmpty(message = "验证码不能为空")
    private String code;

    @Schema(description = "密码", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    @Pattern(regexp = SystemConstant.PASSWORD_PATTERN_REGEXP, message = SystemConstant.PASSWORD_PATTERN_REGEXP_MESSAGE)
    private String password;

}
