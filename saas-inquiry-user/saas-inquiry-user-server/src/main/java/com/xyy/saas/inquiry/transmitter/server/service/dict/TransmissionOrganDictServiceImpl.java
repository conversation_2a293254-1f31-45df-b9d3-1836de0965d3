package com.xyy.saas.inquiry.transmitter.server.service.dict;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static com.xyy.saas.inquiry.transmitter.enums.ErrorCodeConstants.TRANSMISSION_PROVIDER_DICT_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.dict.DictTypeApi;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.yudao.module.system.api.dict.dto.DictTypeRespDTO;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.enums.dict.DictMatchEnum;
import com.xyy.saas.inquiry.generic.api.InquiryGenericApiService;
import com.xyy.saas.inquiry.generic.api.dto.diagnosis.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import com.xyy.saas.inquiry.transmitter.enums.DictTypeConstants;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionDictImportReqVo;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictExcelVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictRespVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictSaveReqVO;
import com.xyy.saas.inquiry.transmitter.server.controller.admin.organ.vo.TransmissionOrganPageReqVO;
import com.xyy.saas.inquiry.transmitter.server.convert.dict.TransmitterDictConvert;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.dict.TransmissionOrganDictDO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.dict.TransmissionOrganDictMatchDO;
import com.xyy.saas.inquiry.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import com.xyy.saas.inquiry.transmitter.server.dal.mysql.dict.TransmissionOrganDictMapper;
import com.xyy.saas.inquiry.transmitter.server.dal.mysql.dict.TransmissionOrganDictMatchMapper;
import com.xyy.saas.inquiry.transmitter.server.dal.mysql.organ.TransmissionOrganMapper;
import com.xyy.saas.inquiry.util.excel.EasyExcelUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 服务商字典 Service 实现类 transmission_organ_dict
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransmissionOrganDictServiceImpl implements TransmissionOrganDictService {

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private DictTypeApi dictTypeApi;

    @Resource
    private TransmissionOrganDictMapper transmissionOrganDictMapper;

    @Resource
    private TransmissionOrganMapper organMapper;

    @Resource
    private InquiryGenericApiService inquiryGenericApiService;

    @Resource
    private EasyExcelUtil easyExcelUtil;

    @Override
    public Long createTransmissionProviderDict(TransmissionOrganDictSaveReqVO createReqVO) {
        DictTypeRespDTO dictType = dictTypeApi.getDictType(createReqVO.getDictType());
        if (dictType == null) {
            throw exception(TRANSMISSION_PROVIDER_DICT_NOT_EXISTS);
        }
        TransmissionOrganDictDO transmissionProviderDict = BeanUtils.toBean(createReqVO, TransmissionOrganDictDO.class);
        transmissionProviderDict.setDictName(dictType.getName());
        transmissionOrganDictMapper.insert(transmissionProviderDict);
        return transmissionProviderDict.getId();
    }

    @Override
    public void updateTransmissionProviderDict(TransmissionOrganDictSaveReqVO updateReqVO) {
        // 校验存在
        validateTransmissionProviderDictExists(updateReqVO.getId());
        DictTypeRespDTO dictType = dictTypeApi.getDictType(updateReqVO.getDictType());
        if (dictType == null) {
            throw exception(TRANSMISSION_PROVIDER_DICT_NOT_EXISTS);
        }
        TransmissionOrganDictDO updateObj = BeanUtils.toBean(updateReqVO, TransmissionOrganDictDO.class);
        updateObj.setDictName(dictType.getName());
        transmissionOrganDictMapper.updateById(updateObj);
    }

    @Override
    public void deleteTransmissionProviderDict(Long id) {
        // 校验存在
        validateTransmissionProviderDictExists(id);
        // 删除
        transmissionOrganDictMapper.deleteById(id);
    }

    private void validateTransmissionProviderDictExists(Long id) {
        if (transmissionOrganDictMapper.selectById(id) == null) {
            throw exception(TRANSMISSION_PROVIDER_DICT_NOT_EXISTS);
        }
    }

    @Override
    public TransmissionOrganDictDO getTransmissionProviderDict(Long id) {
        return transmissionOrganDictMapper.selectById(id);
    }

    @Override
    public List<TransmissionOrganDictDO> getTransmissionDict(TransmissionOrganDictPageReqVO reqVO) {
        return transmissionOrganDictMapper.selectByCondition(reqVO);
    }

    @Override
    public PageResult<TransmissionOrganDictRespVO> getTransmissionProviderDictPage(TransmissionOrganDictPageReqVO pageReqVO) {
        PageResult<TransmissionOrganDictDO> pageResult = transmissionOrganDictMapper.selectPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>(null, pageResult.getTotal());
        }
        //组装名称
        Set<Integer> organIds = CollectionUtils.convertSet(pageResult.getList(), TransmissionOrganDictDO::getOrganId);
        Map<Integer, TransmissionOrganDO> organDOMap = organMapper.selectList(TransmissionOrganPageReqVO.builder().ids(new ArrayList<>(organIds)).build())
            .stream().collect(Collectors.toMap(TransmissionOrganDO::getId, Function.identity(), (a, b) -> b));
        List<TransmissionOrganDictRespVO> list = pageResult.getList().stream().map(d -> {
            TransmissionOrganDictRespVO vo = TransmitterDictConvert.INSTANCE.convertVo(d);
            vo.setOrganName(organDOMap.getOrDefault(d.getOrganId(), new TransmissionOrganDO()).getName());
            return vo;
        }).toList();

        return new PageResult<>(list, pageResult.getTotal());
    }


    @Override
    public ImportResultDto importDictList(TransmissionDictImportReqVo reqVO) {
        reqVO.setExcelName("三方数据字典导入").setLimitCount(50000);

        return easyExcelUtil.importData(reqVO, TransmissionOrganDictExcelVO.class, this::importDictList);
    }

    private void importDictList(TransmissionDictImportReqVo importReqVO, List<TransmissionOrganDictExcelVO> list) {
        if (CollUtil.isEmpty(list)) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "导入数据不能为空");
        }
        Integer organId = importReqVO.getOrganId();
        String dictValue = importReqVO.getDictValue();
        // 校验服务商
        TransmissionOrganDO organDO = organMapper.selectById(organId);
        if (organDO == null) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "导入服务商不存在");
        }
        if (list.stream().map(TransmissionOrganDictExcelVO::getOrganName).distinct().count() > 1) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "导入的服务商不一致");
        }
        if (!StringUtils.equals(organDO.getName(), list.getFirst().getOrganName())) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "导入的服务商不一致");
        }

        // 校验字典类型
        DictDataRespDTO dictData = dictDataApi.getDictData(null, DictTypeConstants.TRANSMISSION_ORGAN_DICT, dictValue);
        if (dictData == null) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "导入数据字典不存在");
        }
        if (list.stream().map(TransmissionOrganDictExcelVO::getDictTypeName).distinct().count() > 1) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "导入的字典类型不一致");
        }
        if (!StringUtils.equals(dictData.getLabel(), list.getFirst().getDictTypeName())) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "导入的字典类型不一致");
        }

        // 校验-转换saveVO
        List<TransmissionOrganDictSaveReqVO> dictSaveReqVOS = list.stream().map(excelVO -> TransmitterDictConvert.INSTANCE.convertExcelVO(excelVO, organId, dictData)).toList();

        // 去重
        List<TransmissionOrganDictSaveReqVO> saveReqVOS = dictSaveReqVOS.stream().distinct().toList();
        TransmissionOrganDictPageReqVO reqVO = TransmissionOrganDictPageReqVO.builder().organId(organId).dictType(dictValue).build();

        for (List<TransmissionOrganDictSaveReqVO> reqVOS : Lists.partition(saveReqVOS, 500)) {
            reqVO.setValues(CollectionUtils.convertList(reqVOS, TransmissionOrganDictSaveReqVO::getValue));
            Map<String, TransmissionOrganDictDO> existsDictMap = transmissionOrganDictMapper.selectByCondition(reqVO).stream()
                .collect(Collectors.toMap(TransmissionOrganDictDO::getValue, Function.identity(), (a, b) -> b));
            // 修改
            List<TransmissionOrganDictDO> updateList = reqVOS.stream().filter(r -> existsDictMap.containsKey(r.getValue()))
                .map(r -> TransmitterDictConvert.INSTANCE.convertDo(r).setId(existsDictMap.get(r.getValue()).getId())).toList();
            if (CollUtil.isNotEmpty(updateList)) {
                transmissionOrganDictMapper.updateBatch(updateList);
            }
            // 新增
            List<TransmissionOrganDictDO> createList = reqVOS.stream().filter(r -> !existsDictMap.containsKey(r.getValue()))
                .map(TransmitterDictConvert.INSTANCE::convertDo).toList();
            if (CollUtil.isNotEmpty(createList)) {
                transmissionOrganDictMapper.insertBatch(createList);
            }
        }
    }


    @Resource
    private TransmissionOrganDictMatchMapper transmissionOrganDictMatchMapper;

    @Override
    @Async
    public void diagnosisMatch(Integer organId) {
        Long count = transmissionOrganDictMapper.selectCount(organId, DictTypeConstants.DIAGNOSIS_DICT);
        if (count <= 0) {
            return;
        }

        // 1.分页查询organId诊断字典
        int pageSize = 500; // 每页处理500条
        int totalPages = (int) Math.ceil((double) count / pageSize);
        TransmissionOrganDictPageReqVO pageReqVO = TransmissionOrganDictPageReqVO.builder().dictType(DictTypeConstants.DIAGNOSIS_DICT).organId(organId).build();
        for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
            pageReqVO.setPageNo(pageNum);
            pageReqVO.setPageSize(pageSize);

            PageResult<TransmissionOrganDictDO> pageResult = transmissionOrganDictMapper.selectPage(pageReqVO);
            if (CollUtil.isEmpty(pageResult.getList())) {
                continue;
            }

            // 提取字典名称列表
            List<String> dictNames = pageResult.getList().stream()
                .map(TransmissionOrganDictDO::getLabel) // 使用label字段作为匹配字段
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

            if (CollUtil.isEmpty(dictNames)) {
                continue;
            }

            // 2.批量查询诊断表
            List<InquiryDiagnosisDto> diagnosisList = inquiryGenericApiService.queryDiagnosisByCondition(dictNames);

            if (CollUtil.isEmpty(diagnosisList)) {
                continue;
            }

            // 创建诊断名称到诊断信息的映射
            Map<String, InquiryDiagnosisDto> diagnosisMap = diagnosisList.stream()
                .collect(Collectors.toMap(d -> d.getShowName().concat("_").concat(d.getDiagnosisType() + ""), Function.identity(), (a, b) -> a));

            TransmissionOrganDictMatchPageReqVO matchPageReqVO = TransmissionOrganDictMatchPageReqVO.builder().organId(organId).dictType(DictTypeConstants.DIAGNOSIS_DICT)
                .dictIds(diagnosisList.stream().map(InquiryDiagnosisDto::getId).toList()).build();
            Map<Long, TransmissionOrganDictMatchDO> matchDOMap = transmissionOrganDictMatchMapper.selectByCondition(matchPageReqVO).stream()
                .collect(Collectors.toMap(TransmissionOrganDictMatchDO::getDictId, Function.identity(), (a, b) -> a));

            // 3.收集需要创建的匹配记录
            List<TransmissionOrganDictMatchDO> matchDOList = new ArrayList<>();

            for (TransmissionOrganDictDO dictDO : pageResult.getList()) {
                if (StringUtils.isBlank(dictDO.getLabel())) {
                    continue;
                }
                InquiryDiagnosisDto matchedDiagnosis = diagnosisMap.get(dictDO.getLabel().concat("_").concat(dictDO.getOuterValue()));
                if (matchedDiagnosis != null) {
                    // 如果已存在匹配记录，则跳过
                    if (matchDOMap.containsKey(matchedDiagnosis.getId())) {
                        continue;
                    }
                    // 创建新的匹配记录DO
                    TransmissionOrganDictMatchDO matchDO = TransmissionOrganDictMatchDO.builder()
                        .organId(organId)
                        .dictType(DictTypeConstants.DIAGNOSIS_DICT)
                        .dictId(matchedDiagnosis.getId())
                        .organDictId(dictDO.getId())
                        .status(DictMatchEnum.MATCH.getCode())
                        .build();

                    matchDOList.add(matchDO);
                }
            }

            // 4.批量插入匹配记录
            if (CollUtil.isNotEmpty(matchDOList)) {
                transmissionOrganDictMatchMapper.insertBatch(matchDOList);
            }
        }
    }
}