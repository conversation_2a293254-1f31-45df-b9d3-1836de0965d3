spring:
  cloud:
    nacos:
      server-addr: mse-5bfd0582-nacos-ans.mse.aliyuncs.com:8848
      discovery:
        namespace: e1b55694-2e80-4aec-aa56-4aae6fd307ff
        group: http
        register-enabled: false
      config:
        namespace: e1b55694-2e80-4aec-aa56-4aae6fd307ff
        group: DEFAULT_GROUP
  config:
    import:
      - optional:nacos:inquiry-global-${spring.profiles.active}.yaml
  datasource:
    dynamic:
      datasource:
        master:
          url: ***********************************************************************************************************************************************************************************************
          username: app_saas_system_w
          password: Gmp387stnMkeD2aDeBHx
        inquiry:
          url: ************************************************************************************************************************************************************************************************
          username: app_saas_inquiry_w
          password: 5V5DWrcb2DsJUwkbKYHG
  data:
    redis:
      #      host: ************** # 地址
      host: db011-saas.prod.redis.ybm100.top # 地址
      port: 50001 # 端口
      password: Rmzhz63yYz5VwhQQmRZb # 密码，建议生产环境开启
      database: 14
  #定时任务 quartz 配置 ---------------------
  quartz:
    auto-startup: true # 本地开发环境，尽量不要开启 Job
    scheduler-name: schedulerName # Scheduler 名字。默认为 schedulerName
    job-store-type: jdbc # Job 存储器类型。默认为 memory 表示内存，可选 jdbc 使用数据库。
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    properties: # 添加 Quartz Scheduler 附加属性，更多可以看 http://www.quartz-scheduler.org/documentation/2.4.0-SNAPSHOT/configuration.html 文档
      org:
        quartz:
          # Scheduler 相关配置
          scheduler:
            instanceName: schedulerName
            instanceId: AUTO # 自动生成 instance ID
          # JobStore 相关配置
          jobStore:
            # JobStore 实现类。可见博客：https://blog.csdn.net/weixin_42458219/article/details/122247162
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            isClustered: true # 是集群模式
            clusterCheckinInterval: 15000 # 集群检查频率，单位：毫秒。默认为 15000，即 15 秒
            misfireThreshold: 60000 # misfire 阀值，单位：毫秒。
          # 线程池相关配置
          threadPool:
            threadCount: 25 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级
            class: org.quartz.simpl.SimpleThreadPool # 线程池类型
    jdbc: # 使用 JDBC 的 JobStore 的时候，JDBC 的配置
      initialize-schema: NEVER # 是否自动使用 SQL 初始化 Quartz 表结构。这里设置成 never ，我们手动创建表结构。

  elasticsearch:
    uris: ${ES_HOSTS:es001-inquiry-prod.elasticsearch.ybm100.top:19200,es002-inquiry-prod.elasticsearch.ybm100.top:19200,es003-inquiry-prod.elasticsearch.ybm100.top:19200}
    connection-timeout: 5s
    socket-timeout: 30s

rocketmq:
  name-server: mq01-inquiry-prod.rocketmq.ybm100.top:9876;mq02-inquiry-prod.rocketmq.ybm100.top:9876;mq03-inquiry-prod.rocketmq.ybm100.top:9876;mq04-inquiry-prod.rocketmq.ybm100.top:9876
  #  topic:
  #    inquiry-topic: xyy_saas_inquiry_topic_test
  producer:
    group: saas_inquiry_system

external:
  rocketmq:
    middle:
      nameServer: mq1-me-prod.rocketmq.ybm100.top:9876;mq2-me-prod.rocketmq.ybm100.top:9876;mq3-me-prod.rocketmq.ybm100.top:9876;mq4-me-prod.rocketmq.ybm100.top:9876
    saas:
      nameServer: mq01-saas-prod.rocketmq.ybm100.top:9876;mq02-saas-prod.rocketmq.ybm100.top:9876;mq03-saas-prod.rocketmq.ybm100.top:9876;mq04-saas-prod.rocketmq.ybm100.top:9876

# 百度api-sdk
#baidu:
#  api:
#    face: # 人脸识别
#      groupId: inquiry_prod
#      appId: 11
#      appKey: 22
#      secretKey: 33
# 腾讯api-sdk
tencent:
  api:
    face: # 人脸识别
      group-id: inquiry_prod
      secret-id: AKIDGOK3Zsx40ldWIEIE5uqcTDpY6RaWFm6D
      secret-key: wfBTYQ4VnuqZ4MSPBwXw7A1f6LOFsXHs


# binlog 同步 ES 任务配置
binlog:
  sync:
    enabled: true
    tasks:
      - task-id: binlog-sync-saas_product_stdlib
        task-name: binlog同步ES自建标准库
        enabled: true
        data-source-id: master
        topics: Canal_xyy_saas_system_saas_product_stdlib
        table-mapping:
          table-name: saas_product_stdlib
          es-index: saas_product_stdlib_index
          es-type: saas_product_stdlib
          es-settings:
            number_of_shards: 3
            number_of_replicas: 1
        full-sync-config:
          enabled: false
          task-name: 全量同步ES自建标准库
          thread-count: 8

    # RocketMQ配置 - 只配置消费者
    rocketmq:
      name-servers: ${rocketmq.name-server}
      consumer:
        consume-thread-min: 8
        consume-thread-max: 8