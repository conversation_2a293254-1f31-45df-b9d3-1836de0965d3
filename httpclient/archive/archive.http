### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "username": "admin",
  "password": "DASDF1A."
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}


### 2. 执行表数据归档
POST {{baseAdminSystemUrl}}/system/archive-table/execute/1
Content-Type: application/json
Authorization: Bearer {{token}}
