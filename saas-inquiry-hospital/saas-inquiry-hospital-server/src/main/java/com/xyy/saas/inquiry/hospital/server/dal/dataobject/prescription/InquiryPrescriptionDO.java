package com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditWayTypeEnum;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionExtDto;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 处方记录 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_inquiry_prescription", autoResultMap = true)
// @KeySequence("saas_inquiry_prescription_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryPrescriptionDO extends BaseDO {

    /**
     * 处方ID
     */
    @TableId
    private Long id;

    /**
     * 处方编码
     */
    private String pref;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 问诊编号
     */
    private String inquiryPref;
    /**
     * 互联网医院编码
     */
    private String hospitalPref;
    /**
     * 处方笺模版GUID
     */
    private String preTempId;
    /**
     * 科室编码
     */
    private String deptPref;
    /**
     * 科室编码
     */
    private String deptName;
    /**
     * 病历GUID
     */
    private String diseaseCasesPref;
    /**
     * 患者GUID
     */
    private String patientPref;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者性别：1 男 2 女
     */
    private Integer patientSex;
    /**
     * 患者年龄
     */
    private String patientAge;
    /**
     * 患者手机号
     */
    private String patientMobile;
    /**
     * 医生编码
     */
    private String doctorPref;
    /**
     * 医师姓名
     */
    private String doctorName;
    /**
     * 药师编码
     */
    private String pharmacistPref;
    /**
     * 药师姓名
     */
    private String pharmacistName;
    /**
     * 是否自动开方：0 否  、 1是
     */
    private Integer autoInquiry;
    /**
     * 处方审核级数
     */
    private Integer auditLevel;
    /**
     * 处方状态  0、待开方   1、已取消   2、待审核     3、审核中  4、审核通过   5、审核驳回
     */
    private Integer status;
    /**
     * 当前审方人类型 1-医生,2-药店,3-平台,4-医院 {@link com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum}
     */
    private Integer auditorType;
    /**
     * 处方分发状态 0-未分配,1-已分配
     */
    private Integer distributeStatus;
    /**
     * 处方分配的用户id
     */
    private Long distributeUserId;

    /**
     * 用药类型：0西药，1中药
     */
    private Integer medicineType;
    /**
     * 使用状态：0 初始 1可用 2已用 3过期  4失效
     */
    private Integer useStatus;
    /**
     * 失效原因
     */
    private String invalidReason;
    /**
     * 失效时间
     */
    private LocalDateTime invalidTime;
    /**
     * 费别
     */
    private String feeType;
    /**
     * 问诊开始时间
     */
    private LocalDateTime inquiryStartTime;
    /**
     * 问诊结束时间
     */
    private LocalDateTime inquiryEndTime;
    /**
     * 医师出方时间
     */
    private LocalDateTime outPrescriptionTime;
    /**
     * 药师审核时间
     */
    private LocalDateTime auditPrescriptionTime;
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 三方平台处方单号
     */
    private String thirdPrescriptionNo;

    /**
     * 主诉
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> mainSuit;
    /**
     * 诊断编码
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> diagnosisCode;
    /**
     * 诊断说明
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> diagnosisName;
    /**
     * 处方笺图片url
     */
    private String prescriptionImgUrl;
    /**
     * 处方笺PDFurl
     */
    private String prescriptionPdfUrl;
    /**
     * 病历img图片url
     */
    private String caseImgUrl;
    /**
     * 问诊方式  1、图文问诊  2、视频问诊  3、电话问诊
     */
    private Integer inquiryWayType;
    /**
     * 问诊业务类型 1、药店问诊  2、远程审方
     */
    private Integer inquiryBizType;
    /**
     * 审方方式类别 1、荷叶审方  2、远程审方 {@link PrescriptionAuditWayTypeEnum}
     */
    private Integer auditWayType;
    /**
     * 处方类型
     */
    private Integer prescriptionType;
    /**
     * 客户端渠类型 0、app  1、pc  2、小程序
     */
    private Integer clientChannelType;
    /**
     * 医生客户端类型 0、app  1、pc
     */
    private Integer doctorChannelType;
    /**
     * 医生操作系统类型 Android ，iOS，Windows，Mac，Linux
     */
    private String doctorOsType;
    /**
     * 问诊渠道 0、荷叶 1、智慧脸  2、海典ERP
     */
    private Integer bizChannelType;
    /**
     * 处方打印状态（0-未打印、1-已打印、NULL -未知）
     */
    private Integer printStatus;
    /**
     * 签章平台类型 1、法大大  2、自绘
     */
    private Integer signPlatform;

    /**
     * 数据状态 0 有效 1 作废 {@link CommonStatusEnum}
     */
    private Integer enable;

    /**
     * 处方拓展字段
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private PrescriptionExtDto ext;

    @JsonIgnore
    public PrescriptionExtDto extGet() {
        if (ext == null) {
            ext = new PrescriptionExtDto();
        }
        return ext;
    }
}