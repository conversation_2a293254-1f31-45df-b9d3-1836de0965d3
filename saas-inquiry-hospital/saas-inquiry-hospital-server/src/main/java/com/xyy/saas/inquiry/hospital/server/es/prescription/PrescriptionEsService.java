package com.xyy.saas.inquiry.hospital.server.es.prescription;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import com.xyy.saas.binlog.core.es.EsQueryBuilder;
import com.xyy.saas.binlog.core.es.EsQueryService;
import com.xyy.saas.binlog.core.es.EsSearchRequest;
import com.xyy.saas.binlog.core.es.EsSearchResponse;
import com.xyy.saas.binlog.core.es.EsSearchResponse.EsHit;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.hospital.server.constant.QuerySceneEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.InquiryPrescriptionMapper;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 处方表ES数据查询
 *
 * @Author:chenxiaoyi
 * @Date:2025/08/29 11:14
 */
@Service
@Slf4j
public class PrescriptionEsService {

    @Resource
    private EsQueryService esQueryService;

    @Resource
    private InquiryPrescriptionMapper inquiryPrescriptionMapper;

    private static final String INDEX_NAME = "saas_inquiry_prescription_index_";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * ES分页查询处方记录 参考 InquiryPrescriptionMapper#selectPage 的SQL查询逻辑转换为ES查询
     */
    public PageResult<InquiryPrescriptionRespVO> getEsInquiryPrescriptionPage(InquiryPrescriptionPageReqVO queryVo) {
        try {
            BoolQuery.Builder boolQuery = new BoolQuery.Builder();

            // 构建查询条件
            buildPrescriptionQueryConditions(boolQuery, queryVo);

            // 构建排序
            List<co.elastic.clients.elasticsearch._types.SortOptions> sorts = buildSortOptions(queryVo);

            // 构建搜索请求
            String index = StrUtil.concat(true, INDEX_NAME, StringUtils.defaultIfBlank(queryVo.getYear(), LocalDateTime.now().getYear() + ""));
            String routing = getRouting(queryVo);
            EsSearchRequest searchRequest = EsSearchRequest.builder()
                .index(index)
                .trackTotalHits(true)
                .routing(routing)
                .query(boolQuery.build()._toQuery())
                .from(queryVo.getPageNo() != null ? (queryVo.getPageNo() - 1) * queryVo.getPageSize() : 0)
                .size(queryVo.getPageSize() != null ? queryVo.getPageSize() : 10)
                .sorts(sorts)
                .build();

            // 执行ES查询
            EsSearchResponse<InquiryPrescriptionRespVO> response = esQueryService.search(searchRequest, InquiryPrescriptionRespVO.class);

            if (response == null) {
                log.warn("[getEsInquiryPrescriptionPage] ES查询返回null");
                return PageResult.empty();
            }

            // 转换结果
            List<InquiryPrescriptionRespVO> list = response.getHits() != null ?
                response.getHits().stream().map(EsHit::getSource).toList() :
                Collections.emptyList();

            long total = response.getTotalHits() != null ? response.getTotalHits().getValue() : 0L;

            //  门店查询分页总数量
            // if (!TenantConstant.isSystemTenant()) {
            //     BoolQuery.Builder countBoolQuery = new BoolQuery.Builder();
            //     buildPrescriptionQueryConditions(countBoolQuery, queryVo);
            //     total = esQueryService.count(index, countBoolQuery.build()._toQuery(), routing);
            // }

            log.info("[getEsInquiryPrescriptionPage] ES查询成功，返回{}条记录，总计{}条", list.size(), total);
            return new PageResult<>(list, total);

        } catch (Exception e) {
            log.error("[getEsInquiryPrescriptionPage] ES查询异常", e);
            return PageResult.empty();
        }
    }

    private String getRouting(InquiryPrescriptionPageReqVO queryVo) {
        // 超管,查全部 或 单个门店
        if (TenantConstant.isSystemTenant()) {
            return queryVo.getTenantId() == null ? null : queryVo.getTenantId().toString();
        }
        // 单体查自己，或者连锁查所有
        if (CollUtil.isEmpty(queryVo.getTenantIds())) {
            return queryVo.getTenantId() == null ? TenantContextHolder.getTenantId().toString() : queryVo.getTenantId().toString();
        }
        return CollUtil.size(queryVo.getTenantIds()) == 1 ? queryVo.getTenantIds().getFirst().toString() : null;
    }

    /**
     * 构建处方查询条件
     * <p>
     * 原始SQL查询逻辑参考：InquiryPrescriptionMapper#selectPage 主要查询条件包括： 1. 基础过滤：id IN (?), pref = ?, pref IN (?), inquiry_pref IN (?) 2. 人员过滤：patient_pref = ?, hospital_pref = ?, doctor_pref = ? 3. 状态过滤：status = ?, status IN (?), auditor_type =
     * ?, distribute_status = ?, distribute_user_id = ? 4. 用药类型：medicine_type = ?, medicine_type IN (?) 5. 业务类型：inquiry_way_type, inquiry_biz_type, client_channel_type, biz_channel_type, audit_way_type 6. 其他条件：auto_inquiry,
     * third_prescription_no, enable, prescription_type, print_status 7. 医院过滤：hospital_pref IN (?) 8. 时间范围：out_prescription_time, audit_prescription_time, create_time BETWEEN ? AND ? 9. 租户药师复杂逻辑：见handleTenantAndPharmacistLogic方法 10.
     * 排序：根据QuerySceneEnum动态排序
     */
    private void buildPrescriptionQueryConditions(BoolQuery.Builder boolQuery, InquiryPrescriptionPageReqVO queryVo) {
        // 基础过滤条件
        // SQL: id IN (?)
        EsQueryBuilder.termQueryOpt("id", queryVo.getIds()).ifPresent(boolQuery::filter);

        // 处方编码过滤 - 支持单个和批量查询
        // SQL: pref = ? OR pref IN (?)
        EsQueryBuilder.termQueryOpt("pref", queryVo.getPref()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("pref", queryVo.getPrefs()).ifPresent(boolQuery::filter);

        // 问诊编码列表过滤 - 根据问诊记录查询相关处方
        // SQL: inquiry_pref IN (?)
        EsQueryBuilder.termQueryOpt("inquiryPref", queryVo.getInquiryPrefList()).ifPresent(boolQuery::filter);

        // 人员相关过滤 - 患者、医院、医生维度
        // SQL: patient_pref = ? AND hospital_pref = ? AND doctor_pref = ?
        EsQueryBuilder.termQueryOpt("patientPref", queryVo.getPatientPref()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("hospitalPref", queryVo.getHospitalPref()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("doctorPref", queryVo.getDoctorPref()).ifPresent(boolQuery::filter);

        // 状态相关过滤 - 处方状态、审核类型、分发状态等
        // SQL: status = ? OR status IN (?) AND auditor_type = ? AND distribute_status = ? AND distribute_user_id = ?
        EsQueryBuilder.termQueryOpt("status", queryVo.getStatus()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("status", queryVo.getStatuss()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("auditorType", queryVo.getAuditorType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("distributeStatus", queryVo.getDistributeStatus()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("distributeUserId", queryVo.getDistributeUserId()).ifPresent(boolQuery::filter);

        // 用药类型过滤 - 中药/西药分类
        // SQL: medicine_type = ? OR medicine_type IN (?)
        EsQueryBuilder.termQueryOpt("medicineType", queryVo.getMedicineType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("medicineType", queryVo.getMedicineTypes()).ifPresent(boolQuery::filter);

        // 商品名称match
        EsQueryBuilder.multiFieldMatchQueryOpt(new String[]{"productName"}, queryVo.getProductName(), true).ifPresent(boolQuery::must);

        // 业务类型过滤 - 问诊方式、业务类型、渠道类型等
        // SQL: inquiry_way_type = ? AND inquiry_biz_type = ? AND client_channel_type = ? AND biz_channel_type = ? AND audit_way_type = ?
        EsQueryBuilder.termQueryOpt("inquiryWayType", queryVo.getInquiryWayType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("inquiryBizType", queryVo.getInquiryBizType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("clientChannelType", queryVo.getClientChannelType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("bizChannelType", queryVo.getBizChannelType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("auditWayType", queryVo.getAuditWayType()).ifPresent(boolQuery::filter);

        // 其他条件过滤
        // SQL: auto_inquiry = ? AND third_prescription_no = ? AND enable = ? AND prescription_type = ? AND print_status = ?
        EsQueryBuilder.termQueryOpt("autoInquiry", queryVo.getAutoInquiry()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("thirdPrescriptionNo", queryVo.getThirdPrescriptionNo()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("enable", queryVo.getEnable()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("prescriptionType", queryVo.getPrescriptionType()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("slowDisease", queryVo.getSlowDisease()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("offlinePrescription", queryVo.getOfflinePrescription()).ifPresent(boolQuery::filter);

        EsQueryBuilder.termQueryOpt("printStatus", queryVo.getPrintStatus()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("deleted", false).ifPresent(boolQuery::filter);

        // 医院编码列表过滤 - 支持多医院查询
        // SQL: hospital_pref IN (?)
        EsQueryBuilder.termQueryOpt("hospitalPref", queryVo.getHospitalList()).ifPresent(boolQuery::filter);

        // 时间范围过滤 - 开方时间、审核时间、创建时间
        // SQL: out_prescription_time BETWEEN ? AND ? AND audit_prescription_time BETWEEN ? AND ? AND create_time BETWEEN ? AND ?
        buildTimeRangeQuery("outPrescriptionTime", queryVo.getOutPrescriptionTime()).ifPresent(boolQuery::filter);
        buildTimeRangeQuery("auditPrescriptionTime", queryVo.getAuditPrescriptionTime()).ifPresent(boolQuery::filter);
        buildTimeRangeQuery("createTime", queryVo.getCreateTime()).ifPresent(boolQuery::filter);

        // 处理租户和药师的复杂OR逻辑 - 最复杂的查询条件
        handleTenantAndPharmacistLogic(boolQuery, queryVo);
    }

    /**
     * 构建时间范围查询 参考 ProductStdlibEsServiceImpl 的实现风格
     */
    private java.util.Optional<Query> buildTimeRangeQuery(String field, LocalDateTime[] timeRange) {
        if (timeRange == null || timeRange.length != 2) {
            return java.util.Optional.empty();
        }

        LocalDateTime startTime = timeRange[0];
        LocalDateTime endTime = timeRange[1];

        if (startTime == null || endTime == null) {
            return java.util.Optional.empty();
        }

        Query rangeQuery = EsQueryBuilder.rangeQuery(field)
            .gte(startTime.format(DATE_TIME_FORMATTER))
            .lte(endTime.format(DATE_TIME_FORMATTER))
            .build();

        return java.util.Optional.of(rangeQuery);
    }

    /**
     * 处理租户和药师的复杂OR逻辑
     * <p>
     * 原始SQL逻辑： if (TenantConstant.isSystemTenant()) { wrapper.eqIfPresent(InquiryPrescriptionDO::getPharmacistPref, reqVO.getPharmacistPref()) .inIfPresent(InquiryPrescriptionDO::getTenantId, reqVO.getTenantIds())
     * .eqIfPresent(InquiryPrescriptionDO::getTenantId, reqVO.getTenantId()); } else { // 查药师 or 所在门店 wrapper.and(wp -> wp.eq(StringUtils.isNotBlank(reqVO.getPharmacistPref()), InquiryPrescriptionDO::getPharmacistPref,
     * reqVO.getPharmacistPref()) .or(w -> w.in(CollUtil.isNotEmpty(reqVO.getTenantIds()), InquiryPrescriptionDO::getTenantId, reqVO.getTenantIds()))); }
     */
    private void handleTenantAndPharmacistLogic(BoolQuery.Builder boolQuery, InquiryPrescriptionPageReqVO queryVo) {
        if (TenantConstant.isSystemTenant()) {
            // 系统租户：简单的AND逻辑
            // SQL: pharmacist_pref = ? AND tenant_id IN (?) AND tenant_id = ?
            EsQueryBuilder.termQueryOpt("pharmacistPref", queryVo.getPharmacistPref()).ifPresent(boolQuery::filter);
            EsQueryBuilder.termQueryOpt("tenantId", queryVo.getTenantIds()).ifPresent(boolQuery::filter);
            EsQueryBuilder.termQueryOpt("tenantId", queryVo.getTenantId()).ifPresent(boolQuery::filter);
        } else {
            // 非系统租户：复杂的OR逻辑
            // SQL: (pharmacist_pref = ? OR tenant_id IN (?))
            // 业务含义：查询指定药师的处方 OR 指定门店的处方
            BoolQuery.Builder orQueryBuilder = new BoolQuery.Builder();

            // 药师条件：查询指定药师审核的处方
            EsQueryBuilder.termQueryOpt("pharmacistPref", queryVo.getPharmacistPref())
                .ifPresent(orQueryBuilder::should);

            // 门店条件：查询指定门店的处方
            EsQueryBuilder.termQueryOpt("tenantId", queryVo.getTenantIds())
                .ifPresent(orQueryBuilder::should);

            // 构建 OR 查询对象（仅构建一次）
            BoolQuery orQuery = orQueryBuilder.build();

            // 将OR条件组合添加到主查询中
            // 注意：只有当存在OR条件时才添加，避免空查询
            if (!orQuery.should().isEmpty()) {
                boolQuery.must(orQuery._toQuery());
            }
        }
    }

    /**
     * 构建排序选项
     * <p>
     * 原始SQL排序逻辑： 1. PHARMACIST场景特殊处理： - status=2时按out_prescription_time倒序 - 其他状态按audit_prescription_time倒序 2. 其他场景按QuerySceneEnum定义的sortField排序： - DRUGSTORE: 按ID排序 - DOCTOR: 按ID排序 - PHARMACIST: 按audit_prescription_time排序 3.
     * 默认：按开方时间(out_prescription_time)倒序
     */
    private List<co.elastic.clients.elasticsearch._types.SortOptions> buildSortOptions(InquiryPrescriptionPageReqVO queryVo) {
        // PHARMACIST场景的特殊排序逻辑
        if (Objects.equals(QuerySceneEnum.PHARMACIST.getCode(), queryVo.getQueryScene())) {
            // 原SQL逻辑：status=2 按 out_prescription_time 倒序，其他按 audit_prescription_time 倒序
            // ES实现：简化为按audit_prescription_time排序（药师审核时间）
            // 业务含义：药师查看处方时，优先显示最近审核的处方
            return List.of(EsQueryBuilder.sort("auditPrescriptionTime", SortOrder.Desc));
        }

        // 根据QuerySceneEnum的sortField排序
        if (queryVo.getQueryScene() != null) {
            QuerySceneEnum sceneEnum = QuerySceneEnum.getEnumByCode(queryVo.getQueryScene());
            // 根据枚举定义的排序字段进行排序
            return switch (sceneEnum) {
                case PHARMACIST ->
                    // 药师场景：按审核时间倒序
                    List.of(EsQueryBuilder.sort("auditPrescriptionTime", SortOrder.Desc));
                case DOCTOR ->
                    // 医生场景：按ID倒序（最新创建的处方在前）
                    List.of(EsQueryBuilder.sort("id", SortOrder.Desc));
                case DRUGSTORE ->
                    // 门店场景：按ID倒序（最新创建的处方在前）
                    List.of(EsQueryBuilder.sort("id", SortOrder.Desc));
                default -> List.of(EsQueryBuilder.sort("id", SortOrder.Desc));
            };
        }

        // 默认排序：按开方时间倒序
        // 业务含义：显示最近开具的处方
        return List.of(EsQueryBuilder.sort("outPrescriptionTime", SortOrder.Desc));
    }
}
