package com.xyy.saas.inquiry.hospital.server.convert.clinicalcase;

import com.xyy.saas.inquiry.hospital.api.clinicalcase.dto.InquiryClinicalCaseRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase.vo.InquiryClinicalCaseRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase.vo.InquiryClinicalCaseSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.clinicalcase.InquiryClinicalCaseDO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.inquiry.ClinicalCaseExtDto;
import com.xyy.saas.inquiry.pojo.transmitter.internet.OutPatientCaseTransmitterDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 病例信息
 *
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryClinicalCaseConvert {

    InquiryClinicalCaseConvert INSTANCE = Mappers.getMapper(InquiryClinicalCaseConvert.class);

    default InquiryClinicalCaseDO convert(InquiryClinicalCaseSaveReqVO createReqVO, InquiryRecordDto inquiryRecord, InquiryRecordDetailDto recordDetailDto) {
        InquiryClinicalCaseDO caseDO = convert(createReqVO);
        convertFillCaseInquiry(caseDO, inquiryRecord);

        caseDO.extGet().setLiverKidneyValue(recordDetailDto.getLiverKidneyValue());
        caseDO.extGet().setGestationLactationValue(recordDetailDto.getGestationLactationValue());

        caseDO.setPatientIdCard(recordDetailDto.getPatientIdCard());
        caseDO.setCurrentIllnessDesc(recordDetailDto.getCurrentIllnessDesc());
        caseDO.setPatientHisDesc(recordDetailDto.getPatientHisDesc());
        return caseDO;
    }

    @Mapping(target = "pref", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "patientIdCard", ignore = true)
    void convertFillCaseInquiry(@MappingTarget InquiryClinicalCaseDO caseDO, InquiryRecordDto inquiryRecord);

    @Mapping(target = "diagnosisCode", expression = "java(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(createReqVO.getDiagnosis(), com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSimpleVO::getDiagnosisCode))")
    @Mapping(target = "diagnosisName", expression = "java(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(createReqVO.getDiagnosis(), com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSimpleVO::getDiagnosisName))")
    @Mapping(target = "tcmDiagnosisCode", expression = "java(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(createReqVO.getTcmDiagnosis(), com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo"
        + ".InquiryDiagnosisSimpleVO::getDiagnosisCode))")
    @Mapping(target = "tcmDiagnosisName", expression = "java(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(createReqVO.getTcmDiagnosis(), com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo"
        + ".InquiryDiagnosisSimpleVO::getDiagnosisName))")
    InquiryClinicalCaseDO convert(InquiryClinicalCaseSaveReqVO createReqVO);

    InquiryClinicalCaseRespVO convertVo(InquiryClinicalCaseDO caseDO);

    default InquiryClinicalCaseDO convert(InquiryRecordDto inquiryRecord, InquiryRecordDetailDto inquiryRecordDetail) {
        InquiryClinicalCaseDO caseDO = convert(inquiryRecordDetail);
        caseDO.setExt(convertExt(inquiryRecordDetail));
        convertFillCaseInquiry(caseDO, inquiryRecord);
        return caseDO;
    }

    ClinicalCaseExtDto convertExt(InquiryRecordDetailDto inquiryRecordDetail);

    @Mapping(target = "pref", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "tcmDiagnosisCode", expression = "java(inquiryRecord.getExt().getDiagnosisCode())")
    @Mapping(target = "tcmDiagnosisName", expression = "java(inquiryRecord.getExt().getDiagnosisName())")
    InquiryClinicalCaseDO convert(InquiryRecordDetailDto inquiryRecord);

    InquiryClinicalCaseRespDto convertDto(InquiryClinicalCaseRespVO clinicalCaseRespVO);

    @Mapping(target = "idCard", source = "patientIdCard")
    @Mapping(target = "fullName", source = "patientName")
    @Mapping(target = "businessNo", source = "pref")
    OutPatientCaseTransmitterDTO convertTransmissionDTO(InquiryClinicalCaseRespVO inquiryClinicalCaseRespVO);
}
