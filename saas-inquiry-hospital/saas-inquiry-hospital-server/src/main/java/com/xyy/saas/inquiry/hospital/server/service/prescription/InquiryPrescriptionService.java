package com.xyy.saas.inquiry.hospital.server.service.prescription;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionFlushQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionAbandonVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionExcelRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPdfReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPricingVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.InquiryPrescriptionCountVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.List;


/**
 * 处方记录 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryPrescriptionService {

    /**
     * 创建处方记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    InquiryPrescriptionRespDTO createInquiryPrescription(@Valid InquiryPrescriptionSaveReqVO createReqVO);

    /**
     * 更新处方记录
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryPrescription(InquiryPrescriptionSaveReqVO updateReqVO);

    /**
     * 删除处方记录
     *
     * @param id 编号
     */
    void deleteInquiryPrescription(Long id);

    /**
     * 获得处方记录
     *
     * @param id 编号
     * @return 处方记录
     */
    InquiryPrescriptionRespVO getInquiryPrescription(Long id);

    /**
     * 处方打印
     *
     * @param id
     * @return
     */
    String printInquiryPrescription(Long id);


    /**
     * 获得处方记录分页
     *
     * @param pageReqVO 分页查询
     * @return 处方记录分页
     */
    PageResult<InquiryPrescriptionRespVO> getInquiryPrescriptionPage(InquiryPrescriptionPageReqVO pageReqVO);


    /**
     * 处方数量统计
     *
     * @return
     */
    InquiryPrescriptionCountVO getInquiryPrescriptionCount();

    /**
     * 根据条件查询一条处方信息
     *
     * @param prescriptionQueryDTO
     * @return
     */
    InquiryPrescriptionRespVO queryByCondition(InquiryPrescriptionQueryDTO prescriptionQueryDTO);

    /**
     * 根据条件统计处方数量
     *
     * @param reqVO
     * @return
     */
    Long getPrescriptionCount(InquiryPrescriptionPageReqVO reqVO);

    /**
     * 处方分配药师审核
     *
     * @param id     处方id
     * @param userId userId
     * @return 是否分配成功
     */
    boolean distributePharmacist(Long id, Long userId);

    /**
     * 处方释放审核药师
     *
     * @param id     处方id
     * @param userId userId
     * @return 是否分配成功
     */
    boolean releasePharmacist(Long id, Long userId);

    /**
     * 处方开具后置处理
     *
     * @param pref 处方编号
     */
    void postProcessIssuesPrescription(String pref);

    /**
     * 处方划价
     *
     * @param pricingVO 划价vo
     * @return
     */
    Long pricingInquiryPrescription(InquiryPrescriptionPricingVO pricingVO);


    IPage<PatientSimpleDTO> getPrescriptionPatientList(InquiryPrescriptionQueryDTO queryDTO);

    /**
     * 获取excel导出对象
     *
     * @param pageReqVO
     * @return
     */
    List<InquiryPrescriptionExcelRespVO> getInquiryPrescriptionExcelList(InquiryPrescriptionPageReqVO pageReqVO);

    /**
     * 管理员更新处方
     *
     * @param updateReqVO
     */
    void updateInquiryPrescriptionByAdmin(InquiryPrescriptionSaveReqVO updateReqVO);

    /**
     * 条件查询处方列表
     *
     * @param queryReqVo
     * @return
     */
    List<InquiryPrescriptionDO> queryListByCondition(InquiryPrescriptionPageReqVO queryReqVo);

    /**
     * 根据编码查询处方
     *
     * @param prefs
     * @return
     */
    List<InquiryPrescriptionDO> queryListByPrefs(List<String> prefs);


    /**
     * 批量打印处方
     *
     * @param ids
     * @return
     */
    List<String> printInquiryPrescriptions(InquiryPrescriptionPdfReqVO pdfReqVO);


    /**
     * 批量拼接打印
     *
     * @param exportPdfVO
     * @return
     */
    List<String> batchCombinerPrintInquiryPrescription(@Valid InquiryPrescriptionPdfReqVO exportPdfVO);


    /**
     * 导出处方pdf图片成zip下载
     *
     * @param pageReqVO
     * @return
     */
    void exportInquiryPrescriptionPdf(@Valid InquiryPrescriptionPdfReqVO exportPdfVO, HttpServletResponse response);


    /**
     * 作废处方
     *
     * @param abandonVO
     */
    void abandon(InquiryPrescriptionAbandonVO abandonVO);

    /**
     * 恢复作废处方
     *
     * @param abandonVO
     */
    void abandonRestore(InquiryPrescriptionAbandonVO abandonVO);

    /**
     * 获取刷 线下审方处方
     *
     * @param flushQueryDTO
     * @return
     */
    PageResult<InquiryPrescriptionDO> getFlushOfflinePrescriptionPage(InquiryPrescriptionFlushQueryDTO flushQueryDTO);

    /**
     * 获取处方记录 + 处理图片日期
     *
     * @param queryDTO
     * @return
     */
    InquiryPrescriptionRespVO getInquiryPrescriptionWithPdf(InquiryPrescriptionQueryDTO queryDTO);

    /**
     * 获取es开关
     *
     * @return
     */
    Boolean esSearchSwitch();
}