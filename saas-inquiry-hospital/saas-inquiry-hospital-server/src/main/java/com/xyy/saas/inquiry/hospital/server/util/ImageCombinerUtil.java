package com.xyy.saas.inquiry.hospital.server.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import com.xyy.saas.inquiry.util.FileApiUtil;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import lombok.extern.slf4j.Slf4j;

/**
 * 图片合并处理器
 */
@Slf4j
public class ImageCombinerUtil {

    // public static void main(String[] args) {
    //     final List<P> pList = List.of(P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250225/c1f2c2af48ec6b78adaab07ca31672af3a4045f0fa125762b6a7959560ebbd4b.jpg").build()
    //         , P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250109/f40b596fc9dd8b2d2f3e5dd2eb4ceff8771b59e99a256996d7e9bd1ef978f0bc.jpg").build()
    //         , P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250109/47552c02cb59b3a038877ee827f90f13cc808851858018a170335aff877e0b62.jpg").build()
    //         , P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250109/6fe6bc55c17b4b258f77e87fb26b4ac9bda832670167228c87bd45ac8a1ae7f4.jpg").build()
    //         , P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250109/45e3429847bfe10c238b295ba9e52fa8e2d86b257f99ff00bb560e0841b90c7b.jpg").build()
    //         , P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250110/64781da8b57ae8594a94491711c4b90f872f318d4ad89f6bf2bc5550ad168ad2.jpg").build());
    //
    //
    //
    // }

    // A4纸张像素大小 - 降低DPI减少内存占用
    static float DPI = 300;
    static final int PAPER_WIDTH = (int) (210 * DPI / 25.4);  // ≈595像素
    static final int PAPER_HEIGHT = (int) (297 * DPI / 25.4); // ≈842像素


    /**
     * 主方法：合并图片并上传，返回文件URL列表
     */
    public static List<String> combineAndUploadImages(List<String> imageUrls, int groupSize) {
        if (CollUtil.isEmpty(imageUrls)) {
            return new ArrayList<>();
        }

        List<String> fileUrls = new ArrayList<>();

        // 直接按groupSize分组处理，循环合并上传后删除
        for (int i = 0; i < imageUrls.size(); i += groupSize) {
            int end = Math.min(i + groupSize, imageUrls.size());
            List<String> groupUrls = imageUrls.subList(i, end);

            // 合并当前组的图片
            File combinedFile = combineImageGroup(groupUrls, groupSize, i / groupSize);

            if (combinedFile != null) {
                try {
                    // 立即上传并获取URL
                    String fileUrl = FileApiUtil.createFileNoRecord(FileUtil.readBytes(combinedFile));
                    fileUrls.add(fileUrl);
                } catch (Exception e) {
                    log.error("上传合并文件失败: {}", combinedFile.getName(), e);
                } finally {
                    // 立即删除临时文件
                    FileUtil.del(combinedFile);
                }
            }
        }

        return fileUrls;
    }

    /**
     * 简化版本：合并单个图片组
     */
    public static File combineImageGroup(List<String> imageUrls, int groupSize, int groupIndex) {
        if (CollUtil.isEmpty(imageUrls)) {
            return null;
        }

        // 智能计算最优输出尺寸
        int[] optimalSize = calculateOptimalSize(imageUrls, groupSize);
        int paperWidth = optimalSize[0];
        int paperHeight = optimalSize[1];

        // 计算子图尺寸
        int subWidth, subHeight;
        if (groupSize == 2) {
            subWidth = paperWidth / 2;
            subHeight = paperHeight;
        } else {
            subWidth = paperWidth / 2;
            subHeight = paperHeight / 2;
        }

        BufferedImage paperImage = null;
        Graphics2D graphics = null;
        List<BufferedImage> tempImages = new ArrayList<>();

        try {
            // 创建画布
            paperImage = createPaperImageOptimized(paperWidth, paperHeight);
            graphics = paperImage.createGraphics();
            setupGraphicsForPerformance(graphics);

            // 处理每个子图片
            for (int i = 0; i < imageUrls.size(); i++) {
                String imageUrl = imageUrls.get(i);
                BufferedImage subImage = null;

                try {
                    subImage = processSingleImageOptimized(imageUrl, subWidth, subHeight);
                    if (subImage != null) {
                        tempImages.add(subImage);

                        // 计算坐标
                        int x, y;
                        if (groupSize == 2) {
                            x = i * subWidth;
                            y = 0;
                        } else {
                            x = (i % 2) * subWidth;
                            y = (i / 2) * subHeight;
                        }

                        graphics.drawImage(subImage, x, y, null);
                    }
                } catch (Exception e) {
                    log.warn("处理图片失败，跳过: {}", imageUrl, e);
                }
            }

            // 创建输出文件 - 使用JPG格式减少文件大小
            File targetFile = new File("/tmp/" + IdUtil.simpleUUID() + "_group_" + groupIndex + ".jpg");
            writeCompressedJpg(paperImage, targetFile, 0.85f); // 85%质量

            return targetFile;

        } catch (Exception e) {
            log.error("合并图片组失败", e);
            return null;
        } finally {
            // 立即释放所有图像资源
            if (graphics != null) {
                graphics.dispose();
            }
            if (paperImage != null) {
                paperImage.flush();
            }
            tempImages.forEach(img -> {
                if (img != null) {
                    img.flush();
                }
            });
        }
    }

    /**
     * 设置高性能图形参数
     */
    private static void setupGraphicsForPerformance(Graphics2D graphics) {
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_OFF);
        graphics.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_SPEED);
        graphics.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING, RenderingHints.VALUE_COLOR_RENDER_SPEED);
        graphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_NEAREST_NEIGHBOR);
    }

    /**
     * 创建优化的画布
     */
    private static BufferedImage createPaperImageOptimized(int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        try {
            g.setColor(Color.WHITE);
            g.fillRect(0, 0, width, height);
        } finally {
            g.dispose();
        }
        return image;
    }

    /**
     * 优化版本：处理单个图片
     */
    private static BufferedImage processSingleImageOptimized(String imageUrl, int targetWidth, int targetHeight) {
        BufferedImage original = null;
        BufferedImage scaled = null;
        BufferedImage targetImage = null;
        Graphics2D gScale = null;
        Graphics2D g = null;

        try {
            // 下载图片
            byte[] bytes = HttpUtil.downloadBytes(imageUrl);
            original = ImgUtil.toImage(bytes);

            // 限制原图尺寸，避免超大图片
            if (original.getWidth() > 2000 || original.getHeight() > 2000) {
                log.warn("图片尺寸过大，进行预缩放: {}x{}", original.getWidth(), original.getHeight());
                BufferedImage preScaled = scaleImageFast(original, original.getWidth() / 2, original.getHeight() / 2);
                original.flush();
                original = preScaled;
            }

            // 计算缩放比例
            double ratio = Math.min(
                (double) targetWidth / original.getWidth(),
                (double) targetHeight / original.getHeight()
            );

            // 快速缩放
            int scaledWidth = (int) (original.getWidth() * ratio);
            int scaledHeight = (int) (original.getHeight() * ratio);
            scaled = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB);

            gScale = scaled.createGraphics();
            setupGraphicsForPerformance(gScale);
            gScale.drawImage(original, 0, 0, scaledWidth, scaledHeight, null);

            // 创建目标画布
            targetImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
            g = targetImage.createGraphics();
            g.setColor(Color.WHITE);
            g.fillRect(0, 0, targetWidth, targetHeight);

            // 居中绘制
            int x = (targetWidth - scaled.getWidth()) / 2;
            int y = (targetHeight - scaled.getHeight()) / 2;
            g.drawImage(scaled, x, y, null);

            return targetImage;

        } finally {
            // 立即释放资源
            if (gScale != null) {
                gScale.dispose();
            }
            if (g != null) {
                g.dispose();
            }
            if (original != null) {
                original.flush();
            }
            if (scaled != null) {
                scaled.flush();
            }
        }
    }

    /**
     * 快速缩放图片
     */
    private static BufferedImage scaleImageFast(BufferedImage original, int maxWidth, int maxHeight) {
        double ratio = Math.min((double) maxWidth / original.getWidth(), (double) maxHeight / original.getHeight());
        int newWidth = (int) (original.getWidth() * ratio);
        int newHeight = (int) (original.getHeight() * ratio);

        BufferedImage scaled = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = scaled.createGraphics();
        try {
            setupGraphicsForPerformance(g);
            g.drawImage(original, 0, 0, newWidth, newHeight, null);
        } finally {
            g.dispose();
        }
        return scaled;
    }

    /**
     * 写入压缩的JPG文件
     */
    private static void writeCompressedJpg(BufferedImage image, File outputFile, float quality) throws IOException {
        ImageWriter writer = ImageIO.getImageWritersByFormatName("jpg").next();
        ImageWriteParam param = writer.getDefaultWriteParam();

        // 设置压缩参数
        param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        param.setCompressionQuality(quality); // 质量控制 0.0-1.0

        try (FileOutputStream fos = new FileOutputStream(outputFile);
            ImageOutputStream ios = ImageIO.createImageOutputStream(fos)) {

            writer.setOutput(ios);
            writer.write(null, new IIOImage(image, null, null), param);

        } finally {
            writer.dispose();
        }
    }

    /**
     * 智能计算最优输出尺寸，避免不必要的放大
     */
    private static int[] calculateOptimalSize(List<String> imageUrls, int groupSize) {
        try {
            // 下载第一张图片获取基准尺寸
            if (!imageUrls.isEmpty()) {
                byte[] bytes = HttpUtil.downloadBytes(imageUrls.get(0));
                BufferedImage sampleImage = ImgUtil.toImage(bytes);

                int originalWidth = sampleImage.getWidth();
                int originalHeight = sampleImage.getHeight();
                sampleImage.flush();

                // 计算合适的输出尺寸
                int targetWidth, targetHeight;
                if (groupSize == 2) {
                    // 横向排列：宽度翻倍
                    targetWidth = Math.min(originalWidth * 2, PAPER_WIDTH);
                    targetHeight = Math.min(originalHeight, PAPER_HEIGHT);
                } else {
                    // 田字格：宽高都翻倍
                    targetWidth = Math.min(originalWidth * 2, PAPER_WIDTH);
                    targetHeight = Math.min(originalHeight * 2, PAPER_HEIGHT);
                }

                return new int[]{targetWidth, targetHeight};
            }
        } catch (Exception e) {
            log.warn("计算最优尺寸失败，使用默认尺寸", e);
        }

        // 默认尺寸
        if (groupSize == 2) {
            return new int[]{PAPER_HEIGHT, PAPER_WIDTH}; // 横向
        } else {
            return new int[]{PAPER_WIDTH, PAPER_HEIGHT}; // 纵向
        }
    }
}