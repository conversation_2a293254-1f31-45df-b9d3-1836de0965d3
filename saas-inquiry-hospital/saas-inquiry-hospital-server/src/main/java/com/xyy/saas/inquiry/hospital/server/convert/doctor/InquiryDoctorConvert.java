package com.xyy.saas.inquiry.hospital.server.convert.doctor;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.CooperationStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorTitleEnum;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorCardInfoDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorPracticeDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryHospitalDeptDoctorDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorWorkRecordRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorWorkRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryHospitalDoctorRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryHospitalDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorRelationRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorAuditedRecordDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorBillingDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorFilingDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorPracticeDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorWorkRecordDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryHospitalDeptDoctorDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.dto.DoctorForwardDetailDto;
import com.xyy.saas.inquiry.pojo.forward.ForwardCaInfo;
import com.xyy.saas.inquiry.pojo.forward.ForwardPersonInfo;
import com.xyy.saas.inquiry.signature.api.ca.dto.SyncCreateCaDto;
import com.xyy.saas.inquiry.util.PrefUtil;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper
public interface InquiryDoctorConvert {

    InquiryDoctorConvert INSTANCE = Mappers.getMapper(InquiryDoctorConvert.class);

    @Mapping(target = "nickname", source = "createReqVO.name")
    AdminUserSaveDTO convertUser(InquiryDoctorSaveReqVO createReqVO);

    default InquiryDoctorDO initConvertVO2DO(InquiryDoctorSaveReqVO createReqVO, InquiryDoctorDO inquiryDoctor) {
        InquiryDoctorDO doctorDO = convert(createReqVO);
        doctorDO.setPref(ObjectUtil.isEmpty(inquiryDoctor) ? PrefUtil.getDoctorPref() : inquiryDoctor.getPref());
        doctorDO.setOnlineStatus(ObjectUtil.isEmpty(inquiryDoctor) ? OnlineStatusEnum.OFFLINE.getCode() : inquiryDoctor.getOnlineStatus());
        return doctorDO;
    }

    ;

    InquiryDoctorDO convert(InquiryDoctorSaveReqVO createReqVO);

    DoctorPracticeDO convertPractice(InquiryDoctorSaveReqVO createReqVO);

    InquiryDoctorPracticeDto convertDo2Dto(DoctorPracticeDO practiceDO);

    DoctorFilingDO convertFiling(InquiryDoctorSaveReqVO createReqVO);

    DoctorBillingDO convertBilling(InquiryDoctorSaveReqVO createReqVO);

    List<DoctorWorkRecordDO> convertWorkRecords(List<DoctorWorkRecordSaveReqVO> jobItems);

    List<InquiryHospitalDeptDoctorDO> convertHospitalDoctors(List<InquiryHospitalDoctorSaveReqVO> hospitalDoctorItems);

    @Mapping(target = "fillingStatus4ShaanxiRegulatory", source = "ext.fillingStatus4ShaanxiRegulatory")
    InquiryDoctorDetailRespVO convertToVo(InquiryDoctorDO inquiryDoctor, @MappingTarget InquiryDoctorDetailRespVO result);

    default InquiryDoctorCardInfoDto convertToCardInfo(InquiryDoctorDO inquiryDoctor, DoctorPracticeDO doctorPracticeDO) {
        // 填充基本信息
        InquiryDoctorDetailRespVO vo = convertToVo(inquiryDoctor, new InquiryDoctorDetailRespVO());
        // 填充执业信息
        convertPracticeWithEvaluate(doctorPracticeDO, vo);
        // 转换返回
        return convertDetailVO2CardDTO(vo);
    }

    InquiryDoctorCardInfoDto convertDetailVO2CardDTO(InquiryDoctorDetailRespVO vo);

    default InquiryDoctorDetailRespVO convertPracticeWithEvaluate(DoctorPracticeDO doctorPracticeDO, InquiryDoctorDetailRespVO result) {
        InquiryDoctorDetailRespVO respVO = convertPracticeToVo(doctorPracticeDO, result);
        // 从业时间根据医生开始执业时间算到目前多少年，如果开始执业时间为空，则依据当前医生id的哈希值取模2，奇数给9，偶数给10
        respVO.setPracticeTime(ObjectUtil.isNull(doctorPracticeDO.getStartPracticeTime()) ? doctorPracticeDO.getDoctorId().hashCode() % 2 == 0 ? 9 : 10 : LocalDateTime.now().getYear() - doctorPracticeDO.getStartPracticeTime().getYear());
        respVO.setReceptionNum("9999+");
        respVO.setGoodCommentRate(doctorPracticeDO.getDoctorId().hashCode() % 2 == 0 ? "98" : "99");
        // 填充职称名称
        Optional.ofNullable(result.getTitleCode()).ifPresent(professionId -> result.setTitleName(DoctorTitleEnum.fromCode(professionId).getDesc()));
        return respVO;
    }

    @Mapping(target = "id", ignore = true)
    InquiryDoctorDetailRespVO convertPracticeToVo(DoctorPracticeDO doctorPracticeDO, @MappingTarget InquiryDoctorDetailRespVO result);

    @Mapping(target = "id", ignore = true)
    InquiryDoctorDetailRespVO convertFilingToVo(DoctorFilingDO doctorFilingDO, @MappingTarget InquiryDoctorDetailRespVO result);

    @Mapping(target = "id", ignore = true)
    InquiryDoctorDetailRespVO convertBillingToVo(DoctorBillingDO doctorBillingDO, @MappingTarget InquiryDoctorDetailRespVO result);

    @Mapping(target = "id", ignore = true)
    InquiryDoctorDetailRespVO convertWorkRecordToVo(DoctorWorkRecordDO doctorWorkRecordDO, @MappingTarget InquiryDoctorDetailRespVO result);

    List<DoctorWorkRecordRespVO> convertWorkRecordsToVo(List<DoctorWorkRecordDO> doctorWorkRecordDOList);

    List<InquiryHospitalDoctorRespVO> convertHospitalDoctorsToVo(List<InquiryHospitalDeptDoctorDO> hospitalDoctorDeptDOList);

    List<DoctorAuditedRecordRespVO> convertAuditedRecordsToVo(List<DoctorAuditedRecordDO> collect);

    DoctorAuditedRecordDO convertAuditRecordVoToDo(DoctorAuditedRecordSaveReqVO auditReqVO);


    List<InquiryDoctorDto> convertToDtos(List<InquiryDoctorDO> inquiryDoctors);


    InquiryDoctorDto convertToDto(InquiryDoctorDO inquiryDoctor);

    InquiryHospitalDeptDoctorDto convertDoToDto(InquiryHospitalDeptDoctorDO inquiryDoctor);

    default List<InquiryDoctorRespVO> convertDOsToVOs(List<InquiryDoctorDO> resultDos, List<InquiryHospitalDeptDoctorDO> deptDoctorDOS) {
        List<InquiryDoctorRespVO> respVOList = convertDOListToVOList(resultDos);
        Map<String, List<InquiryHospitalDeptDoctorDO>> autoInquiryMap = deptDoctorDOS.stream().collect(Collectors.groupingBy(InquiryHospitalDeptDoctorDO::getDoctorPref));
        respVOList.forEach(item -> {
                List<InquiryHospitalDeptDoctorDO> deptDoctorList = autoInquiryMap.get(item.getPref());
                item.setInquiryWayType(deptDoctorList.stream().filter(doctor -> doctor.getInquiryType().equals(DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode())).map(InquiryHospitalDeptDoctorDO::getInquiryWayType).distinct().toList());
                item.setAutoInquiryWayType(deptDoctorList.stream().filter(doctor -> doctor.getInquiryType().equals(DoctorInquiryTypeEnum.AUTO_INQUIRY.getCode())).map(InquiryHospitalDeptDoctorDO::getInquiryWayType).distinct().toList());
            }
        );
        return respVOList;
    }

    List<InquiryDoctorRespVO> convertDOListToVOList(List<InquiryDoctorDO> resultDos);


    default InquiryDoctorDetailRespVO convertSync(DoctorForwardDetailDto dto) {
        InquiryDoctorDetailRespVO vo = new InquiryDoctorDetailRespVO();
        vo.setName(dto.getDocName());
        vo.setSex(dto.getSex());
        vo.setIdCard(dto.getIdCard());
        vo.setMobile(dto.getDocTel());
        vo.setFirstPracticeDeptName(dto.getDocDeptName()); // 科室
        vo.setAuditStatus(AuditStatusEnum.APPROVED.getCode());
        vo.setEnvTag(EnvTagEnum.PROD.getEnv());
        vo.setJobType(Objects.equals(dto.getDocJobType(), 1) ? 1 : 2);    // 医生类型 1全职  2兼职
        // 合作状态 1合作 0取消
        vo.setCooperation(Objects.equals(dto.getCooperationStatus(), (byte) 1) ? CooperationStatusEnum.IN_COOPERATION.getCode() : CooperationStatusEnum.UNCOOPERATED.getCode());
        vo.setProfessionalDec(dto.getProfessional()); // 擅长
        vo.setBiography(dto.getDocComment()); // 简介
        vo.setFirstPracticeName(dto.getWorkInstName()); // 第一机构
        vo.setTitleCode(NumberUtils.toInt(dto.getTitleCode(), 1));
        // 证照信息
        vo.setPhoto(dto.getHeadPortrait()); // 头像
        vo.setVerifiyImgUrl(dto.getEmployeeCard()); // 查证结果
        vo.setTitleImgUrl(dto.getTitleCertList()); // 职称证
        vo.setPersonalImgUrl(dto.getBadgeList()); // 胸牌

        if (StringUtils.isNotBlank(dto.getCertDocPracList())) {
            vo.setOccupationImgUrls(List.of(StringUtils.split(dto.getCertDocPracList(), ","))); // 执业证
        }
        if (StringUtils.isNotBlank(dto.getDocCertList())) {
            vo.setQualificationImgUrls(List.of(StringUtils.split(dto.getDocCertList(), ","))); // 资格证
        }

        vo.setIdCardFrontImgUrl(dto.getIdCardFront()); // 身份证正面
        vo.setIdCardReverseImgUrl(dto.getIdCardBack()); // 身份证反面
        vo.setNationCode(NumberUtils.toInt(dto.getNationCode(), 1)); // 民族
        vo.setOrgProvinceCode(dto.getProvinceCode()); // 省份
        vo.setAddress(dto.getDocAddress()); //  通信地址
        vo.setCanal(convertChannel(dto.getSignChannel())); // 渠道
        vo.setDoctorMedicareNo(dto.getMedicareCode()); // 医保编码

        // 个人执业信息
        Optional.ofNullable(dto.getDocMultiSitedDateStart()).ifPresent(d -> vo.setStartPracticeTime(d.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())); // 执业开始
        Optional.ofNullable(dto.getDocMultiSitedDateEnd()).ifPresent(d -> vo.setEndPracticeDate(d.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())); // 执业结束
        vo.setProfessionalNo(dto.getPracNo()); // 执业证书号
        Optional.ofNullable(dto.getPracRecDate()).ifPresent(d -> vo.setProfessionalTime(d.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())); // 执业证书取得时间
        vo.setQualificationNo(dto.getCertNo()); // 资格证号
        Optional.ofNullable(dto.getCertRecDate()).ifPresent(d -> vo.setQualificationTime(d.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())); // 资格证书取得时间
        vo.setTitleNo(dto.getTitleNo()); // 职称证号
        Optional.ofNullable(dto.getTitleRecDate()).ifPresent(date -> vo.setTitleTime(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())); // 职称证取得时间

        // 收款信息
        vo.setPayeeName(dto.getBankUser());
        vo.setPayeeIdCard(dto.getBankIdCard());
        vo.setPayeeBankName(dto.getBankName());
        vo.setPayeeBankNo(dto.getBankCode());
        vo.setPayeeTelPhone(dto.getBankTel());

        return vo;
    }

    // 手动转换医生渠道字典
    @Named("convertChannelManual") // 给这个方法一个命名限定
    default Integer convertChannel(String signChannel) {
        if (StringUtils.equals(signChannel, "1")) {
            return 7;
        }
        if (StringUtils.equals(signChannel, "2")) {
            return 6;
        }
        if (StringUtils.equals(signChannel, "4")) {
            return 4;
        }
        if (StringUtils.equals(signChannel, "0")) {
            return 5;
        }
        if (StringUtils.equals(signChannel, "31")) {
            return 1;
        }
        if (StringUtils.equals(signChannel, "33")) {
            return 3;
        }
        if (StringUtils.equals(signChannel, "99")) {
            return 9;
        }
        return 8;
    }

    default SyncCreateCaDto convertDoctorCa(InquiryDoctorDO inquiryDoctor, ForwardCaInfo caInfo, ForwardPersonInfo personInfo) {
        return SyncCreateCaDto.builder()
            .userId(inquiryDoctor.getUserId())
            .name(inquiryDoctor.getName())
            .mobile(inquiryDoctor.getMobile())
            .idCard(inquiryDoctor.getIdCard())
            .personInfo(personInfo)
            .caInfo(caInfo)
            .build();
    }

    List<InquiryHospitalDeptDoctorRelationRespVO> convertDoctorDeptList(List<InquiryHospitalDeptDoctorDO> uniqueList);
}
