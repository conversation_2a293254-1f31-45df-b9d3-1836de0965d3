package com.xyy.saas.inquiry.hospital.server.convert.doctor;

import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorStatusDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryHospitalDeptDoctorDO;
import java.util.ArrayList;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: cxy
 */
@Mapper
public interface InquiryDoctorStatusConvert {

    InquiryDoctorStatusConvert INSTANCE = Mappers.getMapper(InquiryDoctorStatusConvert.class);

    default List<InquiryDoctorStatusDO> convertDeptRelation(List<InquiryHospitalDeptDoctorDO> onLines, OnlineStatusEnum onlineStatusEnum) {
        return onLines.stream().map(d -> InquiryDoctorStatusDO.builder()
                .doctorPref(d.getDoctorPref())
                .inquiryType(d.getInquiryType())
                .inquiryWayType(d.getInquiryWayType())
                .status(onlineStatusEnum.getCode()).build())
            .toList();
    }

    default List<InquiryDoctorStatusDO> convertSaveReqVOTODOS(InquiryDoctorStatusSaveReqVO reqVO, InquiryDoctorDO doctorDO) {
        List<InquiryDoctorStatusDO> result = new ArrayList<>();
        reqVO.getInquiryWayTypeItems().forEach(way -> {
            result.add(InquiryDoctorStatusDO.builder()
                .status(OnlineStatusEnum.ONLINE.getCode())
                .doctorPref(doctorDO.getPref())
                .inquiryType(DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode())
                .inquiryWayType(way)
                .build()
            );
        });
        return result;
    }

    default InquiryDoctorStatusRespVO convertDOSTOVO(List<InquiryDoctorStatusDO> doctorStatusDOList, InquiryDoctorDO doctorDO, List<DictDataRespDTO> inquiryWayTypeDicts) {
        return InquiryDoctorStatusRespVO.builder()
            .userId(doctorDO.getUserId())
            .inquiryWayTypeDicts(inquiryWayTypeDicts)
            .onlineStatus(doctorDO.getOnlineStatus())
            .inquiryWayTypeItems(doctorStatusDOList.stream().map(InquiryDoctorStatusDO::getInquiryWayType).distinct().toList())
            .autoGrabStatus(doctorDO.getAutoGrabStatus())
            .build();
    }
}
