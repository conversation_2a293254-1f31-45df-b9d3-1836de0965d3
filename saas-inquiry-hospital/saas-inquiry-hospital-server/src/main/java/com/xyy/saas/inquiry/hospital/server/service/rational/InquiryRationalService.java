package com.xyy.saas.inquiry.hospital.server.service.rational;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryLimitAuditReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryRationalDictConfigPageVo;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryRationalDictConfigVo;
import com.xyy.saas.inquiry.pojo.RationalTipsVO;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/20 13:55
 */
public interface InquiryRationalService {

    /**
     * 获取默认过敏列表
     *
     * @return 过敏列表
     */
    CommonResult<List<InquiryRationalDictConfigVo>> getDefaultAllergy();

    /**
     * 获取默认过敏列表
     *
     * @return 过敏列表
     */
    CommonResult<List<InquiryRationalDictConfigVo>> getRecommendAllergy();

    /**
     * 获取过敏列表
     *
     * @param pageVO 请求入参
     * @return 分页过敏列表
     */
    CommonResult<PageResult<InquiryRationalDictConfigVo>> queryAllergyList(InquiryRationalDictConfigPageVo pageVO);

    /**
     * 合理用药审核接口
     *
     * @param reqVO 请求入参
     * @return 合理用药提醒
     */
    CommonResult<List<RationalTipsVO>> limitDrugAudit(InquiryLimitAuditReqVO reqVO);
}
