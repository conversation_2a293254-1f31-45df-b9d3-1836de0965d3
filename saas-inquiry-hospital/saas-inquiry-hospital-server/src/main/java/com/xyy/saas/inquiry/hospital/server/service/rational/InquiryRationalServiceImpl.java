package com.xyy.saas.inquiry.hospital.server.service.rational;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.common.api.rational.ProductDiagnosisRelationApi;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.common.GrayStrategyEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.hospital.server.config.foward.InquiryRationalForwardClient;
import com.xyy.saas.inquiry.hospital.server.config.rational.RationalConfig;
import com.xyy.saas.inquiry.hospital.server.constant.LimitDrugAuditTypeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryLimitAuditReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryRationalDictConfigPageVo;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryRationalDictConfigVo;
import com.xyy.saas.inquiry.hospital.server.convert.rational.InquiryRationalConvert;
import com.xyy.saas.inquiry.hospital.server.service.rational.dto.DrugForwardDto;
import com.xyy.saas.inquiry.hospital.server.service.rational.dto.InquiryLimitAuditForwardDto;
import com.xyy.saas.inquiry.hospital.server.service.rational.dto.InquiryRationalDictConfigPageDto;
import com.xyy.saas.inquiry.pojo.ForwardResult;
import com.xyy.saas.inquiry.pojo.GrayConfig;
import com.xyy.saas.inquiry.pojo.GrayMatchContext;
import com.xyy.saas.inquiry.pojo.RationalTipsVO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.forward.ForwardPageResultDto;
import com.xyy.saas.inquiry.product.api.ProductStdlibApi;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/20 13:55
 */
@Slf4j
@Service
public class InquiryRationalServiceImpl implements InquiryRationalService {

    @Resource
    private InquiryRationalForwardClient inquiryRationalForwardClient;

    @DubboReference
    private ProductDiagnosisRelationApi productDiagnosisRelationApi;

    @DubboReference
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    @DubboReference
    private TenantApi tenantApi;

    @DubboReference
    private PermissionApi permissionApi;

    @DubboReference
    private ProductStdlibApi productStdlibApi;

    @Autowired
    private RationalConfig rationalConfig;

    @Override
    public CommonResult<List<InquiryRationalDictConfigVo>> getDefaultAllergy() {
        ForwardResult<List<InquiryRationalDictConfigVo>> forwardResult = inquiryRationalForwardClient.getDefaultAllergy();
        return convertForwardResult(forwardResult);
    }

    @Override
    public CommonResult<List<InquiryRationalDictConfigVo>> getRecommendAllergy() {
        ForwardResult<List<InquiryRationalDictConfigVo>> forwardResult = inquiryRationalForwardClient.getRecommendAllergy();
        return convertForwardResult(forwardResult);
    }

    @Override
    public CommonResult<PageResult<InquiryRationalDictConfigVo>> queryAllergyList(InquiryRationalDictConfigPageVo pageVO) {
        InquiryRationalDictConfigPageDto dto = InquiryRationalConvert.INSTANCE.convertDictConfigPage(pageVO);
        ForwardResult<ForwardPageResultDto<InquiryRationalDictConfigVo>> forwardResult = inquiryRationalForwardClient.queryAllergyList(dto);
        if (forwardResult.isSuccess()) {
            return CommonResult.success(new PageResult(forwardResult.getResult().getList(), forwardResult.getResult().getTotal()));
        }
        return CommonResult.error(forwardResult.getMsg());
    }

    // 走转发老服务实现
    public CommonResult<List<RationalTipsVO>> limitDrugAuditForward(InquiryLimitAuditReqVO reqVO) {

        InquiryLimitAuditForwardDto limitAuditForwardDto = InquiryRationalConvert.INSTANCE.convertLimitDrugAudit(reqVO);

        List<Integer> limitDrugAuditCodeList = Lists.newArrayList();

        // 判断是医生端还是商家端
        // 医生端
        if (permissionApi.hasAnyRoles(SecurityFrameworkUtils.getLoginUserId(), RoleCodeEnum.DOCTOR.getCode())) {

            if (reqVO.getTenantId() == null) {
                log.warn("limitDrugAudit warn : {}", reqVO);
                return CommonResult.success(Lists.newArrayList());
            }

            // 商家端 , 用法用量审查默认都开启
        } else {
            limitDrugAuditCodeList.add(LimitDrugAuditTypeEnum.USAGE_AND_DOSAGE_REVIEW.getCode());

        }

        // 获取门店信息
        TenantDto tenantDto = tenantApi.getTenant(Optional.ofNullable(reqVO.getTenantId()).orElse(TenantContextHolder.getRequiredTenantId()));

        // 查询超适应配置--顺序为  门店  ->  区域
        InquiryOptionConfigRespDto config = inquiryOptionConfigApi.getInquiryOptionConfig(tenantDto, InquiryOptionTypeEnum.FORM_OFF_LABEL_REVIEW);
        // config 不为空且超适应症审查为ture
        if (config != null && ObjectUtil.equal(config.getFormOffLabelReview(), Boolean.TRUE)) {
            limitDrugAuditCodeList.add(LimitDrugAuditTypeEnum.INDICATION_REVIEW.getCode());
        }
        // 设置 审查类型
        limitAuditForwardDto.setLimitDrugAuditCodeList(limitDrugAuditCodeList);
        // 转换商品id为中台标准库id
        this.transitionDrugPrefToStandardId(limitAuditForwardDto);
        ForwardResult<List<RationalTipsVO>> forwardResult = inquiryRationalForwardClient.limitDrugAudit(limitAuditForwardDto);
        return convertForwardResult(forwardResult);
    }


    @Override
    public CommonResult<List<RationalTipsVO>> limitDrugAudit(InquiryLimitAuditReqVO reqVO) {
        GrayConfig config = rationalConfig.getGrayConfig();
        if(ObjectUtil.isEmpty( config)){
            log.info("因未配置灰度，当前租户：{} ，合理用药走老系统", TenantContextHolder.getTenantId());
        }
        if(GrayStrategyEnum.isGrayTenant(config.getGrayStrategy(), GrayMatchContext.builder().tenantId(TenantContextHolder.getTenantId()).grayValue(config.getValue()).build())){
            log.info("当前租户：{} 合理用药走新系统", TenantContextHolder.getTenantId());
            // 走新系统
            return limitDrugAuditNewSystem(reqVO);
        }
        //当前门店是否属于灰度门店
        log.info("当前租户：{} 不满足灰度规则，走老系统",TenantContextHolder.getTenantId());
        // 不满足灰度，走老系统
        return limitDrugAuditForward(reqVO);
    }

    // 走新系统实现
    public CommonResult<List<RationalTipsVO>> limitDrugAuditNewSystem(InquiryLimitAuditReqVO reqVO) {
        return productDiagnosisRelationApi.limitDrugAudit(InquiryRationalConvert.INSTANCE.convertLimitDrugAuditDto(reqVO));
    }

    /**
     * 转换商品id为中台标准库id
     *
     * @param limitAuditForwardDto
     */
    private void transitionDrugPrefToStandardId(InquiryLimitAuditForwardDto limitAuditForwardDto) {

        if (limitAuditForwardDto == null || CollUtil.isEmpty(limitAuditForwardDto.getDrugDtoList())) {
            return;
        }

        List<Long> prefList = limitAuditForwardDto.getDrugDtoList().stream()
            .filter(item -> StringUtils.isNotBlank(item.getPref()) && Convert.toLong(item.getPref()) != null)
            .map(item -> Convert.toLong(item.getPref())).distinct().toList();

        if (CollUtil.isEmpty(prefList)) {
            limitAuditForwardDto.getDrugDtoList().forEach(item -> item.setPref(""));
            return;
        }

        StdlibProductSearchDto searchDto = new StdlibProductSearchDto().setIdList(prefList);
        List<ProductStdlibDto> productStdlibDtoList = productStdlibApi.searchProductStdlibList(searchDto, prefList.size());

        if (CollUtil.isEmpty(productStdlibDtoList)) {
            limitAuditForwardDto.getDrugDtoList().forEach(item -> item.setPref(""));
            return;
        }

        Map<String, String> midStdlibIdMap = productStdlibDtoList.stream()
            .filter(item ->  item != null && item.getId() != null && item.getMidStdlibId() != null)
            .collect(Collectors.toMap(dto -> dto.getId().toString(), dto -> dto.getMidStdlibId().toString(), (a, b) -> b));

        for (DrugForwardDto item : limitAuditForwardDto.getDrugDtoList()) {
            if (StringUtils.isBlank(item.getPref())) {
                continue;
            }
            if (!midStdlibIdMap.containsKey(item.getPref())) {
                item.setPref("");
                continue;
            }
            item.setPref(midStdlibIdMap.get(item.getPref()));
        }

    }

    private static <T> CommonResult<T> convertForwardResult(ForwardResult<T> forwardResult) {
        if (forwardResult.isSuccess()) {
            return CommonResult.success(forwardResult.getResult());
        }
        return CommonResult.error(forwardResult.getMsg());
    }

}
