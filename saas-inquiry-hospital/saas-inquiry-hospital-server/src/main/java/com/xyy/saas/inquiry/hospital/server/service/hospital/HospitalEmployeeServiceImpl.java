package com.xyy.saas.inquiry.hospital.server.service.hospital;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.user.dto.UserPageReqDTO;
import cn.iocoder.yudao.module.system.api.user.dto.UserRespDTO;
import com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalEmployeeRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalUserPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalUserSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalAvailableRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalAvailablePageReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.hospital.HospitalEmployeeConvert;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.HospitalEmployeeDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.HospitalEmployeeMapper;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 医院员工服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class HospitalEmployeeServiceImpl implements HospitalEmployeeService {

    @Resource
    private AdminUserApi adminUserApi;



    @Resource
    private PermissionApi permissionApi;

    @Resource
    private HospitalEmployeeMapper hospitalEmployeeMapper;

    @Resource
    private InquiryHospitalService inquiryHospitalService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createHospitalEmployee(HospitalUserSaveReqVO createReqVO) {
        // 校验医院信息是否存在
        InquiryHospitalRespDto hospital = inquiryHospitalService.getInquiryHospital(createReqVO.getHospitalPref());
        if (hospital == null) {
            throw exception(ErrorCodeConstants.INQUIRY_HOSPITAL_NOT_EXISTS);
        }
        AdminUserRespDTO user = adminUserApi.getUserByUserName(createReqVO.getUsername());
        if(ObjectUtil.isEmpty(user)){
            // 创建用户
            Long userId = adminUserApi.createUser(HospitalEmployeeConvert.INSTANCE.convert2UserSaveDTO(createReqVO));
            user = adminUserApi.getUser(userId);
        }else{
            // 修改用户
            adminUserApi.updateUser(HospitalEmployeeConvert.INSTANCE.convert2UserUpdateDTO(user,createReqVO));
        }
        if(!RoleCodeEnum.isHospitalEmployee(user.getRoleCodes())){
            // 分配医院员工角色
            permissionApi.assignUserRoleWithSystemRoleCode(user.getId(), RoleCodeEnum.HOSPITAL_EMPLOYEE.getCode());
        }
        // 查询当前员工与医院是否已存在绑定关系
        HospitalEmployeeDO hospitalEmployee = hospitalEmployeeMapper.selectByUserIdAndHospitalPref(HospitalEmployeeConvert.INSTANCE.convert2HospitalBindReqVO(hospital,user));
        if(ObjectUtil.isEmpty(hospitalEmployee)){
            hospitalEmployee = HospitalEmployeeConvert.INSTANCE.createEmployeeRelation(hospital, user.getId());
            hospitalEmployeeMapper.insert(hospitalEmployee);
        }
        return user.getId();
    }



    @Override
    public PageResult<HospitalEmployeeRespVO> getHospitalEmployeePage(HospitalUserPageReqVO pageReqVO) {
        // 1. 根据医院编码查询绑定的员工用户ID列表
        List<HospitalEmployeeDO> hospitalEmployees = hospitalEmployeeMapper.selectList(HospitalUserPageReqVO.builder()
            .hospitalPref(pageReqVO.getHospitalPref()).status(CommonStatusEnum.ENABLE.getStatus()).build());
        if (hospitalEmployees.isEmpty()) {
            return PageResult.empty();
        }
        // 获取userID集合
        List<Long> userIds = hospitalEmployees.stream().map(HospitalEmployeeDO::getUserId).collect(Collectors.toList());

        UserPageReqDTO pageReqDTO = HospitalEmployeeConvert.INSTANCE.convert2UserPageReqDTO(pageReqVO,userIds);

        // 2. 调用用户API获取用户信息
        PageResult<UserRespDTO> users = adminUserApi.getUserPageList(pageReqDTO);

        // 3. 使用MapStruct转换为响应VO
        return HospitalEmployeeConvert.INSTANCE.convertToRespVOList(users);
    }


    @Override
    public PageResult<HospitalBindRespVO> getBindHospitalsByUserId(HospitalAvailablePageReqVO pageReqVO) {
        // 1. 根据分页条件查询绑定的医院列表
        PageResult<HospitalEmployeeDO> hospitalEmployeePage = hospitalEmployeeMapper.selectPage(HospitalEmployeeConvert.INSTANCE.convertHospitalUserPageReqVO(pageReqVO));

        // 2. 使用MapStruct转换为响应VO
        return HospitalEmployeeConvert.INSTANCE.convertToBindRespVOPageList(hospitalEmployeePage);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long bindEmployeeToHospital(HospitalBindReqVO bindReqVO) {
        // 校验医院信息是否存在
        InquiryHospitalRespDto hospital = inquiryHospitalService.getInquiryHospital(bindReqVO.getHospitalPref());
        if (hospital == null) {
            throw exception(ErrorCodeConstants.INQUIRY_HOSPITAL_NOT_EXISTS);
        }
        // 检查是否已经绑定
        HospitalEmployeeDO existEmployee = hospitalEmployeeMapper.selectByUserIdAndHospitalPref(bindReqVO);
        if (existEmployee != null) {
            // 支持幂等
            return existEmployee.getId();
        }
        // 获取用户信息
        AdminUserRespDTO user = adminUserApi.getUser(bindReqVO.getUserId());
        // 创建医院员工关系记录，使用从API获取的医院名称
        HospitalEmployeeDO hospitalEmployee = HospitalEmployeeConvert.INSTANCE.createBindEmployeeRelation(user,hospital);
        
        hospitalEmployeeMapper.insert(hospitalEmployee);

        if(!RoleCodeEnum.isHospitalEmployee(user.getRoleCodes())){
            // 分配医院员工角色（如果还没有）
            permissionApi.assignUserRoleWithSystemRoleCode(user.getId(), RoleCodeEnum.HOSPITAL_EMPLOYEE.getCode());
        }
        return hospitalEmployee.getId();
    }

    @Override
    public void unbindEmployeeFromHospital(Long bindId) {
        // 删除医院员工关系记录
        hospitalEmployeeMapper.deleteById(bindId);
    }

    @Override
    public PageResult<HospitalAvailableRespVO> getAvailableHospitalsByUserId(HospitalAvailablePageReqVO pageReqVO) {
        // 1. 查询用户已绑定的医院编码列表
        List<HospitalEmployeeDO> boundHospitals = hospitalEmployeeMapper.selectList(HospitalUserPageReqVO.builder().userId(pageReqVO.getUserId()).status(CommonStatusEnum.ENABLE.getStatus()).build());
        List<String> boundHospitalPrefs = boundHospitals.stream().map(HospitalEmployeeDO::getHospitalPref).collect(Collectors.toList());

        PageResult<InquiryHospitalRespDto>  pageResult = inquiryHospitalService.getInquiryHospitalPage(HospitalEmployeeConvert.INSTANCE.convertHospitalPageReqVO(pageReqVO,boundHospitalPrefs));

        // 3. 使用MapStruct转换为响应VO
        return HospitalEmployeeConvert.INSTANCE.convertHospitalPageRespVO(pageResult);
    }

    /**
     * 获取用户已绑定的医院列表
     *
     * @param userId 用户编号
     * @return
     */
    @Override
    public List<HospitalBindRespVO> getBindHospitalsByUserId(Long userId) {
        List<HospitalEmployeeDO> boundHospitals = hospitalEmployeeMapper.selectList(HospitalUserPageReqVO.builder().userId(userId).status(CommonStatusEnum.ENABLE.getStatus()).build());
        return HospitalEmployeeConvert.INSTANCE.convertToBindRespVOList(boundHospitals);
    }

}