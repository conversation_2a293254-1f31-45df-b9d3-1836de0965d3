package com.xyy.saas.inquiry.hospital.server.service.hospital;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_CA_NOT_SET_AUTO;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_NOT_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_ONLINE_STATUS_ERROR;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_HOSPITAL_DEPARTMENT_RELATION_DOCTOR_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_HOSPITAL_DEPARTMENT_RELATION_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.spring.SpringUtils;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryHospitalDeptDoctorDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDisableSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorConfigRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorGroupVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorRelationRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorRemoveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.doctor.InquiryDoctorConvert;
import com.xyy.saas.inquiry.hospital.server.convert.hospital.InquiryHospitalDepartmentRelationConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryHospitalDeptDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentRelationDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryHospitalDeptDoctorMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalDepartmentRelationMapper;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorStatusService;
import com.xyy.saas.inquiry.mq.doctor.DoctorAutoInquirySwitchProducer;
import com.xyy.saas.inquiry.mq.doctor.DoctorAutoInquiryTimerWheelEvent;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelDto;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelMessage;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import jakarta.annotation.Resource;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;

/**
 * @ClassName：InquiryHospitalDetpDoctorRelationServiceImpl
 * @Author: xucao
 * @Date: 2024/11/18 10:58
 * @Description: 医院科室医生关联信息Service实现类
 * @Version: 1.0
 */
@Service
@Validated
public class InquiryHospitalDetpDoctorRelationServiceImpl implements InquiryHospitalDetpDoctorRelationService {

    @Resource
    private InquiryHospitalDepartmentRelationMapper inquiryHospitalDepartmentRelationMapper;

    @Resource
    private InquiryHospitalDeptDoctorMapper inquiryHospitalDeptDoctorMapper;

    @Resource
    private InquiryDoctorMapper inquiryDoctorMapper;

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Resource
    private InquiryDoctorStatusService inquiryDoctorStatusService;

    @Resource
    private DoctorAutoInquirySwitchProducer doctorAutoInquirySwitchProducer;

    @DubboReference
    private InquirySignatureCaAuthApi inquirySignatureCaAuthApi;


    public InquiryHospitalDetpDoctorRelationServiceImpl getSelf() {
        return SpringUtils.getBean(InquiryHospitalDetpDoctorRelationServiceImpl.class);
    }

    @Override
    public PageResult<InquiryHospitalDeptDoctorGroupVO> getInquiryHospitalDeptPage(InquiryHospitalDepartmentRelationPageReqVO pageReqVO) {
        IPage<InquiryHospitalDeptDoctorGroupVO> page = TenantUtils.executeIgnore(() -> inquiryHospitalDepartmentRelationMapper.selectHospitalDeptPage(MyBatisUtils.buildPage(pageReqVO), pageReqVO));
        if (page == null || page.getTotal() == 0) {
            return PageResult.empty();
        }
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 获取医院科室下医生列表
     */
    @Override
    public List<InquiryDoctorRespVO> getInquiryHospitalDeptDoctorList(InquiryHospitalDepartmentRelationPageReqVO reqVO) {
        if (StringUtils.isNotBlank(reqVO.getDoctorName()) || StringUtils.isNotBlank(reqVO.getMobile())) {
            final List<InquiryDoctorDO> doctorDOS = inquiryDoctorMapper.selectList(InquiryDoctorPageReqVO.builder().name(reqVO.getDoctorName()).mobile(reqVO.getMobile()).build());
            if (CollUtil.isEmpty(doctorDOS)) {
                return Collections.emptyList();
            }
            reqVO.setDoctorPrefs(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(doctorDOS, InquiryDoctorDO::getPref));
        }

        // 查询医院科室下医生配置信息
        List<InquiryHospitalDeptDoctorDO> list = inquiryHospitalDeptDoctorMapper.selectList(reqVO);
        if (CollectionUtils.isEmpty(list)) {
            return List.of();
        }
        // 查询医生基础信息
        List<InquiryDoctorDO> resultDos = inquiryDoctorMapper.selectList(InquiryDoctorPageReqVO.builder().doctorPrefs(list.stream().map(InquiryHospitalDeptDoctorDO::getDoctorPref).distinct().toList()).build());
        return InquiryDoctorConvert.INSTANCE.convertDOsToVOs(resultDos, list);
    }

    /**
     * 医院科室下新增医生
     */
    @Override
    public Boolean addRecord(InquiryHospitalDeptDoctorSaveReqVO saveReqVO) {
        // 根据id查询信息
        InquiryHospitalDepartmentRelationDO hospitalDeptRelationDO = inquiryHospitalDepartmentRelationMapper.selectById(saveReqVO.getHospitalDeptRelationId());
        if (ObjectUtils.isEmpty(hospitalDeptRelationDO)) {
            throw exception(INQUIRY_HOSPITAL_DEPARTMENT_RELATION_NOT_EXISTS);
        }

        // 查询医生信息
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getPref, saveReqVO.getDoctorPref());
        Assert.notNull(doctorDO, INQUIRY_DOCTOR_NOT_EXISTS.getMsg());

        // 校验医生科室存在
        List<InquiryHospitalDeptDoctorDO> doctorDOS = inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDepartmentRelationPageReqVO.builder()
            .hospitalDeptRelationId(saveReqVO.getHospitalDeptRelationId()).doctorPref(doctorDO.getPref()).build());
        if (CollUtil.isNotEmpty(doctorDOS)) {
            throw exception(INQUIRY_HOSPITAL_DEPARTMENT_RELATION_DOCTOR_EXISTS, doctorDO.getName());
        }

        // 校验自动开方CA权限
        if (CollUtil.isNotEmpty(saveReqVO.getAutoInquiryWayType())) {
            if (!inquirySignatureCaAuthApi.isCaAuthFreeSign(doctorDO.getUserId(), SignaturePlatformEnum.FDD)) {
                throw exception(INQUIRY_DOCTOR_CA_NOT_SET_AUTO);
            }
        }
        List<DoctorAutoInquiryTimerWheelDto> wheels = queryDoctorAutoInquiryTimeWheelDto(Collections.singletonList(saveReqVO.getDoctorPref()));
        getSelf().addRecord(hospitalDeptRelationDO, doctorDO, saveReqVO);
        // 发送自动开方医师出停诊MQ
        doctorAutoInquirySwitchProducer.sendMessage(DoctorAutoInquiryTimerWheelEvent.builder().msg(DoctorAutoInquiryTimerWheelMessage.builder().doctorPref(doctorDO.getPref()).originWheels(wheels).build()).build());
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addRecord(InquiryHospitalDepartmentRelationDO hospitalDeptRelationDO, InquiryDoctorDO doctorDO, InquiryHospitalDeptDoctorSaveReqVO saveReqVO) {
        // 检查医生当前出诊的权限
        checkDoctorOnlineStatus(doctorDO, saveReqVO.getHospitalDeptRelationId(), saveReqVO);
        // 将医生从当前科室移除
        deleteHospitalDeptDoctor(InquiryHospitalDeptDoctorRemoveReqVO.builder().doctorPref(doctorDO.getPref()).hospitalDeptRelationId(hospitalDeptRelationDO.getId()).build());
        // 批量保存
        inquiryHospitalDeptDoctorMapper.insertBatch(InquiryHospitalDepartmentRelationConvert.INSTANCE.convertTODeptDoctorDOList(hospitalDeptRelationDO, doctorDO, saveReqVO));
        // 离线情况下直接返回
        if (ObjectUtil.equals(OnlineStatusEnum.OFFLINE.getCode(), doctorDO.getOnlineStatus())) {
            return;
        }
        // 在线情况下需更新医生可接诊列表
        inquiryDoctorStatusService.updateDoctorCanReceiptInquiryList(doctorDO);
        // 针对当前科室重新出诊
        doctorRedisDao.doctorStartReception(doctorDO, DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode(), hospitalDeptRelationDO.getId());
    }

    private void checkDoctorOnlineStatus(InquiryDoctorDO doctorDO, Long hospitalDeptRelationId, InquiryHospitalDeptDoctorSaveReqVO saveReqVO) {
        // 停诊状态直接返回
        if (ObjectUtil.equals(OnlineStatusEnum.OFFLINE.getCode(), doctorDO.getOnlineStatus())) {
            return;
        }
        // 医生已出诊状态下，判断当前医生是否仅在此医院科室下接诊
        Boolean onlyCurrDept = doctorOnlyCurrDept(doctorDO, hospitalDeptRelationId);
        if (!onlyCurrDept) {
            return;
        }
        // 当前医生仅在此科室下出诊时，判断本次变更是有真人接诊权限
        if (ObjectUtil.isEmpty(saveReqVO) || CollectionUtils.isEmpty(saveReqVO.getInquiryWayType())) {
            throw exception(INQUIRY_DOCTOR_ONLINE_STATUS_ERROR);
        }
    }

    /**
     * 判断当前医生是否仅在此医院科室下接诊
     *
     * @param doctorDO               医生信息
     * @param hospitalDeptRelationId 医院科室id
     * @return
     */
    private Boolean doctorOnlyCurrDept(InquiryDoctorDO doctorDO, Long hospitalDeptRelationId) {
        List<Long> deptIds = inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDepartmentRelationPageReqVO.builder().doctorPref(doctorDO.getPref()).build()).stream().map(InquiryHospitalDeptDoctorDO::getHospitalDeptRelationId)
            .distinct().toList();
        if (CollectionUtils.isEmpty(deptIds)) {
            return Boolean.FALSE;
        }
        return deptIds.size() == 1 && ObjectUtil.equals(hospitalDeptRelationId, deptIds.getFirst());
    }

    /**
     * 启/禁用医院科室
     */
    @Override
    public Boolean deptDisable(InquiryHospitalDeptDisableSaveReqVO saveReqVO) {
        // 根据id查询信息
        InquiryHospitalDepartmentRelationDO hospitalDeptRelationDO = inquiryHospitalDepartmentRelationMapper.selectById(saveReqVO.getId());
        if (ObjectUtils.isEmpty(hospitalDeptRelationDO)) {
            throw exception(INQUIRY_HOSPITAL_DEPARTMENT_RELATION_NOT_EXISTS);
        }
        hospitalDeptRelationDO.setDisabled(saveReqVO.getDisabled());
        return inquiryHospitalDepartmentRelationMapper.updateById(hospitalDeptRelationDO) > 0;
    }

    /**
     * 将医生移出科室
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteHospitalDeptDoctor(InquiryHospitalDeptDoctorRemoveReqVO reqVO) {
        // 获取医生信息
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getPref, reqVO.getDoctorPref());
        // 根据id查询信息
        InquiryHospitalDepartmentRelationDO hospitalDeptRelationDO = inquiryHospitalDepartmentRelationMapper.selectById(reqVO.getHospitalDeptRelationId());
        if (ObjectUtils.isEmpty(hospitalDeptRelationDO)) {
            throw exception(INQUIRY_HOSPITAL_DEPARTMENT_RELATION_NOT_EXISTS);
        }
        // 检查医生当前出诊的权限
        checkDoctorOnlineStatus(doctorDO, hospitalDeptRelationDO.getId(), null);
        // 查询已存在记录
        List<InquiryHospitalDeptDoctorDO> doctorRels = inquiryHospitalDeptDoctorMapper.selectList(
            InquiryHospitalDepartmentRelationPageReqVO.builder().hospitalDeptRelationId(hospitalDeptRelationDO.getId()).doctorPref(reqVO.getDoctorPref()).build());
        // 为空的情况下直接返回
        if (CollectionUtils.isEmpty(doctorRels)) {
            return Boolean.TRUE;
        }
        // 当前医生在线时先停诊
        if (ObjectUtil.equals(OnlineStatusEnum.ONLINE.getCode(), doctorDO.getOnlineStatus())) {
            // 先清除redis
            doctorRedisDao.doctorStopReception(doctorDO, DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode(), hospitalDeptRelationDO.getId());
        }
        inquiryHospitalDeptDoctorMapper.deleteByIds(doctorRels.stream().map(InquiryHospitalDeptDoctorDO::getId).toList());
        return Boolean.TRUE;
    }

    /**
     * 查询医院科室下医生的接诊配置信息
     */
    @Override
    public InquiryHospitalDeptDoctorConfigRespVO getDoctorInquiryConfig(InquiryHospitalDepartmentRelationPageReqVO reqVO) {
        return InquiryHospitalDepartmentRelationConvert.INSTANCE.convertToConfigRespVO(
            inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDepartmentRelationPageReqVO.builder().hospitalDeptRelationId(reqVO.getHospitalDeptRelationId()).doctorPref(reqVO.getDoctorPref()).build()));
    }

    /**
     * 医院关联科室列表查询
     *
     * @param queryVO
     * @return
     */
    @Override
    public List<InquiryHospitalDepartmentRelationDO> selectHospitalDeptmentList(InquiryHospitalDepartmentRelationPageReqVO queryVO) {
        return inquiryHospitalDepartmentRelationMapper.selectHospitalDeptmentList(queryVO);
    }

    @Override
    public String getDoctorHospitalPref(String doctorPref, String hospitalPref) {
        List<InquiryHospitalDeptDoctorDO> doctorDOS = inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDepartmentRelationPageReqVO.builder().doctorPref(doctorPref).hospitalPref(hospitalPref).build());
        if (CollUtil.isEmpty(doctorDOS)) {
            return null;
        }
        return doctorDOS.stream().filter(d -> StringUtils.isNotBlank(d.getDoctorHospitalPref())).findFirst().orElse(new InquiryHospitalDeptDoctorDO()).getDoctorHospitalPref();
    }

    @Override
    public List<String> selectDoctorHospitalList(String doctorPref) {
        return inquiryHospitalDeptDoctorMapper.selectDoctorHospitalList(doctorPref);
    }

    /**
     * 查询医生自动开方时间key - map
     */
    public List<DoctorAutoInquiryTimerWheelDto> queryDoctorAutoInquiryTimeWheelDto(List<String> doctorPrefs) {
        List<DoctorAutoInquiryTimerWheelDto> wheels = new ArrayList<>();
        // 查询医生信息
        List<InquiryDoctorDO> doctorDOList = inquiryDoctorMapper.selectList(InquiryDoctorPageReqVO.builder().doctorPrefs(doctorPrefs).disable(false).build());
        if (CollUtil.isEmpty(doctorDOList)) {
            return wheels;
        }
        Map<String, InquiryDoctorDO> doctorDOMap = doctorDOList.stream().collect(Collectors.toMap(InquiryDoctorDO::getPref, Function.identity(), (a, b) -> b));
        // 查询自动开方医生时间段
        List<InquiryHospitalDeptDoctorDO> hospitalDeptDoctorDOS = inquiryHospitalDeptDoctorMapper.selectList(
            InquiryHospitalDepartmentRelationPageReqVO.builder().doctorPrefs(cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList(doctorDOList, InquiryDoctorDO::getPref))
                .inquiryType(DoctorInquiryTypeEnum.AUTO_INQUIRY.getCode()).build());
        if (CollUtil.isEmpty(hospitalDeptDoctorDOS)) {
            return wheels;
        }

        for (InquiryHospitalDeptDoctorDO deptDoctorDO : hospitalDeptDoctorDOS) {
            InquiryDoctorDO doctorDO = doctorDOMap.get(deptDoctorDO.getDoctorPref());
            String waitPoolKey = RedisKeyConstants.getDoctorWaitPoolKey(deptDoctorDO.getHospitalPref(), deptDoctorDO.getDeptPref(), deptDoctorDO.getInquiryType(), deptDoctorDO.getInquiryWayType(), doctorDO.getEnvTag());

            if (CollUtil.isEmpty(deptDoctorDO.getAutoInquiryTime())) {
                // 全天自动开方,往时间轮存key ,02:00 每天出诊一次
                deptDoctorDO.setAutoInquiryTime(new ArrayList<>() {{
                    add("02:00");
                }});
            }

            // if (CollUtil.isEmpty(deptDoctorDO.getAutoInquiryTime())) {
            //     wheels.add(new DoctorAutoInquiryTimerWheelDto(deptDoctorDO.getDoctorPref(), null,
            //         com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelValue(deptDoctorDO.getDoctorPref(),
            //             com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.DOCTOR_AUTO_INQUIRY_START,
            //             waitPoolKey), null, null));
            // } else {
            deptDoctorDO.getAutoInquiryTime().forEach(time -> {
                try {
                    List<String> timeRange = Arrays.asList(StringUtils.split(time, "|"));
                    if (timeRange.size() >= 2) { // 仅有一个时间是 全天自动开方医生
                        //  停诊时间
                        int e = StringUtils.equals(timeRange.getLast(), "00:00") ? 0 : LocalTime.parse(timeRange.getLast()).getHour() * 60 + LocalTime.parse(timeRange.getLast()).getMinute();
                        wheels.add(new DoctorAutoInquiryTimerWheelDto(deptDoctorDO.getDoctorPref(), com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelKey(e),
                            com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelValue(deptDoctorDO.getDoctorPref(),
                                com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.DOCTOR_AUTO_INQUIRY_END,
                                waitPoolKey), timeRange.getFirst(), timeRange.getLast()));
                    }

                    // 出诊时间
                    int s = LocalTime.parse(timeRange.getFirst()).getHour() * 60 + LocalTime.parse(timeRange.getFirst()).getMinute();
                    DoctorAutoInquiryTimerWheelDto wheelDto = new DoctorAutoInquiryTimerWheelDto(deptDoctorDO.getDoctorPref(), com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelKey(s),
                        com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.getDoctorAutoInquiryTimerWheelValue(deptDoctorDO.getDoctorPref(),
                            com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants.DOCTOR_AUTO_INQUIRY_START,
                            waitPoolKey), timeRange.getFirst(), timeRange.getLast());
                    // 判断是否是全天自动开方医生
                    wheels.add(wheelDto.setAutoAllInquiry(timeRange.size() == 1));

                } catch (Exception e) {

                }
            });
            // }
        }
        return wheels;
    }

    /**
     * 采用先删除后新增的策略，确保数据一致性
     *
     * @param relations 医院科室医生关系列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveHospitalDeptDoctorRelations(List<InquiryHospitalDeptDoctorDto> relations) {
        if (CollUtil.isEmpty(relations)) {
            return;
        }

        // 提取关键字段用于删除条件
        String hospitalPref = relations.getFirst().getHospitalPref();
        String doctorPref = relations.getFirst().getDoctorPref();
        Set<String> deptPrefs = relations.stream()
            .map(InquiryHospitalDeptDoctorDto::getDeptPref)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        // 先删除后新增的策略
        inquiryHospitalDeptDoctorMapper.deleteByHospitalDoctorDept(doctorPref, null, null);

        List<InquiryHospitalDeptDoctorDO> doList = InquiryHospitalDepartmentRelationConvert.INSTANCE.convertDtosToDOs(relations);
        inquiryHospitalDeptDoctorMapper.insertBatch(doList);
    }

    /**
     * 根据医生编码查询医院科室关系列表
     *
     * @param doctorPref 医生编码
     * @return 医院科室医生关系列表
     */
    @Override
    public List<InquiryHospitalDeptDoctorDto> getHospitalDeptDoctorByPref(String doctorPref, String hospitalPref, DoctorTypeEnum doctorTypeEnum) {
        List<InquiryHospitalDeptDoctorDO> doList = inquiryHospitalDeptDoctorMapper.selectList(
            InquiryHospitalDepartmentRelationPageReqVO.builder()
                .doctorPref(doctorPref)
                .hospitalPref(hospitalPref)
                .doctorType(doctorTypeEnum == null ? null : doctorTypeEnum.getCode())
                .build()
        );

        return doList.stream()
            .map(InquiryDoctorConvert.INSTANCE::convertDoToDto)
            .collect(Collectors.toList());
    }

    @Override
    public Map<String, List<InquiryHospitalDeptDoctorRelationRespVO>> getDoctorDeptList(List<String> doctorPrefs) {

        List<InquiryHospitalDeptDoctorDO> doList = inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDepartmentRelationPageReqVO.builder().doctorPrefs(doctorPrefs).build());
        if (CollUtil.isEmpty(doList)) {
            return Map.of();
        }

        List<InquiryHospitalDeptDoctorRelationRespVO> respVOS = InquiryDoctorConvert.INSTANCE.convertDoctorDeptList(doList);

        Map<String, List<InquiryHospitalDeptDoctorRelationRespVO>> resultMap = respVOS.stream()
            .filter(d -> StringUtils.isNotBlank(d.getDeptPref()))
            .collect(Collectors.groupingBy(
                InquiryHospitalDeptDoctorRelationRespVO::getDoctorPref,
                Collectors.collectingAndThen(
                    Collectors.toCollection(ArrayList::new),
                    list -> new ArrayList<>(list.stream()
                        .collect(Collectors.toMap(
                            InquiryHospitalDeptDoctorRelationRespVO::getDeptPref,
                            Function.identity(), (existing, replacement) -> existing)).values()))
            ));
        return resultMap;
    }
}
