package com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 处方记录详情 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_prescription_detail")
// @KeySequence("saas_inquiry_prescription_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryPrescriptionDetailDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 处方编号
     */
    private String prescriptionPref;
    /**
     * 问诊单pref
     */
    private String inquiryPref;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 商品编码
     */
    private String productPref;
    /**
     * 标准库id
     */
    private String standardId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 通用名称
     */
    private String commonName;
    /**
     * 用药方法eg:口服
     */
    private String directions;
    /**
     * 单次剂量 eg:1
     */
    private String singleDose;
    /**
     * 单次剂量单位 eg:片
     */
    private String singleUnit;
    /**
     * 使用频率 eg:一日三次
     */
    private String useFrequency;
    /**
     * 药品类型：0西药，1中药
     */
    private Integer medicineType;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 商品规格
     */
    private String attributeSpecification;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * 产地
     */
    private String producingArea;
    /**
     * 批准文号
     */
    private String approvalNumber;
    /**
     * 包装单位名称
     */
    private String packageUnit;
    /**
     * 商品系统类型
     */
    private Integer productSystemType;
    /**
     * 是否处方药:0否，1是
     */
    private Integer prescriptionYn;
    /**
     * 商品价格
     */
    private BigDecimal productPrice;
    /**
     * 实收金额
     */
    private BigDecimal actualAmount;

}