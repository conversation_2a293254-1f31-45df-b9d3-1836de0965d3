package com.xyy.saas.inquiry.hospital.server.controller.admin.hospital;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDisableSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorConfigRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorGroupVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorRelationRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorRemoveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDetpDoctorRelationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：InquiryHospitalDeptDoctorController
 * @Author: xucao
 * @Date: 2024/11/18 13:14
 * @Description: 医院科室医生关联信息
 */
@Tag(name = "管理后台 - 医院科室医生信息")
@RestController
@RequestMapping("/hospital/inquiry-hospital-dept-doctor")
@Validated
public class InquiryHospitalDeptDoctorController {

    @Resource
    private InquiryHospitalDetpDoctorRelationService inquiryHospitalDetpDoctorRelationService;

    @GetMapping("/page")
    @Operation(summary = "获得医院科室信息分页")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:query')")
    public CommonResult<PageResult<InquiryHospitalDeptDoctorGroupVO>> getInquiryHospitalDeptPage(@Valid InquiryHospitalDepartmentRelationPageReqVO pageReqVO) {
        return success(inquiryHospitalDetpDoctorRelationService.getInquiryHospitalDeptPage(pageReqVO));
    }

    @GetMapping("/getDoctorInquiryConfig")
    @Operation(summary = "获取医院科室医生问诊权限配置")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:query')")
    public CommonResult<InquiryHospitalDeptDoctorConfigRespVO> getDoctorInquiryConfig(@Valid InquiryHospitalDepartmentRelationPageReqVO reqVO) {
        return success(inquiryHospitalDetpDoctorRelationService.getDoctorInquiryConfig(reqVO));
    }

    @GetMapping("/doctorList")
    @Operation(summary = "获得医院科室下医生列表")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:query')")
    public CommonResult<List<InquiryDoctorRespVO>> getInquiryHospitalDeptDoctorList(InquiryHospitalDepartmentRelationPageReqVO reqVO) {
        return success(inquiryHospitalDetpDoctorRelationService.getInquiryHospitalDeptDoctorList(reqVO));
    }

    @PostMapping("/addRecord")
    @Operation(summary = "医院科室添加医生")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:create')")
    public CommonResult<Boolean> addRecord(@Valid @RequestBody InquiryHospitalDeptDoctorSaveReqVO pageReqVO) {
        return success(inquiryHospitalDetpDoctorRelationService.addRecord(pageReqVO));
    }

    @PutMapping("/deptDisable")
    @Operation(summary = "启/禁用医院科室")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:update')")
    public CommonResult<Boolean> deptDisable(@Valid InquiryHospitalDeptDisableSaveReqVO saveReqVO) {
        return success(inquiryHospitalDetpDoctorRelationService.deptDisable(saveReqVO));
    }

    @DeleteMapping("/doctorRemove")
    @Operation(summary = "删除医院科室医生关联信息")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-hospital-department-relation:delete')")
    public CommonResult<Boolean> deleteHospitalDeptDoctor(@Valid InquiryHospitalDeptDoctorRemoveReqVO reqVO) {
        return success(inquiryHospitalDetpDoctorRelationService.deleteHospitalDeptDoctor(reqVO));
    }

    @GetMapping("/doctor-dept-list")
    @Operation(summary = "获得医生所在科室列表")
    public CommonResult<List<InquiryHospitalDeptDoctorRelationRespVO>> getDoctorDeptList(String doctorPref) {
        Map<String, List<InquiryHospitalDeptDoctorRelationRespVO>> deptList = inquiryHospitalDetpDoctorRelationService.getDoctorDeptList(Collections.singletonList(doctorPref));
        return success(deptList.values().stream().flatMap(List::stream).toList());
    }

    @PostMapping("/doctors-dept-list")
    @Operation(summary = "获得医生所在科室列表")
    public CommonResult<Map<String, List<InquiryHospitalDeptDoctorRelationRespVO>>> getDoctorsDeptList(@RequestBody List<String> doctorPrefs) {
        return success(inquiryHospitalDetpDoctorRelationService.getDoctorDeptList(doctorPrefs));
    }
}
