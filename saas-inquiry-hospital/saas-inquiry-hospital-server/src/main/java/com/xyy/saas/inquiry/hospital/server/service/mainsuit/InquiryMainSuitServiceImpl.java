package com.xyy.saas.inquiry.hospital.server.service.mainsuit;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.MAIN_SUIT_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.MAIN_SUIT_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo.InquiryMainSuitPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo.InquiryMainSuitSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.mainsuit.InquiryMainSuitDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.mainsuit.InquiryMainSuitMapper;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 主诉信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryMainSuitServiceImpl implements InquiryMainSuitService {

    @Resource
    private InquiryMainSuitMapper mainSuitMapper;

    @Override
    public Long createMainSuit(InquiryMainSuitSaveReqVO createReqVO) {
        // 校验
        validateMainSuit(null, createReqVO.getMainSuitName());
        // 插入
        InquiryMainSuitDO mainSuit = BeanUtils.toBean(createReqVO, InquiryMainSuitDO.class);
        mainSuitMapper.insert(mainSuit);
        return mainSuit.getId();
    }

    @Override
    public void updateMainSuit(InquiryMainSuitSaveReqVO updateReqVO) {
        // 校验存在
        validateMainSuitExists(updateReqVO.getId());
        validateMainSuit(updateReqVO.getId(), updateReqVO.getMainSuitName());
        // 更新
        InquiryMainSuitDO updateObj = BeanUtils.toBean(updateReqVO, InquiryMainSuitDO.class);
        mainSuitMapper.updateById(updateObj);
    }


    private void validateMainSuit(Long id, String mainSuitName) {
        if (StringUtils.isBlank(mainSuitName)) {
            return;
        }
        InquiryMainSuitDO mainSuitDO = mainSuitMapper.selectOne(InquiryMainSuitDO::getMainSuitName, mainSuitName);
        if (mainSuitDO == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的数据
        if (id == null) {
            throw exception(MAIN_SUIT_EXISTS, mainSuitName);
        }
        if (!Objects.equals(mainSuitDO.getId(), id)) {
            throw exception(MAIN_SUIT_EXISTS, mainSuitName);
        }
    }


    @Override
    public void deleteMainSuit(Long id) {
        // 校验存在
        validateMainSuitExists(id);
        // 删除
        mainSuitMapper.deleteById(id);
    }

    private void validateMainSuitExists(Long id) {
        if (mainSuitMapper.selectById(id) == null) {
            throw exception(MAIN_SUIT_NOT_EXISTS);
        }
    }

    @Override
    public InquiryMainSuitDO getMainSuit(Long id) {
        return mainSuitMapper.selectById(id);
    }

    @Override
    public List<InquiryMainSuitDO> getHotMainSuit() {
        return mainSuitMapper.selectList(new LambdaQueryWrapperX<InquiryMainSuitDO>()
            .eq(InquiryMainSuitDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
            .orderByAsc(InquiryMainSuitDO::getId).last("limit 5"));
    }

    @Override
    public PageResult<InquiryMainSuitDO> getMainSuitPage(InquiryMainSuitPageReqVO pageReqVO) {
        return mainSuitMapper.selectPage(pageReqVO);
    }

}