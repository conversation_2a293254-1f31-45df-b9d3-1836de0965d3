package com.xyy.saas.inquiry.hospital.server.convert.rational;

import com.xyy.saas.inquiry.common.api.rational.dto.InquiryLimitAuditDto;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryLimitAuditReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryRationalDictConfigPageVo;
import com.xyy.saas.inquiry.hospital.server.service.rational.dto.InquiryLimitAuditForwardDto;
import com.xyy.saas.inquiry.hospital.server.service.rational.dto.InquiryRationalDictConfigPageDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * sms Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryRationalConvert {

    InquiryRationalConvert INSTANCE = Mappers.getMapper(InquiryRationalConvert.class);


    @Mapping(target = "medicineType", expression = "java(reqVO.getMedicineType().byteValue())")
    @Mapping(target = "organSign", constant = "1")
    @Mapping(target = "newSystem", constant = "true")
    InquiryLimitAuditForwardDto convertLimitDrugAudit(InquiryLimitAuditReqVO reqVO);


    @Mapping(target = "pageNum", source = "pageNo")
    InquiryRationalDictConfigPageDto convertDictConfigPage(InquiryRationalDictConfigPageVo pageVO);

    @Mapping(target = "medicineType", expression = "java(reqVO.getMedicineType().byteValue())")
    @Mapping(target = "organSign", constant = "1")
    @Mapping(target = "newSystem", constant = "true")
    InquiryLimitAuditDto convertLimitDrugAuditDto(InquiryLimitAuditReqVO reqVO);
}
