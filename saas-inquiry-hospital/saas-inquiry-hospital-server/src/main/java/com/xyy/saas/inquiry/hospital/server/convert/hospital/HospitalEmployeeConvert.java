package com.xyy.saas.inquiry.hospital.server.convert.hospital;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import cn.iocoder.yudao.module.system.api.user.dto.UserPageReqDTO;
import cn.iocoder.yudao.module.system.api.user.dto.UserRespDTO;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalAvailablePageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalEmployeeRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalUserPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalUserSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalAvailableRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.HospitalEmployeeDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 医院员工转换器
 *
 * <AUTHOR>
 */
@Mapper
public interface HospitalEmployeeConvert {

    HospitalEmployeeConvert INSTANCE = Mappers.getMapper(HospitalEmployeeConvert.class);

    /**
     * 转换 UserSaveReqVO 到 AdminUserSaveDTO
     */
    @Mapping(target = "roleCode", ignore = true)
    AdminUserSaveDTO convertVO2DTO(HospitalUserSaveReqVO reqVO);

    default AdminUserSaveDTO convert2UserSaveDTO(HospitalUserSaveReqVO reqVO){
        AdminUserSaveDTO dto = convertVO2DTO(reqVO);
        dto.setTenantId(reqVO.getTenantId());
        return dto;
    }


    /**
     * 创建医院员工关系记录
     */
    default HospitalEmployeeDO createEmployeeRelation(InquiryHospitalRespDto hospital , Long userId) {
        HospitalEmployeeDO hospitalEmployee = new HospitalEmployeeDO();
        hospitalEmployee.setUserId(userId);
        hospitalEmployee.setBindStatus(CommonStatusEnum.ENABLE.getStatus());
        hospitalEmployee.setHospitalPref(hospital.getPref());
        hospitalEmployee.setHospitalName(hospital.getName());
        return hospitalEmployee;
    }

    /**
     * 绑定员工到医院时创建关系记录
     */
    default HospitalEmployeeDO createBindEmployeeRelation(AdminUserRespDTO user, InquiryHospitalRespDto hospital){
        HospitalEmployeeDO hospitalEmployee = new HospitalEmployeeDO();
        hospitalEmployee.setUserId(user.getId());
        hospitalEmployee.setBindStatus(CommonStatusEnum.ENABLE.getStatus());
        hospitalEmployee.setHospitalPref(hospital.getPref());
        hospitalEmployee.setHospitalName(hospital.getName());
        return hospitalEmployee;
    }

    /**
     * 转换用户信息到医院员工响应VO
     */
    default HospitalEmployeeRespVO convertToRespVO(AdminUserRespDTO user, HospitalEmployeeDO hospitalEmployee) {
        HospitalEmployeeRespVO respVO = new HospitalEmployeeRespVO();
        respVO.setId(user.getId());
        respVO.setUsername(user.getMobile());
        respVO.setNickname(user.getNickname());
        respVO.setMobile(user.getMobile());
        respVO.setSex(user.getSex());
        respVO.setStatus(user.getStatus());
        return respVO;
    }

    /**
     * 批量转换用户信息到医院员工响应VO列表
     */
    PageResult<HospitalEmployeeRespVO> convertToRespVOList(PageResult<UserRespDTO> users);

    default UserPageReqDTO convert2UserPageReqDTO(HospitalUserPageReqVO pageReqVO, List<Long> userIds){
        UserPageReqDTO pageReqDTO = new UserPageReqDTO();
        pageReqDTO.setUserIds(userIds);
        pageReqDTO.setTenantId(TenantContextHolder.getTenantId());
        pageReqDTO.setPageNo(pageReqVO.getPageNo());
        pageReqDTO.setPageSize(pageReqVO.getPageSize());
        pageReqDTO.setNickname(pageReqVO.getNickname());
        pageReqDTO.setUsername(pageReqVO.getUsername());
        pageReqDTO.setStatus(pageReqVO.getStatus());
        pageReqDTO.setCreateTime(pageReqVO.getCreateTime());
        return pageReqDTO;
    }

    /**
     * 转换医院员工关系到医院绑定响应VO
     */
    default HospitalBindRespVO convertToBindRespVO(HospitalEmployeeDO hospitalEmployee) {
        HospitalBindRespVO respVO = new HospitalBindRespVO();
        respVO.setId(hospitalEmployee.getId());
        respVO.setHospitalPref(hospitalEmployee.getHospitalPref());
        respVO.setHospitalName(hospitalEmployee.getHospitalName());
        respVO.setBindStatus(hospitalEmployee.getBindStatus());
        respVO.setBindTime(hospitalEmployee.getCreateTime());
        return respVO;
    }

    /**
     * 批量转换医院员工关系到医院绑定响应VO列表
     */
    default List<HospitalBindRespVO> convertToBindRespVOList(List<HospitalEmployeeDO> hospitalEmployees) {
        return hospitalEmployees.stream()
                .map(this::convertToBindRespVO)
                .collect(Collectors.toList());
    }

    /**
     * 分页转换医院员工关系到医院绑定响应VO分页列表
     */
    default PageResult<HospitalBindRespVO> convertToBindRespVOPageList(PageResult<HospitalEmployeeDO> hospitalEmployeePage) {
        List<HospitalBindRespVO> list = convertToBindRespVOList(hospitalEmployeePage.getList());
        return new PageResult<>(list, hospitalEmployeePage.getTotal());
    }


    default InquiryHospitalPageReqVO convertHospitalPageReqVO(HospitalAvailablePageReqVO reqVO ,List<String> unbindHospitalPrefs){
        InquiryHospitalPageReqVO pageReqVO = new InquiryHospitalPageReqVO();
        pageReqVO.setPageNo(reqVO.getPageNo());
        pageReqVO.setPageSize(reqVO.getPageSize());
        pageReqVO.setUnbindHospitalPrefs(unbindHospitalPrefs);
        pageReqVO.setSearchKey(reqVO.getSearchKey());
        return pageReqVO;
    }

    PageResult<HospitalAvailableRespVO> convertHospitalPageRespVO(PageResult<InquiryHospitalRespDto> pageResult);

    default HospitalUserPageReqVO convertHospitalUserPageReqVO(HospitalAvailablePageReqVO pageReqVO){
        HospitalUserPageReqVO reqVO = new HospitalUserPageReqVO();
        reqVO.setPageNo(pageReqVO.getPageNo());
        reqVO.setPageSize(pageReqVO.getPageSize());
        reqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        reqVO.setUserId(pageReqVO.getUserId());
        reqVO.setHospitalName(pageReqVO.getSearchKey());
        return reqVO;
    }

    default HospitalBindReqVO convert2HospitalBindReqVO(InquiryHospitalRespDto hospital, AdminUserRespDTO user){
        HospitalBindReqVO bindReqVO = new HospitalBindReqVO();
        bindReqVO.setHospitalPref(hospital.getPref());
        bindReqVO.setUserId(user.getId());
        return bindReqVO;
    }

    default AdminUserSaveDTO convert2UserUpdateDTO(AdminUserRespDTO user, HospitalUserSaveReqVO createReqVO){
        AdminUserSaveDTO updateDTO = convertRespDTO2UserSaveDTO(user);
        CopyOptions options = CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true);
        BeanUtil.copyProperties(createReqVO, updateDTO, options);
        return updateDTO;
    }

    AdminUserSaveDTO convertRespDTO2UserSaveDTO(AdminUserRespDTO user);
}