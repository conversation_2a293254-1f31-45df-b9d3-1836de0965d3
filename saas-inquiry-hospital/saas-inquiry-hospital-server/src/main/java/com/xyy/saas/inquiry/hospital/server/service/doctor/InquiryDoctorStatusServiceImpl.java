package com.xyy.saas.inquiry.hospital.server.service.doctor;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_NOT_AUDIT_PASS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_NOT_COOPERATING;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_NOT_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_NOT_OFFLINE;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_STATUS_NOT_EXISTS;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_CA_AUTH_NOT_FREE_SIGN;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.CooperationStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.doctor.InquiryDoctorStatusConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorStatusDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryHospitalDeptDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorStatusMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryHospitalDeptDoctorMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalDepartmentRelationMapper;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.hospital.server.util.DateUtil;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryQueryDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;


/**
 * 医生出诊状态关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class InquiryDoctorStatusServiceImpl implements InquiryDoctorStatusService {

    @Resource
    private InquiryDoctorStatusMapper inquiryDoctorStatusMapper;

    @Resource
    private InquiryDoctorMapper inquiryDoctorMapper;

    @Resource
    private InquiryApi inquiryApi;

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Resource
    private InquiryHospitalDeptDoctorMapper inquiryHospitalDeptDoctorMapper;

    @DubboReference
    private InquirySignatureCaAuthApi inquirySignatureCaAuthApi;


    @Override
    public Long createInquiryDoctorStatus(InquiryDoctorStatusSaveReqVO createReqVO) {
        // 插入
        InquiryDoctorStatusDO inquiryDoctorStatus = BeanUtils.toBean(createReqVO, InquiryDoctorStatusDO.class);
        inquiryDoctorStatusMapper.insert(inquiryDoctorStatus);
        // 返回
        return inquiryDoctorStatus.getId();
    }

    @Override
    public void updateInquiryDoctorStatus(InquiryDoctorStatusSaveReqVO updateReqVO) {
        // 更新
        InquiryDoctorStatusDO updateObj = BeanUtils.toBean(updateReqVO, InquiryDoctorStatusDO.class);
        inquiryDoctorStatusMapper.updateById(updateObj);
    }

    @Override
    public void deleteInquiryDoctorStatus(Long id) {
        // 校验存在
        validateInquiryDoctorStatusExists(id);
        // 删除
        inquiryDoctorStatusMapper.deleteById(id);
    }

    private void validateInquiryDoctorStatusExists(Long id) {
        if (inquiryDoctorStatusMapper.selectById(id) == null) {
            throw exception(INQUIRY_DOCTOR_STATUS_NOT_EXISTS);
        }
    }

    @Override
    public InquiryDoctorStatusDO getInquiryDoctorStatus(Long id) {
        return inquiryDoctorStatusMapper.selectById(id);
    }

    @Override
    public PageResult<InquiryDoctorStatusDO> getInquiryDoctorStatusPage(InquiryDoctorStatusPageReqVO pageReqVO) {
        return inquiryDoctorStatusMapper.selectPage(pageReqVO);
    }

    /**
     * 医生出诊
     *
     * @param reqVO 出诊请求参数
     * @return 出诊结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @TraceNode(node = TraceNodeEnum.DOCTOR_RECEIPT_CHANGE)
    public Boolean startReceipt(InquiryDoctorStatusSaveReqVO reqVO) {
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getUserId, reqVO.getUserId());
        // 校验医生信息
        if (doctorDO == null) {
            throw exception(INQUIRY_DOCTOR_NOT_EXISTS);
        }
        // 医生是否审核通过
        if (ObjectUtil.notEqual(doctorDO.getAuditStatus(), AuditStatusEnum.APPROVED.getCode())) {
            throw exception(INQUIRY_DOCTOR_NOT_AUDIT_PASS);
        }
        // 医生是否合作中
        if (ObjectUtil.notEqual(doctorDO.getCooperation(), CooperationStatusEnum.IN_COOPERATION.getCode())) {
            throw exception(INQUIRY_DOCTOR_NOT_COOPERATING);
        }
        // 医生是否已经出诊在线
        if (ObjectUtil.equals(doctorDO.getOnlineStatus(), OnlineStatusEnum.ONLINE.getCode())) {
            throw exception(INQUIRY_DOCTOR_NOT_OFFLINE);
        }
        // 出诊校验CA认证免签
        if (!inquirySignatureCaAuthApi.isCaAuthFreeSign(doctorDO.getUserId(), SignaturePlatformEnum.FDD)) {
            throw exception(INQUIRY_SIGNATURE_CA_AUTH_NOT_FREE_SIGN);
        }
        // 设置出诊状态、时间
        doctorDO.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
        doctorDO.setStartInquiryTime(LocalDateTime.now());
        doctorDO.setAutoGrabStatus(reqVO.getAutoGrabStatus());
        // 批量删除上次出诊信息
        inquiryDoctorStatusMapper.deleteByDoctorPrefType(doctorDO.getPref(), DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode());
        // 批量保存本次出诊信息
        inquiryDoctorStatusMapper.insertBatch(InquiryDoctorStatusConvert.INSTANCE.convertSaveReqVOTODOS(reqVO, doctorDO));
        // 更新医生信息
        inquiryDoctorMapper.updateById(doctorDO);
        // 更新redis缓存
        doctorRedisDao.doctorStartReception(doctorDO, DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode(), null);
        return Boolean.TRUE;
    }

    /**
     * 医生停止接诊
     *
     * @param userId 用户id
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @TraceNode(node = TraceNodeEnum.DOCTOR_RECEIPT_CHANGE)
    public Boolean stopReceipt(Long userId) {
        // 查询医生信息
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getUserId, userId);
        if (doctorDO == null) {
            throw exception(INQUIRY_DOCTOR_NOT_EXISTS);
        }
        // 设置出诊状态、时间
        doctorDO.setOnlineStatus(OnlineStatusEnum.OFFLINE.getCode());
        doctorDO.setEndInquiryTime(LocalDateTime.now());
        // 更新redis
        doctorRedisDao.doctorStopReception(doctorDO, DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode(), null);
        // 查询所有出诊记录，批量更新状态
        Optional.ofNullable(inquiryDoctorStatusMapper.selectList(InquiryDoctorStatusPageReqVO.builder().doctorPref(doctorDO.getPref()).status(OnlineStatusEnum.ONLINE.getCode()).build()))
            .ifPresent(doList -> doList.forEach(doctorStatusDO -> {
                doctorStatusDO.setStatus(OnlineStatusEnum.OFFLINE.getCode());
                inquiryDoctorStatusMapper.updateById(doctorStatusDO);
            }));
        // 更新医生状态
        return inquiryDoctorMapper.updateById(doctorDO) > 0;
    }

    /**
     * 获取医生接诊范围
     *
     * @param userId 用户id
     * @return 接诊范围
     */
    @Override
    public InquiryDoctorStatusRespVO getDoctorReceiptScope(Long userId) {
        // 查询医生信息
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getUserId, userId);
        if (doctorDO == null) {
            throw exception(INQUIRY_DOCTOR_NOT_EXISTS);
        }
        // 查询当前医生真人接诊权限
        List<DictDataRespDTO> inquiryWayTypeDicts = inquiryHospitalDeptDoctorMapper.selectList(
                InquiryHospitalDepartmentRelationPageReqVO.builder().doctorPref(doctorDO.getPref()).inquiryType(DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode()).build()).stream().map(InquiryHospitalDeptDoctorDO::getInquiryWayType).distinct()
            .map(obj -> {
                InquiryWayTypeEnum inquiryWayTypeEnum = InquiryWayTypeEnum.fromCode(obj);
                return DictDataRespDTO.builder().label(inquiryWayTypeEnum.getDescForDoctor()).value(inquiryWayTypeEnum.getCode().toString()).build();
            }).sorted(Comparator.comparing(DictDataRespDTO::getValue)).toList();
        // 当前出诊信息
        List<InquiryDoctorStatusDO> lastReceiptInfo = inquiryDoctorStatusMapper.selectList(
            InquiryDoctorStatusPageReqVO.builder().doctorPref(doctorDO.getPref()).inquiryType(DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode()).status(OnlineStatusEnum.ONLINE.getCode()).build());
        if (CollectionUtils.isEmpty(lastReceiptInfo)) {
            return InquiryDoctorStatusRespVO.builder().userId(doctorDO.getUserId()).autoGrabStatus(doctorDO.getAutoGrabStatus()).onlineStatus(doctorDO.getOnlineStatus()).inquiryWayTypeItems(List.of())
                .inquiryWayTypeDicts(inquiryWayTypeDicts).build();
        }
        return InquiryDoctorStatusConvert.INSTANCE.convertDOSTOVO(lastReceiptInfo, doctorDO, inquiryWayTypeDicts);
    }

    /**
     * 更新医生接诊范围
     *
     * @param reqVO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @TraceNode(node = TraceNodeEnum.DOCTOR_RECEIPT_CHANGE)
    public Boolean updateDoctorReceiptScope(InquiryDoctorStatusSaveReqVO reqVO) {
        // 查询医生信息
        InquiryDoctorDO doctorDO = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getUserId, reqVO.getUserId());
        if (doctorDO == null) {
            throw exception(INQUIRY_DOCTOR_NOT_EXISTS);
        }
        // 先清除redis
        doctorRedisDao.doctorStopReception(doctorDO, DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode(), null);
        // 批量删除当前出诊信息
        inquiryDoctorStatusMapper.deleteByDoctorPrefType(doctorDO.getPref(), DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode());
        // 批量保存修改的信息
        inquiryDoctorStatusMapper.insertBatch(InquiryDoctorStatusConvert.INSTANCE.convertSaveReqVOTODOS(reqVO, doctorDO));
        // 维护更新医生可接诊列表数据
        updateDoctorCanReceiptInquiryList(doctorDO);
        //设置医生自动抢单状态
        doctorDO.setAutoGrabStatus(reqVO.getAutoGrabStatus());
        // 更新医生信息
        inquiryDoctorMapper.updateById(doctorDO);
        // 更新redis缓存
        doctorRedisDao.doctorStartReception(doctorDO, DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode(), null);
        return Boolean.TRUE;
    }

    /**
     * 医生接诊权限变更，需同步更新医生可接诊列表数据
     *
     * @param doctorDO 医生信息
     */
    public void updateDoctorCanReceiptInquiryList(InquiryDoctorDO doctorDO) {
        // 根据当前医生医院配置，更新并过滤医生接诊权限
        List<Integer> canReceiptInquiryWayTypes = getDoctorCanReceiptInquiryWayTypes(doctorDO);
        // 查询当前医生可接问诊单号列表
        List<String> inquiryPrefs = doctorRedisDao.getDoctorCanReceptionList(doctorDO.getPref(), doctorDO.getEnvTag());
        if (CollectionUtils.isEmpty(inquiryPrefs)) {
            // 医生可接诊列表为空，直接返回
            return;
        }
        // 查询医生可接问诊单据列表
        List<InquiryRecordDto> recordDtos = inquiryApi.getInquiryRecordList(InquiryQueryDto.builder().prefs(inquiryPrefs).build());
        // 定义需要移出的问诊单集合
        List<String> removePrefs = new ArrayList<>();
        // 根据当前选择的权限列表，将不可接的问诊单号从可接问诊单号列表中移除
        recordDtos.forEach(recordDto -> {
            // 当前选择的接诊权限不包含当前问诊单的类型，需要将当前问诊从医生可接诊列表移出
            if (!canReceiptInquiryWayTypes.contains(recordDto.getInquiryWayType())) {
                removePrefs.add(recordDto.getPref());
            }
        });
        if (CollectionUtils.isEmpty(removePrefs)) {
            return;
        }
        // 操作redis移出医生可接诊列表
        doctorRedisDao.onDoctorReceiptScopeUpdate(removePrefs, doctorDO.getPref(), doctorDO.getEnvTag());
    }

    private List<Integer> getDoctorCanReceiptInquiryWayTypes(InquiryDoctorDO doctorDO) {
        List<InquiryDoctorStatusDO> newStatusList = new ArrayList<>();
        // 查询当前真人出诊权限列表
        List<InquiryDoctorStatusDO> doctorStatusList = inquiryDoctorStatusMapper.selectList(
                InquiryDoctorStatusPageReqVO.builder().doctorPref(doctorDO.getPref()).inquiryType(DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode()).status(doctorDO.getOnlineStatus()).build());
        // 查询当前真人可接诊权限列表
        List<Integer> inquiryWayTypes = inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDepartmentRelationPageReqVO.builder().doctorPref(doctorDO.getPref()).inquiryType(DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode()).build()).stream().map(InquiryHospitalDeptDoctorDO::getInquiryWayType).distinct().toList();
        // 遍历doctorStatusList ，如果item的inquiryWayType在inquiryWayTypes中，则加入newStatusList
        doctorStatusList.forEach(item -> {
            if (inquiryWayTypes.contains(item.getInquiryWayType())) {
                newStatusList.add(item);
            }
        });
        // 删除所有真人出诊记录
        inquiryDoctorStatusMapper.deleteByDoctorPrefType(doctorDO.getPref(), DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode());
        // 批量写入真人出诊记录
        inquiryDoctorStatusMapper.insertBatch(newStatusList);
        return newStatusList.stream().map(InquiryDoctorStatusDO::getInquiryWayType).distinct().toList();
    }


    @Override
    public void handleAutoInquiryDoctorStatus(InquiryDoctorDO doctor) {
        if (doctor == null) {
            return;
        }
        List<InquiryHospitalDeptDoctorDO> deptDoctorDOS = inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDepartmentRelationPageReqVO.builder()
            .doctorPref(doctor.getPref()).inquiryType(DoctorInquiryTypeEnum.AUTO_INQUIRY.getCode()).build());
        if (CollUtil.isEmpty(deptDoctorDOS)) {
            inquiryDoctorStatusMapper.deleteByDoctorPrefType(doctor.getPref(), DoctorInquiryTypeEnum.AUTO_INQUIRY.getCode());
            return;
        }
        List<InquiryHospitalDeptDoctorDO> onLines = deptDoctorDOS.stream().filter(d -> CollUtil.isEmpty(d.getAutoInquiryTime()) || d.getAutoInquiryTime().stream().anyMatch(t -> {
                List<String> timeRange = Arrays.asList(StringUtils.split(t, "|"));
                LocalTime start = LocalTime.parse(timeRange.getFirst());
                LocalTime end = DateUtil.getEndLocalTime(timeRange.getLast());
                return LocalDateTime.now().toLocalTime().isAfter(start) && LocalDateTime.now().toLocalTime().isBefore(end);
            })).collect(Collectors.toMap(InquiryHospitalDeptDoctorDO::getInquiryWayType, Function.identity(), (a, b) -> a))
            .values().stream().toList();

        log.info("医师自动开方出停诊 :doctor:{},当前状态:{},自动开方是否出诊:{},", doctor.getPref(), doctor.getOnlineStatus(), !onLines.isEmpty());
        inquiryDoctorStatusMapper.deleteByDoctorPrefType(doctor.getPref(), DoctorInquiryTypeEnum.AUTO_INQUIRY.getCode());
        if (CollUtil.isNotEmpty(onLines)) {
            // 记录出诊状态
            inquiryDoctorStatusMapper.insertBatch(InquiryDoctorStatusConvert.INSTANCE.convertDeptRelation(onLines, OnlineStatusEnum.ONLINE));
        }
    }
}