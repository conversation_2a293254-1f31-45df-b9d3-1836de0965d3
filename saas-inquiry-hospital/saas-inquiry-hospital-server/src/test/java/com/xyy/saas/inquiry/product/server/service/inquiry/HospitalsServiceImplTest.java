//package cn.iocoder.yudao.module.saas.service.inquiry;
//
//import com.xyy.saas.inquiry.hospital.server.controller.admin.inquiry.vo.*;
//import com.xyy.saas.inquiry.hospital.server.dal.mysql.inquiry.HospitalsMapper;
//import com.xyy.saas.inquiry.hospital.server.service.hospital.HospitalsServiceImpl;
//import org.junit.jupiter.api.Disabled;
//import org.junit.jupiter.api.Test;
//
//import jakarta.annotation.Resource;
//
//import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
//
//import cn.iocoder.yudao.module.saas.dal.dataobject.inquiry.HospitalsDO;
//import cn.iocoder.yudao.framework.common.pojo.PageResult;
//
//import org.springframework.context.annotation.Import;
//
//import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.*;
//import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.*;
//import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.*;
//import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.*;
//import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.*;
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * {@link HospitalsServiceImpl} 的单元测试类
// *
// * <AUTHOR>
// */
//@Import(HospitalsServiceImpl.class)
//public class HospitalsServiceImplTest extends BaseDbUnitTest {
//
//    @Resource
//    private HospitalsServiceImpl hospitalsService;
//
//    @Resource
//    private HospitalsMapper hospitalsMapper;
//
//    @Test
//    public void testCreateHospitals_success() {
//        // 准备参数
//        HospitalsSaveReqVO createReqVO = randomPojo(HospitalsSaveReqVO.class);
//        createReqVO.setId(null);
//
//        // 调用
//        Integer hospitalsId = hospitalsService.createHospitals(createReqVO);
//        // 断言
//        assertNotNull(hospitalsId);
//        // 校验记录的属性是否正确
//        HospitalsDO hospitals = hospitalsMapper.selectById(hospitalsId);
//        assertPojoEquals(createReqVO, hospitals, "id");
//    }
//
//    @Test
//    public void testUpdateHospitals_success() {
//        // mock 数据
//        HospitalsDO dbHospitals = randomPojo(HospitalsDO.class);
//        hospitalsMapper.insert(dbHospitals);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        HospitalsSaveReqVO updateReqVO = randomPojo(HospitalsSaveReqVO.class, o -> {
//            o.setId(dbHospitals.getId()); // 设置更新的 ID
//        });
//
//        // 调用
//        hospitalsService.updateHospitals(updateReqVO);
//        // 校验是否更新正确
//        HospitalsDO hospitals = hospitalsMapper.selectById(updateReqVO.getId()); // 获取最新的
//        assertPojoEquals(updateReqVO, hospitals);
//    }
//
//    @Test
//    public void testUpdateHospitals_notExists() {
//        // 准备参数
//        HospitalsSaveReqVO updateReqVO = randomPojo(HospitalsSaveReqVO.class);
//
//        // 调用, 并断言异常
//        assertServiceException(() -> hospitalsService.updateHospitals(updateReqVO), HOSPITALS_NOT_EXISTS);
//    }
//
//    @Test
//    public void testDeleteHospitals_success() {
//        // mock 数据
//        HospitalsDO dbHospitals = randomPojo(HospitalsDO.class);
//        hospitalsMapper.insert(dbHospitals);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        Integer id = dbHospitals.getId();
//
//        // 调用
//        hospitalsService.deleteHospitals(id);
//       // 校验数据不存在了
//       assertNull(hospitalsMapper.selectById(id));
//    }
//
//    @Test
//    public void testDeleteHospitals_notExists() {
//        // 准备参数
//        Integer id = randomInteger();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> hospitalsService.deleteHospitals(id), HOSPITALS_NOT_EXISTS);
//    }
//
//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetHospitalsPage() {
//       // mock 数据
//       HospitalsDO dbHospitals = randomPojo(HospitalsDO.class, o -> { // 等会查询到
//           o.setGuid(null);
//           o.setName(null);
//           o.setLevel(null);
//           o.setAddress(null);
//           o.setPhone(null);
//           o.setEmail(null);
//           o.setWebsite(null);
//           o.setHasMedicare(null);
//           o.setDefaultPrescriptionTemplate(null);
//           o.setDisable(null);
//           o.setCreateTime(null);
//       });
//       hospitalsMapper.insert(dbHospitals);
//       // 测试 guid 不匹配
//       hospitalsMapper.insert(cloneIgnoreId(dbHospitals, o -> o.setGuid(null)));
//       // 测试 name 不匹配
//       hospitalsMapper.insert(cloneIgnoreId(dbHospitals, o -> o.setName(null)));
//       // 测试 level 不匹配
//       hospitalsMapper.insert(cloneIgnoreId(dbHospitals, o -> o.setLevel(null)));
//       // 测试 address 不匹配
//       hospitalsMapper.insert(cloneIgnoreId(dbHospitals, o -> o.setAddress(null)));
//       // 测试 phone 不匹配
//       hospitalsMapper.insert(cloneIgnoreId(dbHospitals, o -> o.setPhone(null)));
//       // 测试 email 不匹配
//       hospitalsMapper.insert(cloneIgnoreId(dbHospitals, o -> o.setEmail(null)));
//       // 测试 website 不匹配
//       hospitalsMapper.insert(cloneIgnoreId(dbHospitals, o -> o.setWebsite(null)));
//       // 测试 hasMedicare 不匹配
//       hospitalsMapper.insert(cloneIgnoreId(dbHospitals, o -> o.setHasMedicare(null)));
//       // 测试 defaultPrescriptionTemplate 不匹配
//       hospitalsMapper.insert(cloneIgnoreId(dbHospitals, o -> o.setDefaultPrescriptionTemplate(null)));
//       // 测试 disable 不匹配
//       hospitalsMapper.insert(cloneIgnoreId(dbHospitals, o -> o.setDisable(null)));
//       // 测试 createTime 不匹配
//       hospitalsMapper.insert(cloneIgnoreId(dbHospitals, o -> o.setCreateTime(null)));
//       // 准备参数
//       HospitalsPageReqVO reqVO = new HospitalsPageReqVO();
//       reqVO.setGuid(null);
//       reqVO.setName(null);
//       reqVO.setLevel(null);
//       reqVO.setAddress(null);
//       reqVO.setPhone(null);
//       reqVO.setEmail(null);
//       reqVO.setWebsite(null);
//       reqVO.setHasMedicare(null);
//       reqVO.setDefaultPrescriptionTemplate(null);
//       reqVO.setDisable(null);
//       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//
//       // 调用
//       PageResult<HospitalsDO> pageResult = hospitalsService.getHospitalsPage(reqVO);
//       // 断言
//       assertEquals(1, pageResult.getTotal());
//       assertEquals(1, pageResult.getList().size());
//       assertPojoEquals(dbHospitals, pageResult.getList().getFirst());
//    }
//
//}
