package com.xyy.saas.inquiry.hospital.enums;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * @Desc 门店参数配置类型枚举
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum HospitalSettingTypeEnum implements IntArrayValuable {


    // 100001 -  20000  医院参数配置
    DEFAULT_INQUIRY_WESTERN_MEDICINE_DEPT(10001, "defaultInquiryWesternMedicineDept", "西成药问诊默认科室", "西成药问诊默认科室"),

    DEFAULT_INQUIRY_CHINESE_MEDICINE_DEPT(10002, "defaultInquiryChineseMedicineDept", "中草药问诊默认科室", "中草药问诊默认科室"),

    DEFAULT_WESTERN_PRESCRIPTION_TEMPLATE(10011, "defaultWesternPrescriptionTemplate", "默认处方笺模板（西成药）", "默认处方笺模板（西成药）"),

    DEFAULT_CHINESE_PRESCRIPTION_TEMPLATE(10012, "defaultChinesePrescriptionTemplate", "默认处方笺模板（中草药）", "默认处方笺模板（中草药）"),

    SPECIAL_PRESCRIPTION_TEMPLATES(10013, "specialPrescriptionTemplates", "特定处方笺模板", "特定处方笺模板（条件）"),

    SPECIAL_PRESCRIPTION_CA(10014, "specialPrescriptionCa", "特定处方CA", "特定处方CA（条件）"),

    ;

    private final int type;

    private final String field;

    private final String name;

    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(HospitalSettingTypeEnum::getType).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static HospitalSettingTypeEnum fromType(int type) {
        return Arrays.stream(values())
            .filter(value -> Objects.equals(value.getType(), type))
            .findFirst()
            .orElse(null);
    }

    /**
     * 默认处方笺模板字段 给 mybatis xml 使用 {@link com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalSettingMapper#selectPresTempUsedHospitalIdList(java.util.List)}
     *
     * @return
     */
    public static List<String> defaultPrescriptionTemplateField() {
        return List.of(DEFAULT_WESTERN_PRESCRIPTION_TEMPLATE.field, DEFAULT_CHINESE_PRESCRIPTION_TEMPLATE.field);
    }

    /**
     * 特殊处方笺模板字段 给 mybatis xml 使用 {@link com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalSettingMapper#selectPresTempUsedHospitalIdList(java.util.List)}
     *
     * @return
     */
    public static List<String> specialPrescriptionTemplateField() {
        return List.of(SPECIAL_PRESCRIPTION_TEMPLATES.field);
    }
}