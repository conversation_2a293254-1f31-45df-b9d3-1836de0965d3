package com.xyy.saas.inquiry.hospital.enums;

/**
 * 系统字典类型常量
 * <p>
 * 用途： 1. 定义系统中使用的字典类型 2. 用于数据字典的分类和管理 3. 确保字典类型的统一性
 *
 * <AUTHOR>
 */
public interface DictTypeConstants {

    /**
     * 用户账号状态
     */
    String SYSTEM_USER_ACCOUNT_STATUS = "system_user_account_status";

    /**
     * 签章平台
     */
    String INQUIRY_SIGNATURE_PLATFORM = "inquiry_signature_platform";

    /**
     * 签章实名认证状态
     */
    String INQUIRY_SIGNATURE_CERTIFY_STATUS = "inquiry_signature_certify_status";

    /**
     * 法大大用户认证状态
     */
    String INQUIRY_SIGNATURE_USER_AUTH_STATUS = "inquiry_signature_user_auth_status";

    /**
     * 陕西监管备案状态
     */
    String SHAANXI_REGULATORY_FILLING_STATUS = "shaanxi_regulatory_filling_status";

}
