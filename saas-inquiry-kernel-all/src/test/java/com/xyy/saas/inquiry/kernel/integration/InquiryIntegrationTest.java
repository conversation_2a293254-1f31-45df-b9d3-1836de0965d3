package com.xyy.saas.inquiry.kernel.integration;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.hospital.server.mq.consumer.doctor.DoctorDistributeConsumer;
import com.xyy.saas.inquiry.hospital.server.mq.message.doctor.DoctorDistributeEvent;
import com.xyy.saas.inquiry.hospital.server.mq.message.doctor.dto.DoctorDistributeMessage;
import com.xyy.saas.inquiry.kernel.SaasInquiryKernelAllApplication;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryServiceImpl;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionReceiveVO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.InquiryPrescriptionAuditService;
import jakarta.annotation.Resource;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 问诊集成测试核心类
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/12 11:27
 */
@SpringBootTest(classes = SaasInquiryKernelAllApplication.class)
@ActiveProfiles("dev")
public class InquiryIntegrationTest {

    @Resource
    private InquiryServiceImpl inquiryService;

    @Resource
    private InquiryPrescriptionAuditService inquiryPrescriptionAuditService;

    @Autowired
    private DoctorDistributeConsumer doctorDistributeConsumer;

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 1856634153722658818L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
        TenantContextHolder.setTenantId(1856621116684963842L); // cxy
    }


    @Test
    public void testInquiry() {

        doctorDistributeConsumer.onMessage(DoctorDistributeEvent.builder().msg(DoctorDistributeMessage.builder().inquiryPref("110046").doctorList(List.of("D100001")).build()).build());


    }

    /**
     * 商家-患者发起问诊 - 自动开方
     */

    /**
     * 商家患者发起问诊 - 真人开方
     */

    /**
     * 真人医生抢单接诊
     */

    /**
     * 真人医生开方
     */

    /**
     * 药师待审数量
     */
    @Test
    public void waitReceiveCount_success() {
        CommonResult<Long> commonResult = inquiryPrescriptionAuditService.waitReceiveCount();
        System.out.println(commonResult);
    }

    /**
     * 领一个处方
     */
    @Test
    public void receivePrescription_success() {
        // CommonResult<InquiryPrescriptionReceiveVO> commonResult = inquiryPrescriptionAuditService.receivePrescription();
        // System.out.println(commonResult);
    }

    /**
     * 药师审核处方
     */


}
