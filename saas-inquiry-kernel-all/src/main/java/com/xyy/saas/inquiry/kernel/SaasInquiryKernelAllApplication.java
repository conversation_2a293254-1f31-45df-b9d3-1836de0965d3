package com.xyy.saas.inquiry.kernel;

import cn.iocoder.yudao.module.infra.framework.file.config.YudaoFileAutoConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Import;

/**
 * 启动类
 *
 * <AUTHOR>
 */
@EnableDubbo(scanBasePackages = {"com.xyy.saas.inquiry"})
@EnableDiscoveryClient
//@EnableEventBus
@SpringBootApplication(scanBasePackages = {"com.xyy.saas.inquiry", "com.xyy.common.*"})
@Slf4j
@Import({YudaoFileAutoConfiguration.class})
public class SaasInquiryKernelAllApplication {

    public static void main(String[] args) {
        // 设置skywalking服务名称
        System.setProperty("skywalking.agent.service_name", "inquiry-kernel-all");
        // 设置文件名编码格式，-Dsun.jnu.encoding=UTF-8 设置无效
        System.setProperty("sun.jnu.encoding", "UTF-8");
        // 禁用Elasticsearch健康检查
        System.setProperty("management.health.elasticsearch.enabled", "false");
        SpringApplication.run(SaasInquiryKernelAllApplication.class, args);
        log.info("sun.jnu.encoding:{}", System.getProperty("sun.jnu.encoding"));
    }

}
