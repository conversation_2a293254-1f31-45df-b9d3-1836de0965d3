package com.xyy.saas.inquiry.product.server.service.product;

import cn.hutool.core.map.multi.Table;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.product.ProductSpuCategoryEnum;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductUpdateDto;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductMixedPageQueryVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductPageQueryVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductVo;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO;
import jakarta.annotation.Nonnull;
import org.apache.commons.lang3.StringUtils;
import java.util.List;

/**
 * desc 商品标准库服务
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface ProductStdlibService {

    /**
     * 新增或更新商品标准库（以中台数据覆盖）
     * @param midStdlibIdList
     * @param imageSync       是否同步图片数据
     */
    List<ProductStdlibDO> saveOrUpdateStdlibFromMid(List<Long> midStdlibIdList, boolean imageSync);

    /**
     * 新增或更新商品标准库图片信息（以中台数据覆盖）
     * @param midStdlibIdList
     */
    List<ProductStdlibDO> updateStdlibImageFromMid(List<Long> midStdlibIdList);

    /**
     * 新增或更新商品标准库
     * @param dto
     * @param stdlibUpdate
     */
    ProductStdlibDO saveOrUpdateStdlib(@Nonnull ProductInfoDto dto, boolean stdlibUpdate);

    /**
     * 批量更新商品标准库信息
     * @param vo
     */
    int batchUpdateStdlib(@Nonnull StdlibProductUpdateDto dto);

    /**
     * 查询六要素是否已存在标准库数据
     * @param dto
     * @return
     */
    ProductStdlibDO uniqueQuery(@Nonnull ProductInfoDto dto);

    /**
     * 匹配中台标准库
     * @param tenantId
     * @param dtoList
     * @return
     */
    List<ProductInfoDto> matchProduct2MidStdlib(Long tenantId, List<ProductInfoDto> dtoList);

    /**
     * 提报商品中台
     * @param dto
     * @return
     */
    ProductTransferRecordDO reportProduct2MidStdlib(ProductInfoDto dto);

    /**
     * 商品标准库分页查询（中台 + 自建）
     * @param queryVo
     * @return
     */
    PageResult<StdlibProductVo> getMixedStdlibProductPage(StdlibProductMixedPageQueryVo queryVo);

    /**
     * 商品标准库分页查询（自建）
     * @param queryVo
     * @return
     */
    PageResult<ProductStdlibDto> getSelfStdlibProductPage(StdlibProductPageQueryVo queryVo);

    /**
     * 商品标准库信息
     * @param id
     * @return
     */
    ProductStdlibDto getStdlibProduct(Long id);

    /**
     * 查询自建标准库中去重后的商品通用名列表
     * 
     * @param searchDto 查询条件
     * @param limit 返回条数限制
     * @return 去重后的商品通用名列表
     */
    List<ProductStdlibDto> listDistinctCommonName(StdlibProductSearchDto searchDto, int limit);

    /**
     * 查询自建标准库商品（条形码传0，不根据条形码查询）
     *
     * @param barcode
     * @return 去重后的商品品牌列表
     */
    List<ProductStdlibDto> searchStdlibByBarcode(String barcode);

    /**
     * 查询自建标准库商品
     *
     * @param searchDto 查询条件
     * @param limit 返回条数限制
     * @return 去重后的商品品牌列表
     */
    List<ProductStdlibDto> searchStdlibProductList(StdlibProductSearchDto searchDto, int limit);

    /**
     * 查询自建标准库商品（根据通用名+规则分组去重，组内随机取一条）
     *
     * @param searchDto 查询条件
     * @param limit 返回条数限制
     * @return 去重后的商品品牌列表
     */
    List<ProductStdlibDto> searchStdlibProductListGroupByNameSpec(StdlibProductSearchDto searchDto, int limit);


    /**
     * 通用名+规格去重
     * @param spuCategory
     * @param list
     * @param stdlibDTOTable
     */
    default void distinctGroupByNameSpec(
        String spuCategory,
        List<ProductStdlibDto> list,
        Table<String, String, ProductStdlibDto> stdlibDTOTable) {

        // 当为中药时，需要根据通用名去重, 否则需要根据通用名+规格去重
        boolean isChineseMedicine = ProductSpuCategoryEnum.isChineseMedicine(spuCategory);

        list.forEach(dto -> {
            String key2 = isChineseMedicine ? "" : dto.getSpec();
            if (!stdlibDTOTable.contains(dto.getCommonName(), key2)) {
                stdlibDTOTable.put(dto.getCommonName(), key2, dto);
                return;
            }
            // 覆盖逻辑
            ProductStdlibDto old = stdlibDTOTable.get(dto.getCommonName(), key2);
            // 按照评分降序，中台标准库id降序
            if (dto.usageScore() > old.usageScore() ||
                (dto.usageScore() == old.usageScore() && dto.getMidStdlibId() != null && old.getMidStdlibId() != null
                    && dto.getMidStdlibId() > old.getMidStdlibId())) {
                stdlibDTOTable.put(dto.getCommonName(), key2, dto);
            }
        });
    }





}
