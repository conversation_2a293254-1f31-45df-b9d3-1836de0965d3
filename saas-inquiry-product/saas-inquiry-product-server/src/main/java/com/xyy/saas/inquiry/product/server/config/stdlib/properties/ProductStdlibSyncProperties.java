package com.xyy.saas.inquiry.product.server.config.stdlib.properties;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "product.stdlib.sync")
public class ProductStdlibSyncProperties {

    /**
     * 是否开启中台数据全量同步
     */
    private boolean enableFullSync = false;

    /**
     * 是否开启中台数据MQ同步（暂没用到，mq同步默认开启）
     */
    private boolean enableMqSync = true;

    /**
     * 全量同步间隔
     */
    private long fullSyncInterval = 1000L;
    /**
     * 全量同步中台标准库id范围
     */
    private long fullSyncStartId = 0L;
    /**
     * 全量同步中台标准库id范围
     */
    private long fullSyncEndId = 100000L;
    /**
     * 全量同步中台标准库id范围
     */
    private int fullSyncIdBatchSize = 500;

}
