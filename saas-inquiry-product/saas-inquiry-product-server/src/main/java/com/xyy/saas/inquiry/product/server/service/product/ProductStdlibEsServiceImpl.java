package com.xyy.saas.inquiry.product.server.service.product;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.multi.RowKeyTable;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery.Builder;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import com.xyy.saas.binlog.core.es.EsQueryBuilder;
import com.xyy.saas.binlog.core.es.EsQueryService;
import com.xyy.saas.binlog.core.es.EsSearchRequest;
import com.xyy.saas.binlog.core.es.EsSearchResponse;
import com.xyy.saas.binlog.core.es.EsSearchResponse.EsHit;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductUpdateDto;
import com.xyy.saas.inquiry.product.enums.ProductStdlibStatusEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductMixedPageQueryVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductPageQueryVo;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.StdlibProductVo;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO;
import com.xyy.saas.inquiry.product.server.service.catalog.RegulatoryCatalogDetailService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;

/**
 * 商品标准库服务 - Elasticsearch实现
 * 
 * 查询操作使用ES，修改和中台交互操作委托给MySQL实现
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public class ProductStdlibEsServiceImpl implements ProductStdlibService {

    @Resource
    private EsQueryService esQueryService;

    @Resource
    private ProductStdlibService stdlibService;
    @Resource
    private RegulatoryCatalogDetailService regulatoryCatalogDetailService;

    // ES索引名
    private static final String ES_INDEX = "saas_product_stdlib_index";

    @Override
    public List<ProductStdlibDO> saveOrUpdateStdlibFromMid(List<Long> midStdlibIdList, boolean imageSync) {
        log.debug("[saveOrUpdateStdlibFromMid]委托给MySQL实现处理");
        return stdlibService.saveOrUpdateStdlibFromMid(midStdlibIdList, imageSync);
    }

    @Override
    public List<ProductStdlibDO> updateStdlibImageFromMid(List<Long> midStdlibIdList) {
        log.debug("[updateStdlibImageFromMid]委托给MySQL实现处理");
        return stdlibService.updateStdlibImageFromMid(midStdlibIdList);
    }

    @Override
    public ProductStdlibDO saveOrUpdateStdlib(@Nonnull ProductInfoDto dto, boolean stdlibUpdate) {
        log.debug("[saveOrUpdateStdlib]委托给MySQL实现处理");
        return stdlibService.saveOrUpdateStdlib(dto, stdlibUpdate);
    }

    @Override
    public int batchUpdateStdlib(@Nonnull StdlibProductUpdateDto dto) {
        log.debug("[batchUpdateStdlib]委托给MySQL实现处理");
        return stdlibService.batchUpdateStdlib(dto);
    }

    @Override
    public ProductStdlibDO uniqueQuery(@Nonnull ProductInfoDto dto) {
        try {
            // 克隆对象（不影响原对象）
            ProductStdlibDO stdlib = BeanUtils.toBean(dto, ProductStdlibDO.class);
            // 计算要素字段hash值
            stdlib.calcMnemonicCodeAndKeyPointHash();

            BoolQuery.Builder boolQuery = new BoolQuery.Builder();

            // 对应MySQL的六要素精确匹配逻辑
            // 注意：ES中字段名可能与MySQL不同，需要根据实际索引结构调整
            boolQuery.filter(TermQuery.of(t -> t.field("keyPointHash").value(stdlib.getKeyPointHash()))._toQuery())
                .filter(TermQuery.of(t -> t.field("commonName").value(StringUtils.defaultIfBlank(dto.getCommonName(), "")))._toQuery())
                .filter(TermQuery.of(t -> t.field("brandName").value(StringUtils.defaultIfBlank(dto.getBrandName(), "")))._toQuery())
                .filter(TermQuery.of(t -> t.field("spec").value(StringUtils.defaultIfBlank(dto.getSpec(), "")))._toQuery())
                .filter(TermQuery.of(t -> t.field("barcode").value(StringUtils.defaultIfBlank(dto.getBarcode(), "")))._toQuery())
                .filter(TermQuery.of(t -> t.field("approvalNumber").value(StringUtils.defaultIfBlank(dto.getApprovalNumber(), "")))._toQuery())
                .filter(TermQuery.of(t -> t.field("manufacturer").value(StringUtils.defaultIfBlank(dto.getManufacturer(), "")))._toQuery());

            EsSearchRequest searchRequest = EsSearchRequest.builder()
                .index(ES_INDEX)
                .query(boolQuery.build()._toQuery())
                .size(1024)
                .build();

            EsSearchResponse<ProductStdlibDO> response = esQueryService.search(searchRequest, ProductStdlibDO.class);
            
            if (response != null && CollUtil.isNotEmpty(response.getHits())) {
                List<ProductStdlibDO> hits = response.getHits().stream()
                    .map(EsHit::getSource)
                    .toList();
                
                // 优先返回使用中的，如果没有则取第一条返回（对应MySQL逻辑）
                return hits.stream()
                    .filter(item -> Objects.equals(item.getStatus(), ProductStdlibStatusEnum.USING.code))
                    .findFirst()
                    .orElseGet(() -> CollUtil.isEmpty(hits) ? null : hits.getFirst());
            }
            
            return null;
        } catch (Exception e) {
            log.error("[uniqueQuery]ES查询异常", e);
            return null;
        }
    }

    @Override
    public List<ProductInfoDto> matchProduct2MidStdlib(Long tenantId, List<ProductInfoDto> dtoList) {
        log.debug("[matchProduct2MidStdlib]委托给MySQL实现处理");
        return stdlibService.matchProduct2MidStdlib(tenantId, dtoList);
    }

    @Override
    public ProductTransferRecordDO reportProduct2MidStdlib(ProductInfoDto dto) {
        log.debug("[reportProduct2MidStdlib]委托给MySQL实现处理");
        return stdlibService.reportProduct2MidStdlib(dto);
    }

    @Override
    public PageResult<StdlibProductVo> getMixedStdlibProductPage(StdlibProductMixedPageQueryVo queryVo) {
        log.debug("[getMixedStdlibProductPage]混合查询委托给MySQL实现处理");
        return stdlibService.getMixedStdlibProductPage(queryVo);
    }

    @Override
    public PageResult<ProductStdlibDto> getSelfStdlibProductPage(StdlibProductPageQueryVo queryVo) {
        try {
            Builder boolQuery = buildBaseQuery(queryVo);

            EsSearchRequest searchRequest = EsSearchRequest.builder()
                .index(ES_INDEX)
                .query(boolQuery.build()._toQuery())
                .from(queryVo.getPageNo() != null ? (queryVo.getPageNo() - 1) * queryVo.getPageSize() : 0)
                .size(queryVo.getPageSize() != null ? queryVo.getPageSize() : 10)
                .build();

            EsSearchResponse<ProductStdlibDto> response = esQueryService.search(searchRequest, ProductStdlibDto.class);
            
            if (response == null) {
                return PageResult.empty();
            }

            List<ProductStdlibDto> list = response.getHits() != null ? 
                response.getHits().stream().map(EsHit::getSource).toList() :
                Collections.emptyList();
            long total = response.getTotalHits() != null ? response.getTotalHits().getValue() : 0L;
            
            return new PageResult<>(list, total);
        } catch (Exception e) {
            log.error("[getSelfStdlibProductPage]ES查询异常", e);
            return PageResult.empty();
        }
    }

    @Override
    public ProductStdlibDto getStdlibProduct(Long id) {
        if (id == null) {
            return null;
        }
        try {
            EsSearchRequest searchRequest = EsSearchRequest.builder()
                .index(ES_INDEX)
                .query(IdsQuery.of(t -> t.values(id.toString()))._toQuery() )
                .size(1)
                .build();

            EsSearchResponse<ProductStdlibDto> response = esQueryService.search(searchRequest, ProductStdlibDto.class);
            
            if (response != null && CollUtil.isNotEmpty(response.getHits())) {
                return response.getHits().getFirst().getSource();
            }
        } catch (Exception e) {
            log.error("[getStdlibProduct]ES查询异常, id: {}", id, e);
        }
        return null;
    }

    @Override
    public List<ProductStdlibDto> listDistinctCommonName(StdlibProductSearchDto searchDto, int limit) {
        try {
            Integer finalLimit = Math.max(1, limit);
            // 1. 组装默认属性
            searchDto.assembleAttrDefault();
            if (searchDto.barcodeIsZero()) {
                return List.of();
            }

            // 构建查询条件
            Query boolQuery = buildBaseQuery(searchDto).build()._toQuery();

            Map<String, Aggregation> mainAggregations = getCommonNameAggregation(finalLimit);

            // 注意：由于API限制，我们需要在查询后手动拼接midStdlibId
            // 这个实现将使用两步查询：1.聚合获取commonName 2.为每个commonName查询详细数据

            EsSearchRequest searchRequest = EsSearchRequest.builder()
                .index(ES_INDEX)
                .query(boolQuery)
                .size(0) // 不需要返回文档，只需要聚合结果
                .aggregations(mainAggregations)
                .build();

            EsSearchResponse<ProductStdlibDto> response = esQueryService.search(searchRequest, ProductStdlibDto.class);
            
            if (response == null || response.getAggregations() == null) {
                return Collections.emptyList();
            }

            Map<ProductStdlibDto, List<Long>> commonNameAggMap = getCommonNameAggregationResult(response);

            // 5. 监管目录id过滤
            return filterByInternetRegulatoryCatalogAndSortByCommonNameLengthWithAgg(searchDto, commonNameAggMap);
            
        } catch (Exception e) {
            log.error("[listDistinctCommonName]ES聚合查询异常", e);
            return Collections.emptyList();
        }
    }



    @NotNull
    private static Map<String, Aggregation> getCommonNameAggregation(Integer finalLimit) {
        // 先构建主聚合，然后添加子聚合
        Map<String, Aggregation> mainAggregations = new HashMap<>();
        // 使用 Builder 模式来添加子聚合
        mainAggregations.put("group_by_commonName", Aggregation.of(a -> a
            .terms(TermsAggregation.of(t -> t
                .field("commonName.keyword")
                .size(finalLimit)
            ))
            .aggregations("midStdlibId_values", TermsAggregation.of(t -> t
                .field("midStdlibId")
                .size(10000))._toAggregation())
            .aggregations("top_hit", TopHitsAggregation.of(t -> t
                .size(1)
                .sort(SortOptions.of(s -> s.field(f -> f.field("id").order(SortOrder.Desc))))
            )._toAggregation())
        ));
        return mainAggregations;
    }

    @NotNull
    private static Map<ProductStdlibDto, List<Long>> getCommonNameAggregationResult(EsSearchResponse<ProductStdlibDto> response) {
        // 3. 处理聚合结果，提取commonName和对应的midStdlibIds
        Map<ProductStdlibDto, List<Long>> commonNameAggMap = new HashMap<>();

        // 获取主聚合结果
        Aggregate groupByCommonNameAgg = response.getAggregations().get("group_by_commonName");
        if (groupByCommonNameAgg != null && groupByCommonNameAgg._get() != null) {
            try {
                // 解析 terms 聚合结果
                StringTermsAggregate termsAggregate = (StringTermsAggregate) groupByCommonNameAgg._get();

                for (StringTermsBucket bucket : termsAggregate.buckets().array()) {
                    String commonName = bucket.key().stringValue();

                    // 从 top_hit 获取基础数据
                    Aggregate topHitAgg = bucket.aggregations().get("top_hit");
                    ProductStdlibDto baseDto = null;
                    if (topHitAgg != null) {
                        TopHitsAggregate topHits = (TopHitsAggregate) topHitAgg._get();
                        if (!topHits.hits().hits().isEmpty() && topHits.hits().hits().getFirst().source() != null) {
                            baseDto = topHits.hits().hits().getFirst().source().to(ProductStdlibDto.class);
                        }
                    }

                    if (baseDto == null) {
                        log.warn("无法从top_hit获取基础数据，commonName: {}", commonName);
                        continue;
                    }

                    // 从 id_values 获取所有 midStdlibId
                    Aggregate idValuesAgg = bucket.aggregations().get("midStdlibId_values");
                    List<Long> midStdlibIds = new ArrayList<>();
                    if (idValuesAgg != null) {
                        LongTermsAggregate idValuesTerms = (LongTermsAggregate) idValuesAgg._get();
                        midStdlibIds = idValuesTerms.buckets().array().stream()
                            .map(LongTermsBucket::key)
                            .collect(Collectors.toList());
                    }

                    commonNameAggMap.put(baseDto, midStdlibIds);
                }

            } catch (Exception e) {
                log.warn("处理group_by_common_name聚合结果失败", e);
            }
        }
        return commonNameAggMap;
    }


    /**
     * 根据监管目录id过滤（支持映射关系的版本）
     * @param searchDto 搜索条件
     * @param commonNameAggMap 聚合结果
     * @return 过滤后的ProductStdlibDto集合
     */
    private List<ProductStdlibDto> filterByInternetRegulatoryCatalogAndSortByCommonNameLengthWithAgg(
            StdlibProductSearchDto searchDto, Map<ProductStdlibDto, List<Long>> commonNameAggMap) {
        
        return Optional.ofNullable(searchDto.getInternetRegulatoryCatalogId()).map(catalogId -> {
            // 获取所有 midStdlibId
            List<Long> midStdlibIdList = commonNameAggMap.values().stream().flatMap(List::stream).toList();
            if (CollUtil.isEmpty(midStdlibIdList)) {
                return Collections.<ProductStdlibDto>emptyList();
            }

            // 通过监管目录过滤，获取允许的 midStdlibId 集合
            Set<Long> projectCodeSet = regulatoryCatalogDetailService.getRegulatoryCatalogDetailListByProjectCodes(catalogId, midStdlibIdList)
                .stream().map(RegulatoryCatalogDetailDO::getProjectCode).collect(Collectors.toSet());

            return commonNameAggMap.entrySet().stream().map(agg -> {
                // 只要存在一个允许的 midStdlibId 就返回
                if (agg.getValue().stream().anyMatch(projectCodeSet::contains)) {
                    return agg.getKey();
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
                
        }).orElse(new ArrayList<>(commonNameAggMap.keySet()))
            .stream().sorted(Comparator.comparingInt(i -> i.getCommonName() == null ? 0 : i.getCommonName().length())).toList();
    }

    /**
     * 根据监管目录id过滤
     * @param searchDto
     * @param currentPage
     * @return
     */
    private List<ProductStdlibDto> filterByInternetRegulatoryCatalog(StdlibProductSearchDto searchDto, List<ProductStdlibDto> currentPage) {
        return Optional.ofNullable(searchDto.getInternetRegulatoryCatalogId()).map(catalogId -> {
            List<Long> midStdlibIdList = currentPage.stream().map(ProductStdlibDto::getMidStdlibId).filter(Objects::nonNull).toList();
            if (CollUtil.isEmpty(midStdlibIdList)) {
                return new ArrayList<ProductStdlibDto>();
            }

            Set<Long> projectCodeSet = regulatoryCatalogDetailService.getRegulatoryCatalogDetailListByProjectCodes(catalogId, midStdlibIdList)
                .stream().map(RegulatoryCatalogDetailDO::getProjectCode).collect(Collectors.toSet());

            return currentPage.stream().filter(dto -> projectCodeSet.contains(dto.getMidStdlibId())).toList();
        }).orElse(currentPage);
    }

    @Override
    public List<ProductStdlibDto> searchStdlibByBarcode(String barcode) {
        try {
            if (StrUtil.isBlank(barcode) || "0".equals(barcode)) {
                return Collections.emptyList();
            }

            return searchStdlibProductList(new StdlibProductSearchDto().setBarcode(barcode), 1);
        } catch (Exception e) {
            log.error("[searchStdlibByBarcode]ES查询异常, barcode: {}", barcode, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<ProductStdlibDto> searchStdlibProductList(StdlibProductSearchDto searchDto, int limit) {
        try {
            limit = Math.max(1, limit);
            // 1. 组装默认属性
            searchDto.assembleAttrDefault();
            if (searchDto.barcodeIsZero()) {
                return List.of();
            }

            // 构建查询条件
            Query boolQuery = buildBaseQuery(searchDto).build()._toQuery();

            EsSearchRequest searchRequest = EsSearchRequest.builder()
                .index(ES_INDEX)
                .query(boolQuery)
                .size(limit)
                .build();

            EsSearchResponse<ProductStdlibDto> response = esQueryService.search(searchRequest, ProductStdlibDto.class);

            if (response != null && CollUtil.isNotEmpty(response.getHits())) {
                List<ProductStdlibDto> currentPage = response.getHits().stream()
                    .map(EsHit::getSource).toList();
                // 监管目录id过滤
                return filterByInternetRegulatoryCatalog(searchDto, currentPage);
            }

        } catch (Exception e) {
            log.error("[searchStdlibProductList]ES查询异常", e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<ProductStdlibDto> searchStdlibProductListGroupByNameSpec(StdlibProductSearchDto searchDto, int limit) {
        try {
            limit = Math.max(1, limit);
            // 1. 组装默认属性
            searchDto.assembleAttrDefault();
            if (searchDto.barcodeIsZero()) {
                return List.of();
            }

            // 2. 循环查询并手动分组 - 对应 MySQL 版本的 RowKeyTable 逻辑
            RowKeyTable<String, String, ProductStdlibDto> stdlibDTOTable = new RowKeyTable<>(true);

            // 构建查询条件
            Query boolQuery = buildBaseQuery(searchDto).build()._toQuery();

            for (int i = 1; i < 10; i++) {
                EsSearchRequest searchRequest = EsSearchRequest.builder()
                    .index(ES_INDEX)
                    .query(boolQuery)
                    .from((i - 1) * limit)
                    .size(limit)
                    .build();

                EsSearchResponse<ProductStdlibDto> response = esQueryService.search(searchRequest, ProductStdlibDto.class);
                
                if (response == null || CollUtil.isEmpty(response.getHits())) {
                    break;
                }

                List<ProductStdlibDto> currentPage = response.getHits().stream()
                    .map(EsHit::getSource).toList();
                    
                if (CollUtil.isEmpty(currentPage)) {
                    break;
                }

                // 监管目录id过滤
                List<ProductStdlibDto> filterByInternetRegulatoryCatalog = filterByInternetRegulatoryCatalog(searchDto, currentPage);

                // 当为中药时，需要根据通用名去重, 否则需要根据通用名+规格去重
                distinctGroupByNameSpec(searchDto.getSpuCategory(), filterByInternetRegulatoryCatalog, stdlibDTOTable);

                // 如果分组数量已经满足
                if (stdlibDTOTable.size() >= limit || currentPage.size() < limit) {
                    break;
                }
            }
            
            // table 和数据库分页查询的顺序一致
            return stdlibDTOTable.values().stream().sorted(ProductStdlibDto.comparatorNameSpec).toList();
        } catch (Exception e) {
            log.error("[searchStdlibProductListGroupByNameSpec]ES查询异常", e);
            return Collections.emptyList();
        }
    }






    // region ES查询组装参数
    /**
     * 构建基础查询条件
     */
    private BoolQuery.Builder buildBaseQuery(StdlibProductSearchDto searchDto) {
        BoolQuery.Builder boolQuery = new BoolQuery.Builder();

        // 基础状态和标志过滤
        EsQueryBuilder.termQueryOpt("status", searchDto.getStatus()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("disable", searchDto.getDisable()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("deleted", false).ifPresent(boolQuery::filter);

        // 中台商品过滤
        if (searchDto.getIsMidStdlib() != null) {
            Query isMidStdlibQuery = EsQueryBuilder.rangeQuery("midStdlibId").gt(0).build();
            if (searchDto.getIsMidStdlib()) {
                boolQuery.must(isMidStdlibQuery);
            } else {
                boolQuery.mustNot(isMidStdlibQuery);
            }
        }

        // 属性标志过滤
        if (searchDto.getMultiFlag() != null && searchDto.getMultiFlag().getMidDeactivated() != null) {
            // 过滤中台未停用商品 - 这里需要根据实际ES字段结构调整
            EsQueryBuilder.termQueryOpt("multiFlag.midDeactivated", searchDto.getMultiFlag().getMidDeactivated()).ifPresent(boolQuery::filter);
        }

        // 商品大类过滤
        EsQueryBuilder.termQueryOpt("spuCategory.keyword", searchDto.getSpuCategory()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("presCategory.keyword", searchDto.getPresCategory()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("commonName.keyword", searchDto.getCommonName()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("spec.keyword", searchDto.getSpec()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("barcode.keyword", searchDto.getBarcode()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("manufacturer.keyword", searchDto.getManufacturer()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("approvalNumber.keyword", searchDto.getApprovalNumber()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("midStdlibId", searchDto.getMidStdlibId()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("minPackageNum", searchDto.getMinPackageNum()).ifPresent(boolQuery::filter);

        // 模糊匹配
        EsQueryBuilder.multiFieldMatchQueryOpt(new String[]{"commonName"}, searchDto.getCommonNameLike(), true).ifPresent(boolQuery::must);
        EsQueryBuilder.multiFieldMatchQueryOpt(new String[]{"spec"}, searchDto.getSpecLike(), true).ifPresent(boolQuery::must);
        EsQueryBuilder.wildcardQueryOpt("spec.keyword", searchDto.getSpecWildLike()).ifPresent(boolQuery::must);
        EsQueryBuilder.multiFieldMatchQueryOpt(new String[]{"manufacturer"}, searchDto.getManufacturerLike(), true).ifPresent(boolQuery::must);
        EsQueryBuilder.multiFieldMatchQueryOpt(new String[]{"approvalNumber"}, searchDto.getApprovalNumberLike(), true).ifPresent(boolQuery::must);

        // 分类过滤
        EsQueryBuilder.termQueryOpt("firstCategory.keyword", searchDto.getFirstCategory()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("secondCategory.keyword", searchDto.getSecondCategory()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("thirdCategory.keyword", searchDto.getThirdCategory()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("fourthCategory.keyword", searchDto.getFourthCategory()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("fiveCategory.keyword", searchDto.getFiveCategory()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("sixCategory.keyword", searchDto.getSixCategory()).ifPresent(boolQuery::filter);

        // 集合条件过滤
        EsQueryBuilder.termQueryOpt("id", searchDto.getIdList()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("commonName.keyword", searchDto.getCommonNameList()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("barcode.keyword", searchDto.getBarcodeList()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("approvalNumber.keyword", searchDto.getApprovalNumberList()).ifPresent(boolQuery::filter);
        EsQueryBuilder.termQueryOpt("midStdlibId", searchDto.getMidStdlibIdList()).ifPresent(boolQuery::filter);

        // 混合字段查询
        assembleMixedQuery(searchDto.getMixedQuery(), FULL_SEARCH_FIELDS).ifPresent(boolQuery::must);
        assembleMixedQuery(searchDto.getNatureQuery(), FULL_SEARCH_FIELDS).ifPresent(boolQuery::must);
        assembleMixedQuery(searchDto.getMixedNameQuery(), NAME_SEARCH_FIELDS).ifPresent(boolQuery::must);
        assembleMixedQuery(searchDto.getNatureNameQuery(), NAME_SEARCH_FIELDS).ifPresent(boolQuery::must);

        // 经营范围过滤
        if (CollectionUtils.isNotEmpty(searchDto.getNeedExcludeBusinessScopeList())) {
            EsQueryBuilder.termQueryOpt("businessScope.array", searchDto.getNeedExcludeBusinessScopeList()).ifPresent(boolQuery::mustNot);
        }

        return boolQuery;
    }


    // 搜索字段常量(助记码为字母，单独wildcard搜索)
    private static final List<String> FULL_SEARCH_FIELDS = List.of("commonName", "brandName", "barcode", "approvalNumber", "manufacturer");
    private static final List<String> NAME_SEARCH_FIELDS = List.of("commonName", "brandName");
    private static final String PINYIN_SEARCH_FIELD = "mnemonicCode";
    private Optional<Query> assembleMixedQuery(String query, List<String> fields) {
        if (StringUtils.isBlank(query) || CollectionUtils.isEmpty(fields)) {
            return Optional.empty();
        }

        // 拼音助记码搜索
        Optional<Query> pinyinQuery = StringUtils.isAlpha(query) ? Optional.of(EsQueryBuilder.wildcardQuery(PINYIN_SEARCH_FIELD, "*%s*".formatted(query))) : Optional.empty();

        return Optional.of(EsQueryBuilder.orQuery(
            EsQueryBuilder.multiMatchPhraseQuery(fields, query),
            pinyinQuery.stream().toArray(Query[]::new)
        ));
    }
    // endregion

}