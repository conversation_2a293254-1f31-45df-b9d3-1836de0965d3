package com.xyy.saas.inquiry.product.api.product.dto;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.product.enums.ProductStdlibStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;


@Schema(description = "管理后台 - 标准库商品信息 分页查询请求 VO")
@Data
public class StdlibProductSearchDto extends PageParam implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "自建标准库ID")
    private List<Long> idList;

    @Schema(description = "商品大类")
    private String spuCategory;

    @Schema(description = "商品信息（模糊匹配）")
    private String mixedQuery;
    @Schema(description = "商品信息（自然搜索）")
    private String natureQuery;

    @Schema(description = "商品名称（模糊匹配）")
    private String mixedNameQuery;
    @Schema(description = "商品名称（自然搜索）")
    private String natureNameQuery;

    @Schema(description = "处方分类")
    private String presCategory;

    @Schema(description = "通用名")
    private String commonName;
    @Schema(description = "通用名（模糊匹配）")
    private String commonNameLike;
    @Schema(description = "商品通用名集合")
    private List<String> commonNameList;

    @Schema(description = "规格型号")
    private String spec;
    @Schema(description = "规格型号（match短语匹配）")
    private String specLike;
    @Schema(description = "规格型号（Wild模糊匹配）")
    private String specWildLike;

    @Schema(description = "条形码")
    private String barcode;
    @Schema(description = "条形码集合")
    private List<String> barcodeList;
    @Schema(description = "生产厂家")
    private String manufacturer;
    @Schema(description = "生产厂家（模糊匹配）")
    private String manufacturerLike;
    @Schema(description = "批准文号")
    private String approvalNumber;
    @Schema(description = "批准文号（模糊匹配）")
    private String approvalNumberLike;
    @Schema(description = "批准文号集合")
    private List<String> approvalNumberList;

    @Schema(description = "中台标准库ID")
    private String midStdlibId;
    @Schema(description = "中台标准库ID")
    private List<Long> midStdlibIdList;

    @Schema(description = "最小包装数量")
    private BigDecimal minPackageNum;

    @Schema(description = "一级分类")
    private String firstCategory;
    @Schema(description = "二级分类")
    private String secondCategory;
    @Schema(description = "三级分类")
    private String thirdCategory;
    @Schema(description = "四级分类")
    private String fourthCategory;
    @Schema(description = "五级分类")
    private String fiveCategory;
    @Schema(description = "六级分类")
    private String sixCategory;

    @Schema(description = "属性标志")
    private ProductFlag multiFlag;
    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "停用状态")
    private Boolean disable;

    @Schema(description = "是否关联中台标准库")
    private Boolean isMidStdlib;


    @Schema(description = "互联网监管目录")
    private Long internetRegulatoryCatalogId;

    @Schema(description = "需要排除的所属范围")
    private List<String> needExcludeBusinessScopeList;



    /**
     * 组装默认属性
     */
    public void assembleAttrDefault() {
        // 1. 设置默认状态为使用中
        if (this.getStatus() == null) {
            this.setStatus(ProductStdlibStatusEnum.USING.code);
        }

        // 2. 设置过滤条件 - 中台未停用商品
        ProductFlag multiFlag = Optional.ofNullable(this.getMultiFlag()).orElseGet(ProductFlag::new);
        if (multiFlag.getMidDeactivated() == null) {
            this.setMultiFlag(multiFlag.setMidDeactivated(false));
        }
    }

    /**
     * 判断条形码是否为零
     * @return
     */
    public boolean barcodeIsZero() {
        // 条形码为0，不会有数据
        if (this.getBarcode() != null && "0".equals(this.getBarcode())) {
            return true;
        }
        if (CollectionUtils.isNotEmpty(this.getBarcodeList())) {
            // 删除条形码为0的条形码
            this.getBarcodeList().removeIf("0"::equals);

            return CollectionUtils.isEmpty(this.getBarcodeList());
        }
        return false;
    }
}
